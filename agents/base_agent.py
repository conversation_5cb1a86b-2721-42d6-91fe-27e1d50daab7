"""
Base Agent class for the Agent-Based Workflow System
"""

import uuid
import asyncio
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import logging

class AgentStatus(Enum):
    """Agent execution status"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    WAITING = "waiting"

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class AgentTask:
    """Represents a task for an agent"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    agent_type: str = ""
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    status: AgentStatus = AgentStatus.IDLE
    priority: TaskPriority = TaskPriority.MEDIUM
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentResult:
    """Result returned by an agent"""
    success: bool
    data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None

class BaseAgent(ABC):
    """Base class for all agents in the workflow system"""
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        self.agent_id = agent_id or f"{self.__class__.__name__}_{uuid.uuid4().hex[:8]}"
        self.config = config or {}
        self.status = AgentStatus.IDLE
        self.logger = logging.getLogger(f"agent.{self.agent_id}")
        self.current_task: Optional[AgentTask] = None
        
    @property
    @abstractmethod
    def agent_type(self) -> str:
        """Return the type of this agent"""
        pass
    
    @property
    @abstractmethod
    def required_inputs(self) -> List[str]:
        """Return list of required input keys"""
        pass
    
    @property
    @abstractmethod
    def output_schema(self) -> Dict[str, type]:
        """Return the schema of expected outputs"""
        pass
    
    @abstractmethod
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute the agent's main task"""
        pass
    
    async def run(self, input_data: Dict[str, Any], priority: TaskPriority = TaskPriority.MEDIUM) -> AgentResult:
        """Run the agent with given input data"""
        task = AgentTask(
            agent_type=self.agent_type,
            input_data=input_data,
            priority=priority
        )
        
        return await self._execute_task(task)
    
    async def _execute_task(self, task: AgentTask) -> AgentResult:
        """Internal method to execute a task with error handling and logging"""
        start_time = datetime.now()
        
        try:
            # Validate inputs
            await self._validate_inputs(task)
            
            # Update status
            self.status = AgentStatus.RUNNING
            self.current_task = task
            task.status = AgentStatus.RUNNING
            task.started_at = start_time
            
            self.logger.info(f"Starting task {task.task_id}")
            
            # Execute the task
            result = await self.execute(task)
            
            # Update completion status
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            if result.success:
                self.status = AgentStatus.COMPLETED
                task.status = AgentStatus.COMPLETED
                task.output_data = result.data
                self.logger.info(f"Task {task.task_id} completed successfully in {execution_time:.2f}s")
            else:
                self.status = AgentStatus.FAILED
                task.status = AgentStatus.FAILED
                task.error_message = result.error_message
                self.logger.error(f"Task {task.task_id} failed: {result.error_message}")
            
            task.completed_at = end_time
            result.execution_time = execution_time
            
            return result
            
        except Exception as e:
            # Handle unexpected errors
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            self.status = AgentStatus.FAILED
            task.status = AgentStatus.FAILED
            task.error_message = str(e)
            task.completed_at = end_time
            
            error_msg = f"Unexpected error in task {task.task_id}: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            return AgentResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
        finally:
            self.current_task = None
    
    async def _validate_inputs(self, task: AgentTask) -> None:
        """Validate that all required inputs are present"""
        missing_inputs = []
        for required_input in self.required_inputs:
            if required_input not in task.input_data:
                missing_inputs.append(required_input)
        
        if missing_inputs:
            raise ValueError(f"Missing required inputs: {missing_inputs}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "status": self.status.value,
            "current_task_id": self.current_task.task_id if self.current_task else None,
            "config": self.config
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform agent health check"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "status": self.status.value,
            "healthy": True,
            "timestamp": datetime.now().isoformat()
        }