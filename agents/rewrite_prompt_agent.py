"""
Rewrite Prompt Agent - Reformulates prompts based on user preferences
Generates optimized prompts tailored to user's selected topics and goals
"""

from typing import Dict, Any, List
from .base_agent import BaseAgent, AgentTask, AgentResult

class RewritePromptAgent(BaseAgent):
    """
    Rewrite Prompt Agent for optimizing content generation prompts
    
    Responsibilities:
    - Analyze user selections and preferences
    - Generate optimized prompts for specific content types
    - Incorporate style guidelines and constraints
    - Ensure prompts align with user intent
    """
    
    @property
    def agent_type(self) -> str:
        return "rewrite_prompt"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["selected_concepts", "content_direction", "user_preferences"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "optimized_prompt": str,
            "prompt_metadata": dict,
            "generation_parameters": dict
        }
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute prompt rewriting"""
        try:
            selected_concepts = task.input_data["selected_concepts"]
            content_direction = task.input_data["content_direction"]
            user_preferences = task.input_data["user_preferences"]
            
            # Generate optimized prompt
            optimized_prompt = await self._generate_optimized_prompt(
                selected_concepts, content_direction, user_preferences
            )
            
            # Create prompt metadata
            prompt_metadata = await self._create_prompt_metadata(
                selected_concepts, content_direction, user_preferences
            )
            
            # Generate parameters for content generation
            generation_parameters = await self._create_generation_parameters(
                content_direction, user_preferences
            )
            
            return AgentResult(
                success=True,
                data={
                    "optimized_prompt": optimized_prompt,
                    "prompt_metadata": prompt_metadata,
                    "generation_parameters": generation_parameters
                }
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                error_message=f"Prompt rewriting failed: {str(e)}"
            )
    
    async def _generate_optimized_prompt(self, selected_concepts: List[Dict], 
                                       content_direction: Dict, 
                                       user_preferences: Dict) -> str:
        """Generate optimized prompt based on user selections"""
        
        # Extract key information
        primary_focus = content_direction.get("primary_focus", "")
        content_structure = content_direction.get("content_structure", [])
        style_guidelines = content_direction.get("style_guidelines", {})
        key_elements = content_direction.get("key_elements", [])
        
        # Build structured prompt
        prompt_parts = []
        
        # 1. Role and Context
        prompt_parts.append("你是一位专业的文学评论家和内容创作者。")
        
        # 2. Primary Focus
        if primary_focus:
            prompt_parts.append(f"请围绕《{primary_focus}》这一核心主题创作内容。")
        
        # 3. Content Structure
        if content_structure:
            structure_text = "请按照以下结构组织内容：\\n"
            for i, section in enumerate(content_structure, 1):
                structure_text += f"{i}. {section}\\n"
            prompt_parts.append(structure_text)
        
        # 4. Key Elements to Include
        if key_elements:
            high_priority_elements = [
                elem["element"] for elem in key_elements 
                if elem.get("importance", 1) >= 4
            ]
            if high_priority_elements:
                elements_text = f"重点讨论以下要素：{', '.join(high_priority_elements)}"
                prompt_parts.append(elements_text)
        
        # 5. Style Guidelines
        if style_guidelines:
            tone = style_guidelines.get("tone", "conversational")
            formality = style_guidelines.get("formality", "medium")
            
            style_instructions = {
                "academic": "采用严谨的学术写作风格，用词准确，逻辑清晰。",
                "conversational": "使用亲切自然的对话风格，贴近读者日常语言。",
                "storytelling": "运用生动的叙事技巧，增强故事性和可读性。",
                "philosophical": "深入进行哲学思辨，探讨深层次的人生哲理。"
            }
            
            if tone in style_instructions:
                prompt_parts.append(style_instructions[tone])
        
        # 6. Content Requirements
        content_types = user_preferences.get("content_types", [])
        if content_types:
            primary_type = content_types[0].get("type", "book_review")
            
            type_requirements = {
                "book_review": "创作一篇深度书评，包含作品分析、个人感悟和推荐理由。",
                "character_analysis": "深入分析主要人物的性格特征、发展轨迹和象征意义。",
                "theme_exploration": "探讨作品的核心主题，结合文本证据进行深度阐释。",
                "historical_context": "介绍作品的创作背景、历史意义和文化价值。"
            }
            
            if primary_type in type_requirements:
                prompt_parts.append(type_requirements[primary_type])
        
        # 7. Quality Requirements
        prompt_parts.append("要求：")
        prompt_parts.append("- 内容原创，观点独特")
        prompt_parts.append("- 语言优美，表达生动")
        prompt_parts.append("- 逻辑清晰，结构完整")
        prompt_parts.append("- 适合视频内容朗读")
        prompt_parts.append("- 字数控制在200-250字")
        
        # 8. Output Format
        prompt_parts.append("\\n请以JSON格式返回：")
        prompt_parts.append('{')
        prompt_parts.append('  "content": "生成的内容正文",')
        prompt_parts.append('  "title": "内容标题",')
        prompt_parts.append('  "tags": ["相关标签1", "相关标签2"]')
        prompt_parts.append('}')
        
        return "\\n\\n".join(prompt_parts)
    
    async def _create_prompt_metadata(self, selected_concepts: List[Dict],
                                    content_direction: Dict,
                                    user_preferences: Dict) -> Dict[str, Any]:
        """Create metadata for the generated prompt"""
        return {
            "concepts_count": len(selected_concepts),
            "primary_focus": content_direction.get("primary_focus", ""),
            "content_type": user_preferences.get("content_types", [{}])[0].get("type", "unknown"),
            "style": content_direction.get("style_guidelines", {}).get("tone", "conversational"),
            "target_audience": content_direction.get("target_audience", "general"),
            "estimated_length": content_direction.get("estimated_length", "medium"),
            "structure_sections": len(content_direction.get("content_structure", [])),
            "key_elements_count": len(content_direction.get("key_elements", []))
        }
    
    async def _create_generation_parameters(self, content_direction: Dict,
                                          user_preferences: Dict) -> Dict[str, Any]:
        """Create parameters for content generation"""
        style_guidelines = content_direction.get("style_guidelines", {})
        
        return {
            "temperature": self._get_temperature_for_style(style_guidelines.get("tone", "conversational")),
            "max_tokens": self._get_max_tokens_for_length(content_direction.get("estimated_length", "medium")),
            "top_p": 0.9,
            "frequency_penalty": 0.1,
            "presence_penalty": 0.1,
            "stop_sequences": ["\\n\\n\\n"],
            "response_format": "json"
        }
    
    def _get_temperature_for_style(self, style: str) -> float:
        """Get appropriate temperature setting for writing style"""
        temperature_map = {
            "academic": 0.3,      # More precise and factual
            "conversational": 0.7, # Balanced creativity and coherence
            "storytelling": 0.8,   # More creative and varied
            "philosophical": 0.5   # Thoughtful but consistent
        }
        return temperature_map.get(style, 0.7)
    
    def _get_max_tokens_for_length(self, length: str) -> int:
        """Get appropriate token limit for content length"""
        length_map = {
            "short": 300,
            "medium": 500,
            "long": 800
        }
        return length_map.get(length, 500)