"""
HITL Agent (Human-in-the-Loop) - Engages user in decision-making
Allows users to select relevant concepts, topics, and content preferences
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from .base_agent import BaseAgent, AgentTask, <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import json

@dataclass
class SelectionOption:
    """Represents a selectable option for the user"""
    id: str
    title: str
    description: str
    category: str
    metadata: Dict[str, Any]
    recommended: bool = False

@dataclass
class UserSelection:
    """Represents user's selection"""
    option_id: str
    selected: bool
    priority: int = 1  # 1-5 scale
    custom_notes: Optional[str] = None

@dataclass
class HITLSession:
    """Represents a HITL interaction session"""
    session_id: str
    user_id: Optional[str]
    options: List[SelectionOption]
    selections: List[UserSelection]
    status: str  # "pending", "completed", "expired"
    created_at: datetime
    expires_at: datetime
    completed_at: Optional[datetime] = None

class HITLAgent(BaseAgent):
    """
    Human-in-the-Loop Agent for user interaction and decision-making
    
    Responsibilities:
    - Present options to users for selection
    - Manage user interaction sessions
    - Collect and validate user preferences
    - Handle timeouts and fallback scenarios
    """
    
    @property
    def agent_type(self) -> str:
        return "hitl"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["search_results", "user_context"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "selected_concepts": list,
            "user_preferences": dict,
            "content_direction": dict,
            "session_metadata": dict
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.active_sessions: Dict[str, HITLSession] = {}
        self.user_interaction_handler: Optional[Callable] = None
        self.session_timeout = self.config.get("session_timeout_minutes", 30)
        
    def set_interaction_handler(self, handler: Callable):
        """Set the handler for user interactions (e.g., web interface, CLI)"""
        self.user_interaction_handler = handler
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute the HITL agent's main workflow"""
        try:
            search_results = task.input_data["search_results"]
            user_context = task.input_data.get("user_context", {})
            
            self.logger.info("Starting HITL interaction session")
            
            # Step 1: Create selection options from search results
            options = await self._create_selection_options(search_results, user_context)
            
            # Step 2: Create HITL session
            session = await self._create_session(options, user_context)
            
            # Step 3: Present options to user and wait for response
            user_selections = await self._present_and_collect(session)
            
            # Step 4: Process user selections
            processed_selections = await self._process_selections(user_selections, session)
            
            # Step 5: Generate content direction based on selections
            content_direction = await self._generate_content_direction(processed_selections, user_context)
            
            result_data = {
                "selected_concepts": processed_selections["concepts"],
                "user_preferences": processed_selections["preferences"],
                "content_direction": content_direction,
                "session_metadata": {
                    "session_id": session.session_id,
                    "total_options": len(session.options),
                    "selected_count": len([s for s in user_selections if s.selected]),
                    "interaction_time": (session.completed_at - session.created_at).total_seconds() if session.completed_at else None
                }
            }
            
            self.logger.info(f"HITL session completed. {len(processed_selections['concepts'])} concepts selected")
            
            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    "session_id": session.session_id,
                    "selection_quality": self._assess_selection_quality(user_selections)
                }
            )
            
        except Exception as e:
            self.logger.error(f"HITL execution failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"HITL execution failed: {str(e)}"
            )
    
    async def _create_selection_options(self, search_results: Dict[str, Any], 
                                      user_context: Dict[str, Any]) -> List[SelectionOption]:
        """Create user-selectable options from search results"""
        options = []
        
        # Create options from extracted concepts
        for i, concept in enumerate(search_results.get("extracted_concepts", [])):
            option = SelectionOption(
                id=f"concept_{i}",
                title=concept,
                description=f"包含关于{concept}的内容",
                category="core_concept",
                metadata={"concept": concept, "type": "extracted"},
                recommended=i < 3  # Mark first 3 as recommended
            )
            options.append(option)
        
        # Create options from related topics
        for i, topic in enumerate(search_results.get("related_topics", [])):
            option = SelectionOption(
                id=f"topic_{i}",
                title=topic["topic"],
                description=topic["description"],
                category="related_topic",
                metadata={"topic": topic, "relevance_score": topic["relevance_score"]},
                recommended=topic["relevance_score"] > 0.8
            )
            options.append(option)
        
        # Create content type options
        content_types = [
            {
                "id": "book_review",
                "title": "书籍评论",
                "description": "深度书评和文学分析",
                "category": "content_type"
            },
            {
                "id": "character_analysis", 
                "title": "人物分析",
                "description": "深入分析书中人物性格和发展",
                "category": "content_type"
            },
            {
                "id": "theme_exploration",
                "title": "主题探索", 
                "description": "探讨作品的核心主题和哲学思考",
                "category": "content_type"
            },
            {
                "id": "historical_context",
                "title": "历史背景",
                "description": "介绍作品的创作背景和历史意义",
                "category": "content_type"
            }
        ]
        
        for content_type in content_types:
            option = SelectionOption(
                id=content_type["id"],
                title=content_type["title"],
                description=content_type["description"],
                category=content_type["category"],
                metadata={"content_type": content_type["id"]},
                recommended=content_type["id"] == "book_review"  # Default recommendation
            )
            options.append(option)
        
        # Create style options
        style_options = [
            {
                "id": "academic",
                "title": "学术风格",
                "description": "严谨的学术分析风格",
                "category": "style"
            },
            {
                "id": "conversational",
                "title": "对话风格",
                "description": "轻松的对话和讨论风格",
                "category": "style"
            },
            {
                "id": "storytelling",
                "title": "叙事风格",
                "description": "故事性的叙述风格",
                "category": "style"
            },
            {
                "id": "philosophical",
                "title": "哲学思辨",
                "description": "深度的哲学思考和讨论",
                "category": "style"
            }
        ]
        
        for style in style_options:
            option = SelectionOption(
                id=style["id"],
                title=style["title"],
                description=style["description"],
                category=style["category"],
                metadata={"style": style["id"]},
                recommended=style["id"] == "conversational"  # Default recommendation
            )
            options.append(option)
        
        return options
    
    async def _create_session(self, options: List[SelectionOption], 
                            user_context: Dict[str, Any]) -> HITLSession:
        """Create a new HITL session"""
        session_id = f"hitl_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(options)) % 10000:04d}"
        
        session = HITLSession(
            session_id=session_id,
            user_id=user_context.get("user_id"),
            options=options,
            selections=[],
            status="pending",
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(minutes=self.session_timeout)
        )
        
        self.active_sessions[session_id] = session
        return session
    
    async def _present_and_collect(self, session: HITLSession) -> List[UserSelection]:
        """Present options to user and collect their selections"""
        if self.user_interaction_handler:
            # Use custom interaction handler (e.g., web interface)
            return await self.user_interaction_handler(session)
        else:
            # Use default automated selection for development/testing
            return await self._automated_selection(session)
    
    async def _automated_selection(self, session: HITLSession) -> List[UserSelection]:
        """Automated selection for testing purposes"""
        selections = []
        
        # Auto-select recommended options
        for option in session.options:
            if option.recommended:
                selection = UserSelection(
                    option_id=option.id,
                    selected=True,
                    priority=5 if option.category == "core_concept" else 3,
                    custom_notes="Auto-selected (recommended)"
                )
                selections.append(selection)
            elif option.category == "content_type" and option.metadata.get("content_type") == "book_review":
                # Always select book review as content type
                selection = UserSelection(
                    option_id=option.id,
                    selected=True,
                    priority=4,
                    custom_notes="Auto-selected (default content type)"
                )
                selections.append(selection)
            elif option.category == "style" and option.metadata.get("style") == "conversational":
                # Select conversational style by default
                selection = UserSelection(
                    option_id=option.id,
                    selected=True,
                    priority=3,
                    custom_notes="Auto-selected (default style)"
                )
                selections.append(selection)
        
        # Mark session as completed
        session.selections = selections
        session.status = "completed"
        session.completed_at = datetime.now()
        
        return selections
    
    async def _process_selections(self, selections: List[UserSelection], 
                                session: HITLSession) -> Dict[str, Any]:
        """Process user selections into structured data"""
        processed = {
            "concepts": [],
            "preferences": {
                "content_types": [],
                "styles": [],
                "priority_concepts": [],
                "custom_requirements": []
            }
        }
        
        # Create lookup for options
        option_lookup = {opt.id: opt for opt in session.options}
        
        # Process each selection
        for selection in selections:
            if not selection.selected:
                continue
                
            option = option_lookup.get(selection.option_id)
            if not option:
                continue
            
            if option.category == "core_concept":
                processed["concepts"].append({
                    "concept": option.metadata.get("concept", option.title),
                    "priority": selection.priority,
                    "notes": selection.custom_notes
                })
                
                if selection.priority >= 4:
                    processed["preferences"]["priority_concepts"].append(option.title)
                    
            elif option.category == "related_topic":
                processed["concepts"].append({
                    "concept": option.title,
                    "priority": selection.priority,
                    "type": "related_topic",
                    "relevance_score": option.metadata.get("relevance_score", 0.5)
                })
                
            elif option.category == "content_type":
                processed["preferences"]["content_types"].append({
                    "type": option.metadata.get("content_type", option.title),
                    "priority": selection.priority
                })
                
            elif option.category == "style":
                processed["preferences"]["styles"].append({
                    "style": option.metadata.get("style", option.title),
                    "priority": selection.priority
                })
            
            # Collect custom requirements
            if selection.custom_notes and "Auto-selected" not in selection.custom_notes:
                processed["preferences"]["custom_requirements"].append(selection.custom_notes)
        
        return processed
    
    async def _generate_content_direction(self, processed_selections: Dict[str, Any], 
                                        user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate content direction based on user selections"""
        direction = {
            "primary_focus": "",
            "content_structure": [],
            "style_guidelines": {},
            "key_elements": [],
            "target_audience": "general",
            "estimated_length": "medium"
        }
        
        # Determine primary focus
        priority_concepts = processed_selections["preferences"].get("priority_concepts", [])
        if priority_concepts:
            direction["primary_focus"] = priority_concepts[0]
        elif processed_selections["concepts"]:
            direction["primary_focus"] = processed_selections["concepts"][0]["concept"]
        
        # Determine content structure
        content_types = processed_selections["preferences"].get("content_types", [])
        if content_types:
            primary_type = content_types[0]["type"]
            if primary_type == "book_review":
                direction["content_structure"] = [
                    "书籍介绍",
                    "核心主题分析", 
                    "人物性格解读",
                    "写作技巧评析",
                    "个人感悟总结"
                ]
            elif primary_type == "character_analysis":
                direction["content_structure"] = [
                    "人物背景介绍",
                    "性格特征分析",
                    "成长轨迹探讨",
                    "关系网络解析",
                    "象征意义阐释"
                ]
            elif primary_type == "theme_exploration":
                direction["content_structure"] = [
                    "主题概述",
                    "文本证据分析",
                    "哲学层面思考",
                    "现实意义探讨",
                    "延伸思考"
                ]
        
        # Set style guidelines
        styles = processed_selections["preferences"].get("styles", [])
        if styles:
            primary_style = styles[0]["style"]
            direction["style_guidelines"] = {
                "tone": primary_style,
                "complexity": "medium",
                "formality": "medium" if primary_style == "conversational" else "high",
                "emotional_expression": "moderate"
            }
        
        # Collect key elements
        for concept_data in processed_selections["concepts"]:
            direction["key_elements"].append({
                "element": concept_data["concept"],
                "importance": concept_data["priority"],
                "type": concept_data.get("type", "core_concept")
            })
        
        # Set target audience based on selections
        if any(style["style"] == "academic" for style in styles):
            direction["target_audience"] = "academic"
        elif any(style["style"] == "conversational" for style in styles):
            direction["target_audience"] = "general_public"
        
        return direction
    
    def _assess_selection_quality(self, selections: List[UserSelection]) -> float:
        """Assess the quality of user selections"""
        if not selections:
            return 0.0
        
        selected_count = len([s for s in selections if s.selected])
        avg_priority = sum(s.priority for s in selections if s.selected) / max(selected_count, 1)
        
        # Quality based on selection count and average priority
        quality = min(1.0, (selected_count / 10) * 0.7 + (avg_priority / 5) * 0.3)
        return quality
    
    async def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a HITL session"""
        session = self.active_sessions.get(session_id)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "status": session.status,
            "created_at": session.created_at.isoformat(),
            "expires_at": session.expires_at.isoformat(),
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "total_options": len(session.options),
            "selections_count": len([s for s in session.selections if s.selected])
        }
    
    async def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        now = datetime.now()
        expired_sessions = [
            session_id for session_id, session in self.active_sessions.items()
            if now > session.expires_at and session.status == "pending"
        ]
        
        for session_id in expired_sessions:
            self.active_sessions[session_id].status = "expired"
            self.logger.info(f"Session {session_id} expired")
        
        # Remove old expired sessions (older than 24 hours)
        cleanup_threshold = now - timedelta(hours=24)
        old_sessions = [
            session_id for session_id, session in self.active_sessions.items()
            if session.created_at < cleanup_threshold and session.status == "expired"
        ]
        
        for session_id in old_sessions:
            del self.active_sessions[session_id]
            self.logger.info(f"Cleaned up old session {session_id}")