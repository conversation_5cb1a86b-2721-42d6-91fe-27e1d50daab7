"""
Workflow Agent - Supervises the entire agent workflow
Monitors task progress, manages execution flow, and handles failures
"""

import asyncio
from typing import Dict, Any, List, Optional, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import uuid
import json

from .base_agent import BaseAgent, AgentTask, AgentResult, AgentStatus, TaskPriority

class WorkflowStatus(Enum):
    """Workflow execution status"""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

@dataclass
class WorkflowStep:
    """Represents a step in the workflow"""
    step_id: str
    agent_type: str
    depends_on: List[str] = field(default_factory=list)
    input_mapping: Dict[str, str] = field(default_factory=dict)
    output_mapping: Dict[str, str] = field(default_factory=dict)
    required: bool = True
    retry_count: int = 3
    timeout_seconds: int = 300
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class WorkflowExecution:
    """Represents a workflow execution instance"""
    workflow_id: str
    workflow_name: str
    status: WorkflowStatus
    steps: List[WorkflowStep]
    current_step: Optional[str] = None
    step_results: Dict[str, AgentResult] = field(default_factory=dict)
    global_context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    progress: float = 0.0

class WorkflowAgent(BaseAgent):
    """
    Workflow Agent for managing multi-agent workflows
    
    Responsibilities:
    - Define and execute complex multi-agent workflows
    - Handle dependencies between agents
    - Manage error handling and retry logic
    - Provide progress monitoring and reporting
    - Handle parallel and sequential execution patterns
    """
    
    @property
    def agent_type(self) -> str:
        return "workflow"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["workflow_definition", "initial_data"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "workflow_result": dict,
            "execution_summary": dict,
            "step_results": dict,
            "performance_metrics": dict
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.registered_agents: Dict[str, BaseAgent] = {}
        self.active_workflows: Dict[str, WorkflowExecution] = {}
        self.workflow_templates: Dict[str, List[WorkflowStep]] = {}
        self._initialize_default_workflows()
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent for workflow execution"""
        self.registered_agents[agent.agent_type] = agent
        self.logger.info(f"Registered agent: {agent.agent_type} ({agent.agent_id})")
    
    def _initialize_default_workflows(self):
        """Initialize default workflow templates"""
        
        # Book Review Video Generation Workflow
        self.workflow_templates["book_review_video"] = [
            WorkflowStep(
                step_id="search",
                agent_type="search",
                depends_on=[],
                input_mapping={
                    "user_input": "user_input",
                    "domain_context": "domain_context"
                },
                output_mapping={
                    "search_results": "search_results"
                }
            ),
            WorkflowStep(
                step_id="hitl",
                agent_type="hitl", 
                depends_on=["search"],
                input_mapping={
                    "search_results": "search_results",
                    "user_context": "user_context"
                },
                output_mapping={
                    "selected_concepts": "selected_concepts",
                    "user_preferences": "user_preferences",
                    "content_direction": "content_direction"
                }
            ),
            WorkflowStep(
                step_id="rewrite_prompt",
                agent_type="rewrite_prompt",
                depends_on=["hitl"],
                input_mapping={
                    "selected_concepts": "selected_concepts",
                    "content_direction": "content_direction",
                    "user_preferences": "user_preferences"
                },
                output_mapping={
                    "optimized_prompt": "optimized_prompt"
                }
            ),
            WorkflowStep(
                step_id="scripting",
                agent_type="scripting",
                depends_on=["rewrite_prompt"],
                input_mapping={
                    "optimized_prompt": "optimized_prompt",
                    "content_direction": "content_direction"
                },
                output_mapping={
                    "script_content": "script_content",
                    "script_metadata": "script_metadata"
                }
            ),
            WorkflowStep(
                step_id="audio_generation",
                agent_type="audio",
                depends_on=["scripting"],
                input_mapping={
                    "script_content": "script_content",
                    "script_metadata": "script_metadata"
                },
                output_mapping={
                    "audio_segments": "audio_segments",
                    "alignment_data": "alignment_data"
                }
            ),
            WorkflowStep(
                step_id="image_generation",
                agent_type="image_context",
                depends_on=["scripting"],
                input_mapping={
                    "script_content": "script_content",
                    "selected_concepts": "selected_concepts"
                },
                output_mapping={
                    "background_images": "background_images",
                    "cover_image": "cover_image"
                }
            ),
            WorkflowStep(
                step_id="video_assembly",
                agent_type="video",
                depends_on=["audio_generation", "image_generation"],
                input_mapping={
                    "audio_segments": "audio_segments",
                    "alignment_data": "alignment_data", 
                    "background_images": "background_images",
                    "cover_image": "cover_image",
                    "script_metadata": "script_metadata"
                },
                output_mapping={
                    "final_video": "final_video",
                    "video_metadata": "video_metadata"
                }
            )
        ]
        
        # Podcast Generation Workflow
        self.workflow_templates["podcast_generation"] = [
            WorkflowStep(
                step_id="search",
                agent_type="search",
                depends_on=[],
                input_mapping={
                    "user_input": "user_input",
                    "domain_context": "domain_context"
                },
                output_mapping={
                    "search_results": "search_results"
                }
            ),
            WorkflowStep(
                step_id="hitl",
                agent_type="hitl",
                depends_on=["search"],
                input_mapping={
                    "search_results": "search_results",
                    "user_context": "user_context"
                },
                output_mapping={
                    "selected_concepts": "selected_concepts",
                    "user_preferences": "user_preferences"
                }
            ),
            WorkflowStep(
                step_id="scripting",
                agent_type="scripting",
                depends_on=["hitl"],
                input_mapping={
                    "selected_concepts": "selected_concepts",
                    "user_preferences": "user_preferences"
                },
                output_mapping={
                    "podcast_script": "podcast_script"
                },
                metadata={"format": "podcast", "speakers": "multiple"}
            ),
            WorkflowStep(
                step_id="audio_generation",
                agent_type="audio",
                depends_on=["scripting"],
                input_mapping={
                    "podcast_script": "podcast_script"
                },
                output_mapping={
                    "podcast_audio": "podcast_audio"
                }
            )
        ]
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute a workflow"""
        try:
            workflow_definition = task.input_data["workflow_definition"]
            initial_data = task.input_data["initial_data"]
            
            # Create workflow execution
            workflow_execution = await self._create_workflow_execution(workflow_definition, initial_data)
            
            # Execute the workflow
            result = await self._execute_workflow(workflow_execution)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"Workflow execution failed: {str(e)}"
            )
    
    async def execute_workflow_by_name(self, workflow_name: str, initial_data: Dict[str, Any]) -> AgentResult:
        """Execute a predefined workflow by name"""
        if workflow_name not in self.workflow_templates:
            return AgentResult(
                success=False,
                error_message=f"Unknown workflow template: {workflow_name}"
            )
        
        workflow_definition = {
            "name": workflow_name,
            "steps": self.workflow_templates[workflow_name]
        }
        
        task = AgentTask(
            agent_type=self.agent_type,
            input_data={
                "workflow_definition": workflow_definition,
                "initial_data": initial_data
            }
        )
        
        return await self.execute(task)
    
    async def _create_workflow_execution(self, workflow_definition: Dict[str, Any], 
                                       initial_data: Dict[str, Any]) -> WorkflowExecution:
        """Create a new workflow execution instance"""
        workflow_id = f"wf_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        steps = []
        if "steps" in workflow_definition:
            for step_data in workflow_definition["steps"]:
                if isinstance(step_data, WorkflowStep):
                    steps.append(step_data)
                else:
                    step = WorkflowStep(**step_data)
                    steps.append(step)
        
        workflow_execution = WorkflowExecution(
            workflow_id=workflow_id,
            workflow_name=workflow_definition.get("name", "custom_workflow"),
            status=WorkflowStatus.CREATED,
            steps=steps,
            global_context=initial_data.copy()
        )
        
        self.active_workflows[workflow_id] = workflow_execution
        return workflow_execution
    
    async def _execute_workflow(self, workflow_execution: WorkflowExecution) -> AgentResult:
        """Execute a workflow with dependency management"""
        try:
            workflow_execution.status = WorkflowStatus.RUNNING
            workflow_execution.started_at = datetime.now()
            
            self.logger.info(f"Starting workflow execution: {workflow_execution.workflow_id}")
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(workflow_execution.steps)
            
            # Execute steps in dependency order
            completed_steps = set()
            total_steps = len(workflow_execution.steps)
            
            while len(completed_steps) < total_steps:
                # Find executable steps (all dependencies completed)
                executable_steps = []
                for step in workflow_execution.steps:
                    if (step.step_id not in completed_steps and 
                        all(dep in completed_steps for dep in step.depends_on)):
                        executable_steps.append(step)
                
                if not executable_steps:
                    # Check for circular dependencies or missing steps
                    remaining_steps = [s.step_id for s in workflow_execution.steps if s.step_id not in completed_steps]
                    raise Exception(f"Workflow deadlock: cannot execute remaining steps {remaining_steps}")
                
                # Execute steps in parallel if possible
                step_tasks = []
                for step in executable_steps:
                    task = self._execute_workflow_step(workflow_execution, step)
                    step_tasks.append(task)
                
                # Wait for all parallel steps to complete
                step_results = await asyncio.gather(*step_tasks, return_exceptions=True)
                
                # Process results
                for i, (step, result) in enumerate(zip(executable_steps, step_results)):
                    if isinstance(result, Exception):
                        if step.required:
                            raise result
                        else:
                            self.logger.warning(f"Optional step {step.step_id} failed: {result}")
                            # Create a failed result for optional step
                            workflow_execution.step_results[step.step_id] = AgentResult(
                                success=False,
                                error_message=str(result)
                            )
                    else:
                        workflow_execution.step_results[step.step_id] = result
                        
                        # Update global context with step outputs
                        if result.success:
                            self._update_global_context(workflow_execution, step, result)
                    
                    completed_steps.add(step.step_id)
                
                # Update progress
                workflow_execution.progress = len(completed_steps) / total_steps
                workflow_execution.current_step = None  # All current steps completed
            
            # Workflow completed successfully
            workflow_execution.status = WorkflowStatus.COMPLETED
            workflow_execution.completed_at = datetime.now()
            workflow_execution.progress = 1.0
            
            # Compile final result
            final_result = self._compile_workflow_result(workflow_execution)
            
            self.logger.info(f"Workflow {workflow_execution.workflow_id} completed successfully")
            
            return AgentResult(
                success=True,
                data=final_result,
                metadata={
                    "workflow_id": workflow_execution.workflow_id,
                    "execution_time": (workflow_execution.completed_at - workflow_execution.started_at).total_seconds(),
                    "steps_completed": len(completed_steps)
                }
            )
            
        except Exception as e:
            workflow_execution.status = WorkflowStatus.FAILED
            workflow_execution.completed_at = datetime.now()
            workflow_execution.error_message = str(e)
            
            self.logger.error(f"Workflow {workflow_execution.workflow_id} failed: {str(e)}")
            
            return AgentResult(
                success=False,
                error_message=str(e),
                metadata={"workflow_id": workflow_execution.workflow_id}
            )
    
    async def _execute_workflow_step(self, workflow_execution: WorkflowExecution, 
                                   step: WorkflowStep) -> AgentResult:
        """Execute a single workflow step"""
        self.logger.info(f"Executing step: {step.step_id} ({step.agent_type})")
        workflow_execution.current_step = step.step_id
        
        # Get the agent for this step
        agent = self.registered_agents.get(step.agent_type)
        if not agent:
            raise Exception(f"Agent not found for type: {step.agent_type}")
        
        # Prepare input data
        input_data = self._prepare_step_input(workflow_execution, step)
        
        # Execute with retry logic
        for attempt in range(step.retry_count):
            try:
                # Execute the agent with timeout
                result = await asyncio.wait_for(
                    agent.run(input_data),
                    timeout=step.timeout_seconds
                )
                
                if result.success:
                    self.logger.info(f"Step {step.step_id} completed successfully")
                    return result
                else:
                    self.logger.warning(f"Step {step.step_id} failed (attempt {attempt + 1}): {result.error_message}")
                    if attempt == step.retry_count - 1:
                        raise Exception(f"Step {step.step_id} failed after {step.retry_count} attempts: {result.error_message}")
                    
                    # Wait before retry
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    
            except asyncio.TimeoutError:
                if attempt == step.retry_count - 1:
                    raise Exception(f"Step {step.step_id} timed out after {step.timeout_seconds} seconds")
                await asyncio.sleep(2 ** attempt)
            except Exception as e:
                if attempt == step.retry_count - 1:
                    raise
                await asyncio.sleep(2 ** attempt)
        
        raise Exception(f"Step {step.step_id} failed after all retry attempts")
    
    def _prepare_step_input(self, workflow_execution: WorkflowExecution, step: WorkflowStep) -> Dict[str, Any]:
        """Prepare input data for a workflow step"""
        input_data = {}
        
        for step_input_key, context_key in step.input_mapping.items():
            if context_key in workflow_execution.global_context:
                input_data[step_input_key] = workflow_execution.global_context[context_key]
            else:
                self.logger.warning(f"Missing context key for step {step.step_id}: {context_key}")
        
        return input_data
    
    def _update_global_context(self, workflow_execution: WorkflowExecution, 
                             step: WorkflowStep, result: AgentResult):
        """Update global context with step results"""
        for result_key, context_key in step.output_mapping.items():
            if result_key in result.data:
                workflow_execution.global_context[context_key] = result.data[result_key]
    
    def _build_dependency_graph(self, steps: List[WorkflowStep]) -> Dict[str, List[str]]:
        """Build dependency graph for workflow steps"""
        graph = {}
        for step in steps:
            graph[step.step_id] = step.depends_on.copy()
        return graph
    
    def _compile_workflow_result(self, workflow_execution: WorkflowExecution) -> Dict[str, Any]:
        """Compile final workflow result"""
        successful_steps = [
            step_id for step_id, result in workflow_execution.step_results.items()
            if result.success
        ]
        
        failed_steps = [
            step_id for step_id, result in workflow_execution.step_results.items()
            if not result.success
        ]
        
        return {
            "workflow_result": workflow_execution.global_context,
            "execution_summary": {
                "workflow_id": workflow_execution.workflow_id,
                "workflow_name": workflow_execution.workflow_name,
                "status": workflow_execution.status.value,
                "total_steps": len(workflow_execution.steps),
                "successful_steps": len(successful_steps),
                "failed_steps": len(failed_steps),
                "execution_time": (
                    workflow_execution.completed_at - workflow_execution.started_at
                ).total_seconds() if workflow_execution.completed_at else None,
                "progress": workflow_execution.progress
            },
            "step_results": {
                step_id: {
                    "success": result.success,
                    "execution_time": result.execution_time,
                    "error_message": result.error_message
                }
                for step_id, result in workflow_execution.step_results.items()
            },
            "performance_metrics": {
                "total_execution_time": (
                    workflow_execution.completed_at - workflow_execution.started_at
                ).total_seconds() if workflow_execution.completed_at else None,
                "average_step_time": sum(
                    result.execution_time for result in workflow_execution.step_results.values()
                    if result.execution_time
                ) / len(workflow_execution.step_results) if workflow_execution.step_results else 0,
                "success_rate": len(successful_steps) / len(workflow_execution.steps) if workflow_execution.steps else 0
            }
        }
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a workflow execution"""
        workflow = self.active_workflows.get(workflow_id)
        if not workflow:
            return None
        
        return {
            "workflow_id": workflow.workflow_id,
            "workflow_name": workflow.workflow_name,
            "status": workflow.status.value,
            "progress": workflow.progress,
            "current_step": workflow.current_step,
            "created_at": workflow.created_at.isoformat(),
            "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
            "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
            "error_message": workflow.error_message,
            "steps_completed": len([
                step_id for step_id, result in workflow.step_results.items()
                if result.success
            ]),
            "total_steps": len(workflow.steps)
        }
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow"""
        workflow = self.active_workflows.get(workflow_id)
        if not workflow or workflow.status != WorkflowStatus.RUNNING:
            return False
        
        workflow.status = WorkflowStatus.CANCELLED
        workflow.completed_at = datetime.now()
        
        self.logger.info(f"Workflow {workflow_id} cancelled")
        return True
    
    def list_workflow_templates(self) -> List[str]:
        """List available workflow templates"""
        return list(self.workflow_templates.keys())
    
    def get_workflow_template(self, template_name: str) -> Optional[List[WorkflowStep]]:
        """Get a workflow template by name"""
        return self.workflow_templates.get(template_name)