"""
Audio Agent - Handles Doubao TTS integration for emotionally rich voiceovers
Manages voice selection, emotion mapping, and audio timeline synchronization
"""

import asyncio
import json
import websockets
import ssl
import uuid
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from .base_agent import BaseAgent, AgentTask, AgentResult
import base64
import io
import wave
import time
from datetime import datetime

@dataclass
class AudioSegment:
    """Represents an audio segment with metadata"""
    segment_id: str
    audio_data: bytes
    duration: float
    voice_id: str
    emotion: str
    text: str
    timing: Dict[str, float]
    metadata: Dict[str, Any]

@dataclass
class VoiceMapping:
    """Maps speakers to voice configurations"""
    speaker_name: str
    voice_id: str
    emotions: List[str]
    base_config: Dict[str, Any]

class AudioAgent(BaseAgent):
    """
    Audio Agent for Doubao TTS integration
    
    Responsibilities:
    - Convert script segments to high-quality audio
    - Manage multi-emotion voice synthesis
    - Handle voice assignment and speaker consistency
    - Create synchronized audio timeline
    - Integrate background music and sound effects
    """
    
    @property
    def agent_type(self) -> str:
        return "audio"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["script_content", "script_metadata"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "audio_segments": list,
            "alignment_data": dict,
            "voice_metadata": dict,
            "audio_timeline": dict
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.doubao_config = self._initialize_doubao_config()
        self.voice_library = self._initialize_voice_library()
        self.emotion_mappings = self._initialize_emotion_mappings()
        self.audio_cache = {}
    
    def _initialize_doubao_config(self) -> Dict[str, Any]:
        """Initialize Doubao TTS configuration"""
        return {
            "api_url": self.config.get("doubao_api_url", "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"),
            "app_id": self.config.get("doubao_app_id", "2588986021"),
            "token": self.config.get("doubao_token", ""),
            "cluster": self.config.get("doubao_cluster", "volc_tts"),
            "voice_type": "BIG_MODEL",
            "encoding": "mp3",
            "sample_rate": 24000,
            "speech_rate": 1.0,
            "volume": 0.95,
            "pitch_rate": 1.0
        }
    
    def _initialize_voice_library(self) -> Dict[str, Dict[str, Any]]:
        """Initialize the voice library with verified Doubao voices"""
        return {
            "zh_female_roumeinvyou_emo_v2_mars_bigtts": {
                "name": "柔美女友（多情感）",
                "description": "温柔甜美的女声，情感丰富细腻",
                "gender": "female",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "hate", "excited", "coldness", "neutral"],
                "verified": True,
                "recommended_for": ["主持人", "评论员", "旁白者"]
            },
            "zh_male_jingqiangnansheng_emo_v2_mars_bigtts": {
                "name": "京腔男声（多情感）",
                "description": "富有磁性的男声，京腔韵味浓厚",
                "gender": "male", 
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "hate", "excited", "coldness", "neutral"],
                "verified": True,
                "recommended_for": ["嘉宾", "学者", "专家"]
            },
            "zh_female_wennuandashu_emo_v2_mars_bigtts": {
                "name": "温暖大叔（多情感）",
                "description": "温暖成熟的女声，适合深度内容",
                "gender": "female",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "hate", "excited", "coldness", "neutral"],
                "verified": True,
                "recommended_for": ["学者", "专业解说"]
            },
            "zh_female_qingchunvyou_emo_v2_mars_bigtts": {
                "name": "青春女友（多情感）",
                "description": "青春活泼的女声，充满朝气",
                "gender": "female",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "hate", "excited", "coldness", "neutral"],
                "verified": True,
                "recommended_for": ["年轻主持人", "活泼解说"]
            }
        }
    
    def _initialize_emotion_mappings(self) -> Dict[str, str]:
        """Map script emotions to Doubao TTS emotions"""
        return {
            "neutral": "neutral",
            "happy": "happy", 
            "excited": "excited",
            "enthusiastic": "excited",
            "contemplative": "neutral",
            "analytical": "neutral",
            "thoughtful": "neutral",
            "emotional": "sad",
            "warm": "happy",
            "mysterious": "coldness",
            "dramatic": "surprised"
        }
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute audio generation workflow"""
        try:
            script_content = task.input_data["script_content"]
            script_metadata = task.input_data["script_metadata"]
            
            self.logger.info("Starting audio generation with Doubao TTS")
            
            # Step 1: Parse script segments
            script_segments = await self._parse_script_segments(script_content, script_metadata)
            
            # Step 2: Create voice mappings
            voice_mappings = await self._create_voice_mappings(script_segments, script_metadata)
            
            # Step 3: Generate audio for each segment
            audio_segments = await self._generate_audio_segments(script_segments, voice_mappings)
            
            # Step 4: Create audio timeline
            audio_timeline = await self._create_audio_timeline(audio_segments)
            
            # Step 5: Generate alignment data
            alignment_data = await self._create_alignment_data(audio_segments, script_segments)
            
            # Step 6: Create voice metadata
            voice_metadata = await self._create_voice_metadata(voice_mappings, audio_segments)
            
            result_data = {
                "audio_segments": [segment.__dict__ for segment in audio_segments],
                "alignment_data": alignment_data,
                "voice_metadata": voice_metadata,
                "audio_timeline": audio_timeline
            }
            
            self.logger.info(f"Audio generation completed. Generated {len(audio_segments)} audio segments")
            
            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    "total_segments": len(audio_segments),
                    "total_duration": sum(seg.duration for seg in audio_segments),
                    "voices_used": len(voice_mappings),
                    "emotions_used": len(set(seg.emotion for seg in audio_segments))
                }
            )
            
        except Exception as e:
            self.logger.error(f"Audio generation failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"Audio generation failed: {str(e)}"
            )
    
    async def _parse_script_segments(self, script_content: Dict[str, Any], 
                                   script_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse script content into processable segments"""
        segments = []
        
        if "sections" in script_content:
            segment_id = 0
            cumulative_time = 0
            
            for section_name, section_data in script_content["sections"].items():
                segment = {
                    "id": f"seg_{segment_id:03d}",
                    "text": section_data["content"],
                    "speaker": section_data.get("speaker", "默认"),
                    "emotion": section_data.get("emotion", "neutral"),
                    "duration": section_data.get("duration", 30),
                    "start_time": cumulative_time,
                    "section_name": section_name,
                    "purpose": section_data.get("purpose", ""),
                    "word_count": len(section_data["content"])
                }
                segments.append(segment)
                segment_id += 1
                cumulative_time += segment["duration"]
        
        return segments
    
    async def _create_voice_mappings(self, script_segments: List[Dict[str, Any]], 
                                   script_metadata: Dict[str, Any]) -> List[VoiceMapping]:
        """Create voice mappings for speakers"""
        unique_speakers = list(set(seg["speaker"] for seg in script_segments))
        voice_mappings = []
        
        # Get style from metadata to influence voice selection
        style = script_metadata.get("script_info", {}).get("style", "conversational")
        
        for i, speaker in enumerate(unique_speakers):
            # Select appropriate voice based on speaker role and style
            voice_id = self._select_voice_for_speaker(speaker, style, i)
            voice_config = self.voice_library[voice_id]
            
            mapping = VoiceMapping(
                speaker_name=speaker,
                voice_id=voice_id,
                emotions=voice_config["emotions"],
                base_config={
                    "voice_type": self.doubao_config["voice_type"],
                    "encoding": self.doubao_config["encoding"],
                    "sample_rate": self.doubao_config["sample_rate"],
                    "speech_rate": self._get_speech_rate_for_style(style),
                    "volume": self.doubao_config["volume"],
                    "pitch_rate": self.doubao_config["pitch_rate"]
                }
            )
            voice_mappings.append(mapping)
        
        return voice_mappings
    
    def _select_voice_for_speaker(self, speaker: str, style: str, speaker_index: int) -> str:
        """Select appropriate voice ID for speaker"""
        # Voice selection logic based on speaker role and style
        voice_preferences = {
            ("主持人", "conversational"): "zh_female_roumeinvyou_emo_v2_mars_bigtts",
            ("嘉宾", "conversational"): "zh_male_jingqiangnansheng_emo_v2_mars_bigtts",
            ("评论员", "academic"): "zh_female_wennuandashu_emo_v2_mars_bigtts",
            ("旁白者", "storytelling"): "zh_female_qingchunvyou_emo_v2_mars_bigtts",
            ("学者A", "academic"): "zh_female_wennuandashu_emo_v2_mars_bigtts",
            ("学者B", "academic"): "zh_male_jingqiangnansheng_emo_v2_mars_bigtts"
        }
        
        # Try exact match first
        selected_voice = voice_preferences.get((speaker, style))
        if selected_voice:
            return selected_voice
        
        # Fallback based on speaker index and available voices
        available_voices = list(self.voice_library.keys())
        return available_voices[speaker_index % len(available_voices)]
    
    def _get_speech_rate_for_style(self, style: str) -> float:
        """Get appropriate speech rate for content style"""
        rate_map = {
            "academic": 0.85,      # Slower for clarity
            "conversational": 1.0,  # Normal pace
            "storytelling": 0.9,    # Slightly slower for dramatic effect
            "philosophical": 0.8    # Contemplative pace
        }
        return rate_map.get(style, 1.0)
    
    async def _generate_audio_segments(self, script_segments: List[Dict[str, Any]], 
                                     voice_mappings: List[VoiceMapping]) -> List[AudioSegment]:
        """Generate audio for all script segments"""
        audio_segments = []
        
        # Create speaker to voice mapping
        speaker_voice_map = {mapping.speaker_name: mapping for mapping in voice_mappings}
        
        # Generate audio for each segment
        for segment in script_segments:
            voice_mapping = speaker_voice_map[segment["speaker"]]
            
            try:
                audio_data, actual_duration = await self._generate_single_audio(
                    text=segment["text"],
                    voice_mapping=voice_mapping,
                    emotion=segment["emotion"]
                )
                
                audio_segment = AudioSegment(
                    segment_id=segment["id"],
                    audio_data=audio_data,
                    duration=actual_duration,
                    voice_id=voice_mapping.voice_id,
                    emotion=segment["emotion"],
                    text=segment["text"],
                    timing={
                        "start_time": segment["start_time"],
                        "end_time": segment["start_time"] + actual_duration,
                        "planned_duration": segment["duration"]
                    },
                    metadata={
                        "speaker": segment["speaker"],
                        "section_name": segment["section_name"],
                        "word_count": segment["word_count"],
                        "purpose": segment["purpose"]
                    }
                )
                audio_segments.append(audio_segment)
                
                self.logger.info(f"Generated audio for segment {segment['id']} ({actual_duration:.2f}s)")
                
                # Add delay to respect rate limits
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Failed to generate audio for segment {segment['id']}: {str(e)}")
                # Create silent segment as fallback
                silent_segment = self._create_silent_segment(segment, voice_mapping)
                audio_segments.append(silent_segment)
        
        return audio_segments
    
    async def _generate_single_audio(self, text: str, voice_mapping: VoiceMapping, 
                                   emotion: str) -> Tuple[bytes, float]:
        """Generate audio for a single text segment using Doubao TTS"""
        
        # Map emotion to Doubao emotion
        doubao_emotion = self.emotion_mappings.get(emotion, "neutral")
        
        # Prepare TTS request
        request_config = {
            "app": {
                "appid": self.doubao_config["app_id"],
                "token": self.doubao_config["token"],
                "cluster": self.doubao_config["cluster"]
            },
            "user": {
                "uid": f"user_{uuid.uuid4().hex[:8]}"
            },
            "audio": {
                "voice_type": voice_mapping.voice_id,
                "encoding": voice_mapping.base_config["encoding"],
                "speed_ratio": voice_mapping.base_config["speech_rate"],
                "volume_ratio": voice_mapping.base_config["volume"],
                "pitch_ratio": voice_mapping.base_config["pitch_rate"],
                "emotion": doubao_emotion
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            # Use WebSocket connection for Doubao TTS
            audio_data = await self._call_doubao_websocket(request_config)
            
            # Calculate actual duration from audio data
            duration = self._calculate_audio_duration(audio_data, voice_mapping.base_config["sample_rate"])
            
            return audio_data, duration
            
        except Exception as e:
            self.logger.error(f"Doubao TTS API call failed: {str(e)}")
            # Return synthesized audio as fallback
            return self._generate_fallback_audio(text, voice_mapping), len(text) * 0.1
    
    async def _call_doubao_websocket(self, config: Dict[str, Any]) -> bytes:
        """Call Doubao TTS via WebSocket"""
        
        # Create SSL context
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        audio_data = b""
        
        try:
            async with websockets.connect(
                self.doubao_config["api_url"],
                ssl=ssl_context,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                
                # Send request
                await websocket.send(json.dumps(config))
                
                # Receive audio data
                async for message in websocket:
                    try:
                        response = json.loads(message)
                        
                        if "data" in response:
                            # Decode base64 audio data
                            audio_chunk = base64.b64decode(response["data"])
                            audio_data += audio_chunk
                        
                        if response.get("code") == 1000:
                            # TTS completed successfully
                            break
                        elif response.get("code") != 1000 and response.get("code") is not None:
                            # Error occurred
                            raise Exception(f"Doubao TTS error: {response.get('message', 'Unknown error')}")
                    
                    except json.JSONDecodeError:
                        # Message might be binary data
                        continue
        
        except Exception as e:
            self.logger.error(f"WebSocket connection failed: {str(e)}")
            raise
        
        if not audio_data:
            raise Exception("No audio data received from Doubao TTS")
        
        return audio_data
    
    def _calculate_audio_duration(self, audio_data: bytes, sample_rate: int) -> float:
        """Calculate duration of audio data"""
        try:
            # For MP3, this is an approximation
            # In production, use proper audio analysis libraries
            estimated_duration = len(audio_data) / (sample_rate * 2 * 0.128)  # Rough estimate for MP3
            return max(0.1, estimated_duration)  # Minimum 0.1 seconds
        except:
            return 1.0  # Default fallback
    
    def _generate_fallback_audio(self, text: str, voice_mapping: VoiceMapping) -> bytes:
        """Generate fallback audio when TTS fails"""
        # Create a minimal WAV file with silence
        # In production, this could use a local TTS engine
        duration = len(text) * 0.1  # 0.1 seconds per character
        sample_rate = voice_mapping.base_config["sample_rate"]
        samples = int(duration * sample_rate)
        
        # Create silent audio data
        audio_data = b'\x00' * (samples * 2)  # 16-bit silence
        
        # Wrap in WAV format
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data)
        
        return wav_buffer.getvalue()
    
    def _create_silent_segment(self, segment: Dict[str, Any], 
                             voice_mapping: VoiceMapping) -> AudioSegment:
        """Create a silent audio segment as fallback"""
        duration = segment["duration"]
        silent_audio = self._generate_fallback_audio("静音", voice_mapping)
        
        return AudioSegment(
            segment_id=segment["id"],
            audio_data=silent_audio,
            duration=duration,
            voice_id=voice_mapping.voice_id,
            emotion="neutral",
            text=segment["text"],
            timing={
                "start_time": segment["start_time"],
                "end_time": segment["start_time"] + duration,
                "planned_duration": duration
            },
            metadata={
                "speaker": segment["speaker"],
                "section_name": segment["section_name"],
                "word_count": segment["word_count"],
                "purpose": segment["purpose"],
                "fallback": True
            }
        )
    
    async def _create_audio_timeline(self, audio_segments: List[AudioSegment]) -> Dict[str, Any]:
        """Create synchronized audio timeline"""
        timeline = {
            "total_duration": 0,
            "segments": [],
            "transitions": [],
            "synchronization_points": []
        }
        
        current_time = 0
        
        for i, segment in enumerate(audio_segments):
            # Adjust timing based on actual audio duration
            segment_timeline = {
                "segment_id": segment.segment_id,
                "start_time": current_time,
                "end_time": current_time + segment.duration,
                "duration": segment.duration,
                "voice_id": segment.voice_id,
                "emotion": segment.emotion,
                "speaker": segment.metadata.get("speaker", ""),
                "text_preview": segment.text[:50] + "..." if len(segment.text) > 50 else segment.text
            }
            timeline["segments"].append(segment_timeline)
            
            # Add transition if not the last segment
            if i < len(audio_segments) - 1:
                transition = {
                    "from_segment": segment.segment_id,
                    "to_segment": audio_segments[i + 1].segment_id,
                    "transition_time": current_time + segment.duration,
                    "transition_type": "crossfade" if segment.voice_id != audio_segments[i + 1].voice_id else "direct",
                    "duration": 0.5
                }
                timeline["transitions"].append(transition)
                current_time += 0.5  # Add transition time
            
            current_time += segment.duration
            
            # Add synchronization points for subtitle alignment
            sync_point = {
                "time": current_time,
                "segment_id": segment.segment_id,
                "type": "segment_end",
                "text_length": len(segment.text)
            }
            timeline["synchronization_points"].append(sync_point)
        
        timeline["total_duration"] = current_time
        
        return timeline
    
    async def _create_alignment_data(self, audio_segments: List[AudioSegment], 
                                   script_segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create alignment data for subtitle synchronization"""
        alignment = {
            "subtitle_timings": [],
            "word_level_timings": [],
            "emotion_markers": [],
            "speaker_changes": []
        }
        
        for audio_seg, script_seg in zip(audio_segments, script_segments):
            # Create subtitle timing
            subtitle_timing = {
                "segment_id": audio_seg.segment_id,
                "text": audio_seg.text,
                "start_time": audio_seg.timing["start_time"],
                "end_time": audio_seg.timing["end_time"],
                "duration": audio_seg.duration,
                "speaker": audio_seg.metadata.get("speaker", ""),
                "display_duration": min(audio_seg.duration, 5.0)  # Max 5 seconds per subtitle
            }
            alignment["subtitle_timings"].append(subtitle_timing)
            
            # Create word-level timings (simplified)
            words = audio_seg.text.split()
            if words:
                word_duration = audio_seg.duration / len(words)
                for i, word in enumerate(words):
                    word_timing = {
                        "word": word,
                        "start_time": audio_seg.timing["start_time"] + (i * word_duration),
                        "end_time": audio_seg.timing["start_time"] + ((i + 1) * word_duration),
                        "segment_id": audio_seg.segment_id
                    }
                    alignment["word_level_timings"].append(word_timing)
            
            # Add emotion markers
            if audio_seg.emotion != "neutral":
                emotion_marker = {
                    "time": audio_seg.timing["start_time"],
                    "emotion": audio_seg.emotion,
                    "segment_id": audio_seg.segment_id,
                    "intensity": self._get_emotion_intensity(audio_seg.emotion)
                }
                alignment["emotion_markers"].append(emotion_marker)
        
        # Detect speaker changes
        current_speaker = None
        for audio_seg in audio_segments:
            speaker = audio_seg.metadata.get("speaker", "")
            if speaker != current_speaker:
                speaker_change = {
                    "time": audio_seg.timing["start_time"],
                    "from_speaker": current_speaker,
                    "to_speaker": speaker,
                    "segment_id": audio_seg.segment_id
                }
                alignment["speaker_changes"].append(speaker_change)
                current_speaker = speaker
        
        return alignment
    
    def _get_emotion_intensity(self, emotion: str) -> float:
        """Get intensity level for emotion (0-1)"""
        intensity_map = {
            "excited": 0.9,
            "happy": 0.7,
            "sad": 0.6,
            "angry": 0.8,
            "surprised": 0.7,
            "fear": 0.6,
            "hate": 0.8,
            "coldness": 0.5,
            "neutral": 0.0
        }
        return intensity_map.get(emotion, 0.5)
    
    async def _create_voice_metadata(self, voice_mappings: List[VoiceMapping], 
                                   audio_segments: List[AudioSegment]) -> Dict[str, Any]:
        """Create comprehensive voice metadata"""
        metadata = {
            "voice_mappings": {},
            "usage_statistics": {},
            "quality_metrics": {},
            "recommendations": []
        }
        
        # Document voice mappings
        for mapping in voice_mappings:
            voice_info = self.voice_library[mapping.voice_id]
            metadata["voice_mappings"][mapping.speaker_name] = {
                "voice_id": mapping.voice_id,
                "voice_name": voice_info["name"],
                "description": voice_info["description"],
                "gender": voice_info["gender"],
                "available_emotions": mapping.emotions,
                "base_config": mapping.base_config
            }
        
        # Calculate usage statistics
        voice_usage = {}
        emotion_usage = {}
        total_duration = 0
        
        for segment in audio_segments:
            # Voice usage
            voice_id = segment.voice_id
            if voice_id not in voice_usage:
                voice_usage[voice_id] = {"count": 0, "duration": 0}
            voice_usage[voice_id]["count"] += 1
            voice_usage[voice_id]["duration"] += segment.duration
            
            # Emotion usage
            emotion = segment.emotion
            if emotion not in emotion_usage:
                emotion_usage[emotion] = {"count": 0, "duration": 0}
            emotion_usage[emotion]["count"] += 1
            emotion_usage[emotion]["duration"] += segment.duration
            
            total_duration += segment.duration
        
        metadata["usage_statistics"] = {
            "total_segments": len(audio_segments),
            "total_duration": total_duration,
            "voice_usage": voice_usage,
            "emotion_usage": emotion_usage,
            "average_segment_duration": total_duration / len(audio_segments) if audio_segments else 0
        }
        
        # Calculate quality metrics
        successful_segments = len([seg for seg in audio_segments if not seg.metadata.get("fallback", False)])
        quality_score = successful_segments / len(audio_segments) if audio_segments else 0
        
        metadata["quality_metrics"] = {
            "success_rate": quality_score,
            "total_segments": len(audio_segments),
            "successful_segments": successful_segments,
            "failed_segments": len(audio_segments) - successful_segments,
            "average_quality": quality_score
        }
        
        # Generate recommendations
        if quality_score < 0.9:
            metadata["recommendations"].append("Consider checking Doubao TTS API connectivity and credentials")
        
        if len(set(seg.emotion for seg in audio_segments)) < 3:
            metadata["recommendations"].append("Consider using more emotional variety for better engagement")
        
        if total_duration > 300:  # 5 minutes
            metadata["recommendations"].append("Consider breaking longer content into multiple parts")
        
        return metadata