"""
Scripting Agent - Generates diverse text content for videos and podcasts
Supports multiple languages, tones, and formats including book reviews, monologues, and multi-speaker podcasts
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from .base_agent import BaseAgent, AgentTask, AgentResult

@dataclass
class ScriptSegment:
    """Represents a segment of the script"""
    id: str
    content: str
    speaker: Optional[str] = None
    emotion: Optional[str] = None
    timing: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None

@dataclass
class ScriptFormat:
    """Defines the format specifications for a script"""
    format_type: str  # "monologue", "dialogue", "podcast", "narrative"
    speakers: List[str]
    style: str
    language: str
    target_duration: float
    structure: List[str]

class ScriptingAgent(BaseAgent):
    """
    Scripting Agent for generating diverse text content
    
    Responsibilities:
    - Generate book reviews with multiple tones and styles
    - Create engaging monologues and dialogues
    - Produce multi-speaker podcast scripts
    - Support multiple languages and cultural contexts
    - Adapt content structure based on target audience
    """
    
    @property
    def agent_type(self) -> str:
        return "scripting"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["optimized_prompt", "content_direction"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "script_content": dict,
            "script_metadata": dict,
            "script_segments": list,
            "audio_instructions": dict
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.content_templates = self._initialize_content_templates()
        self.language_models = self._initialize_language_models()
        self.style_guidelines = self._initialize_style_guidelines()
    
    def _initialize_content_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize content templates for different formats"""
        return {
            "book_review": {
                "structure": [
                    {"section": "opening", "duration": 15, "purpose": "hook_and_introduction"},
                    {"section": "book_overview", "duration": 30, "purpose": "basic_information"},
                    {"section": "deep_analysis", "duration": 60, "purpose": "core_analysis"},
                    {"section": "personal_reflection", "duration": 30, "purpose": "personal_insights"},
                    {"section": "recommendation", "duration": 15, "purpose": "conclusion_and_rating"}
                ],
                "required_elements": ["title", "author", "genre", "key_themes", "personal_opinion"],
                "optional_elements": ["historical_context", "comparison", "quotes", "rating"]
            },
            "character_analysis": {
                "structure": [
                    {"section": "character_introduction", "duration": 20, "purpose": "introduce_character"},
                    {"section": "personality_traits", "duration": 40, "purpose": "analyze_personality"},
                    {"section": "character_development", "duration": 40, "purpose": "trace_growth"},
                    {"section": "relationships", "duration": 30, "purpose": "explore_connections"},
                    {"section": "symbolic_meaning", "duration": 20, "purpose": "deeper_significance"}
                ],
                "required_elements": ["character_name", "key_traits", "development_arc", "relationships"],
                "optional_elements": ["symbolism", "quotes", "comparisons", "author_intent"]
            },
            "podcast_dialogue": {
                "structure": [
                    {"section": "introduction", "duration": 30, "purpose": "welcome_and_topic"},
                    {"section": "main_discussion", "duration": 120, "purpose": "core_content"},
                    {"section": "interaction", "duration": 60, "purpose": "questions_and_debate"},
                    {"section": "conclusion", "duration": 30, "purpose": "summary_and_farewell"}
                ],
                "required_elements": ["topic", "speakers", "key_points", "interactions"],
                "optional_elements": ["guest_introduction", "listener_questions", "recommendations"]
            }
        }
    
    def _initialize_language_models(self) -> Dict[str, Dict[str, Any]]:
        """Initialize language-specific configurations"""
        return {
            "chinese": {
                "formal_markers": ["诸位", "各位", "众所周知", "综上所述", "总而言之"],
                "casual_markers": ["大家好", "咱们", "说实话", "话说回来", "总的来说"],
                "emotional_markers": {
                    "excitement": ["太棒了", "真的很", "不得不说", "简直", "绝对"],
                    "contemplation": ["细想起来", "深入思考", "值得玩味", "令人深思"],
                    "criticism": ["然而", "不过", "遗憾的是", "可惜", "美中不足"]
                },
                "transition_phrases": ["接下来", "另外", "与此同时", "最重要的是", "最后"]
            },
            "english": {
                "formal_markers": ["Furthermore", "Moreover", "In conclusion", "Therefore", "Nevertheless"],
                "casual_markers": ["So", "Well", "You know", "I mean", "Anyway"],
                "emotional_markers": {
                    "excitement": ["Amazing", "Incredible", "Absolutely", "Fantastic", "Brilliant"],
                    "contemplation": ["Interestingly", "Thoughtfully", "Remarkably", "Profoundly"],
                    "criticism": ["However", "Unfortunately", "Regrettably", "Disappointingly"]
                },
                "transition_phrases": ["Moving on", "Additionally", "On the other hand", "Most importantly", "Finally"]
            }
        }
    
    def _initialize_style_guidelines(self) -> Dict[str, Dict[str, Any]]:
        """Initialize style guidelines for different tones"""
        return {
            "academic": {
                "vocabulary_level": "advanced",
                "sentence_structure": "complex",
                "personal_pronouns": "minimal",
                "evidence_requirements": "high",
                "citation_style": "formal"
            },
            "conversational": {
                "vocabulary_level": "accessible",
                "sentence_structure": "varied",
                "personal_pronouns": "frequent",
                "evidence_requirements": "moderate",
                "citation_style": "informal"
            },
            "storytelling": {
                "vocabulary_level": "vivid",
                "sentence_structure": "narrative",
                "personal_pronouns": "as_needed",
                "evidence_requirements": "integrated",
                "citation_style": "embedded"
            },
            "philosophical": {
                "vocabulary_level": "conceptual",
                "sentence_structure": "reflective",
                "personal_pronouns": "contemplative",
                "evidence_requirements": "thoughtful",
                "citation_style": "referenced"
            }
        }
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute script generation"""
        try:
            optimized_prompt = task.input_data["optimized_prompt"]
            content_direction = task.input_data["content_direction"]
            
            self.logger.info("Starting script generation")
            
            # Step 1: Parse content requirements
            script_requirements = await self._parse_content_requirements(content_direction)
            
            # Step 2: Determine script format
            script_format = await self._determine_script_format(script_requirements, content_direction)
            
            # Step 3: Generate script content
            script_content = await self._generate_script_content(optimized_prompt, script_format, script_requirements)
            
            # Step 4: Structure script into segments
            script_segments = await self._create_script_segments(script_content, script_format)
            
            # Step 5: Generate audio instructions
            audio_instructions = await self._create_audio_instructions(script_segments, script_format)
            
            # Step 6: Create metadata
            script_metadata = await self._create_script_metadata(script_format, script_segments, content_direction)
            
            result_data = {
                "script_content": script_content,
                "script_metadata": script_metadata,
                "script_segments": [segment.__dict__ for segment in script_segments],
                "audio_instructions": audio_instructions
            }
            
            self.logger.info(f"Script generation completed. Generated {len(script_segments)} segments")
            
            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    "format_type": script_format.format_type,
                    "total_segments": len(script_segments),
                    "estimated_duration": script_format.target_duration,
                    "language": script_format.language
                }
            )
            
        except Exception as e:
            self.logger.error(f"Script generation failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"Script generation failed: {str(e)}"
            )
    
    async def _parse_content_requirements(self, content_direction: Dict[str, Any]) -> Dict[str, Any]:
        """Parse content direction into script requirements"""
        requirements = {
            "primary_focus": content_direction.get("primary_focus", ""),
            "content_structure": content_direction.get("content_structure", []),
            "style_guidelines": content_direction.get("style_guidelines", {}),
            "key_elements": content_direction.get("key_elements", []),
            "target_audience": content_direction.get("target_audience", "general"),
            "estimated_length": content_direction.get("estimated_length", "medium")
        }
        
        # Determine content type from structure
        structure_sections = requirements["content_structure"]
        if any("书籍" in section for section in structure_sections):
            requirements["content_type"] = "book_review"
        elif any("人物" in section for section in structure_sections):
            requirements["content_type"] = "character_analysis"
        elif any("对话" in section or "访谈" in section for section in structure_sections):
            requirements["content_type"] = "podcast_dialogue"
        else:
            requirements["content_type"] = "general_monologue"
        
        return requirements
    
    async def _determine_script_format(self, requirements: Dict[str, Any], 
                                     content_direction: Dict[str, Any]) -> ScriptFormat:
        """Determine the appropriate script format"""
        content_type = requirements["content_type"]
        style = requirements["style_guidelines"].get("tone", "conversational")
        
        # Determine format type and speakers
        if content_type == "podcast_dialogue":
            format_type = "dialogue"
            speakers = ["主持人", "嘉宾"] if style != "academic" else ["学者A", "学者B"]
        else:
            format_type = "monologue"
            speakers = ["旁白者"] if style == "storytelling" else ["评论员"]
        
        # Calculate target duration
        duration_map = {"short": 120, "medium": 180, "long": 240}
        target_duration = duration_map.get(requirements["estimated_length"], 180)
        
        return ScriptFormat(
            format_type=format_type,
            speakers=speakers,
            style=style,
            language="chinese",
            target_duration=target_duration,
            structure=requirements["content_structure"]
        )
    
    async def _generate_script_content(self, optimized_prompt: str, 
                                     script_format: ScriptFormat,
                                     requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Generate the actual script content"""
        
        # This would integrate with Doubao API for content generation
        # For now, using structured template-based generation
        
        content_type = requirements["content_type"]
        template = self.content_templates.get(content_type, self.content_templates["book_review"])
        
        script_content = {
            "title": f"关于《{requirements['primary_focus']}》的深度解读",
            "format": script_format.format_type,
            "language": script_format.language,
            "sections": {}
        }
        
        # Generate content for each section
        for section_info in template["structure"]:
            section_name = section_info["section"]
            section_content = await self._generate_section_content(
                section_info, requirements, script_format, optimized_prompt
            )
            script_content["sections"][section_name] = section_content
        
        return script_content
    
    async def _generate_section_content(self, section_info: Dict[str, Any],
                                      requirements: Dict[str, Any],
                                      script_format: ScriptFormat,
                                      optimized_prompt: str) -> Dict[str, Any]:
        """Generate content for a specific section"""
        
        section_name = section_info["section"]
        purpose = section_info["purpose"]
        duration = section_info["duration"]
        
        # Calculate word count based on duration (assuming ~2 characters per second for Chinese)
        target_word_count = int(duration * 2)
        
        # Template-based content generation (would be replaced with AI generation)
        content_templates = {
            "opening": f"大家好，今天我们来聊聊《{requirements['primary_focus']}》这本书。这是一部值得深入探讨的作品。",
            "book_overview": f"《{requirements['primary_focus']}》是一部具有深远影响的文学作品。作者通过精湛的写作技巧，为我们呈现了一个引人深思的故事世界。",
            "deep_analysis": f"深入分析《{requirements['primary_focus']}》，我们可以发现作品中蕴含的丰富主题。作者巧妙地运用各种文学手法，构建了一个层次丰富的叙事结构。",
            "personal_reflection": f"读完《{requirements['primary_focus']}》，我深深被作品的深度所震撼。这本书不仅仅是一个故事，更是对人性和社会的深刻反思。",
            "recommendation": f"总的来说，《{requirements['primary_focus']}》是一本值得推荐的优秀作品。无论是文学爱好者还是普通读者，都能从中获得宝贵的启发。"
        }
        
        base_content = content_templates.get(section_name, f"关于{section_name}的内容。")
        
        # Adjust content based on style
        styled_content = await self._apply_style_adjustments(base_content, script_format.style)
        
        # Add speaker assignment for dialogue format
        if script_format.format_type == "dialogue":
            speaker = script_format.speakers[hash(section_name) % len(script_format.speakers)]
        else:
            speaker = script_format.speakers[0]
        
        return {
            "content": styled_content,
            "speaker": speaker,
            "duration": duration,
            "word_count": len(styled_content),
            "purpose": purpose,
            "emotion": self._determine_section_emotion(section_name, script_format.style)
        }
    
    async def _apply_style_adjustments(self, content: str, style: str) -> str:
        """Apply style-specific adjustments to content"""
        style_guide = self.style_guidelines.get(style, self.style_guidelines["conversational"])
        language_config = self.language_models["chinese"]
        
        # Add style-appropriate markers
        if style == "academic":
            markers = language_config["formal_markers"]
            content = f"{markers[0]}，{content}"
        elif style == "conversational":
            markers = language_config["casual_markers"]
            content = f"{markers[0]}，{content}"
        
        return content
    
    def _determine_section_emotion(self, section_name: str, style: str) -> str:
        """Determine appropriate emotion for section"""
        emotion_map = {
            "opening": "neutral" if style == "academic" else "happy",
            "book_overview": "neutral",
            "deep_analysis": "contemplative",
            "personal_reflection": "emotional",
            "recommendation": "enthusiastic",
            "character_introduction": "neutral",
            "personality_traits": "analytical",
            "relationships": "warm",
            "conclusion": "satisfied"
        }
        
        return emotion_map.get(section_name, "neutral")
    
    async def _create_script_segments(self, script_content: Dict[str, Any], 
                                    script_format: ScriptFormat) -> List[ScriptSegment]:
        """Create structured script segments for audio generation"""
        segments = []
        segment_id = 0
        
        for section_name, section_data in script_content["sections"].items():
            segment = ScriptSegment(
                id=f"seg_{segment_id:03d}",
                content=section_data["content"],
                speaker=section_data["speaker"],
                emotion=section_data["emotion"],
                timing={
                    "duration": section_data["duration"],
                    "start_time": sum(s.timing["duration"] for s in segments) if segments else 0
                },
                metadata={
                    "section_name": section_name,
                    "purpose": section_data["purpose"],
                    "word_count": section_data["word_count"]
                }
            )
            segments.append(segment)
            segment_id += 1
        
        return segments
    
    async def _create_audio_instructions(self, segments: List[ScriptSegment], 
                                       script_format: ScriptFormat) -> Dict[str, Any]:
        """Create instructions for audio generation"""
        return {
            "voice_assignments": {
                speaker: {
                    "voice_type": self._get_voice_for_speaker(speaker, script_format.style),
                    "emotion_range": self._get_emotion_range_for_speaker(speaker, script_format.style)
                }
                for speaker in script_format.speakers
            },
            "pacing": {
                "overall_speed": self._get_pacing_for_style(script_format.style),
                "pause_durations": {
                    "section_break": 1.0,
                    "sentence_break": 0.3,
                    "comma_break": 0.2
                }
            },
            "emphasis": {
                "key_terms": [segment.metadata.get("section_name", "") for segment in segments],
                "emotional_peaks": [seg.id for seg in segments if seg.emotion in ["emotional", "enthusiastic"]]
            },
            "audio_effects": {
                "background_music": script_format.style in ["storytelling", "conversational"],
                "fade_transitions": True,
                "voice_modulation": script_format.format_type == "dialogue"
            }
        }
    
    def _get_voice_for_speaker(self, speaker: str, style: str) -> str:
        """Get appropriate voice type for speaker and style"""
        voice_map = {
            ("主持人", "conversational"): "zh_female_roumeinvyou_emo_v2_mars_bigtts",
            ("嘉宾", "conversational"): "zh_male_jingqiangnansheng_emo_v2_mars_bigtts", 
            ("评论员", "academic"): "zh_female_wennuandashu_emo_v2_mars_bigtts",
            ("旁白者", "storytelling"): "zh_female_qingchunvyou_emo_v2_mars_bigtts"
        }
        
        return voice_map.get((speaker, style), "zh_female_roumeinvyou_emo_v2_mars_bigtts")
    
    def _get_emotion_range_for_speaker(self, speaker: str, style: str) -> List[str]:
        """Get emotion range for speaker based on style"""
        if style == "academic":
            return ["neutral", "contemplative", "analytical"]
        elif style == "conversational":
            return ["neutral", "happy", "excited", "thoughtful"]
        elif style == "storytelling":
            return ["neutral", "mysterious", "warm", "dramatic"]
        else:
            return ["neutral", "contemplative", "thoughtful"]
    
    def _get_pacing_for_style(self, style: str) -> float:
        """Get speaking pace for style (words per minute)"""
        pace_map = {
            "academic": 0.8,      # Slower, more deliberate
            "conversational": 1.0, # Normal pace
            "storytelling": 0.9,   # Slightly slower for emphasis
            "philosophical": 0.85   # Contemplative pace
        }
        return pace_map.get(style, 1.0)
    
    async def _create_script_metadata(self, script_format: ScriptFormat,
                                    segments: List[ScriptSegment],
                                    content_direction: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive metadata for the script"""
        total_duration = sum(seg.timing["duration"] for seg in segments)
        total_words = sum(len(seg.content) for seg in segments)
        
        return {
            "script_info": {
                "format_type": script_format.format_type,
                "language": script_format.language,
                "style": script_format.style,
                "total_segments": len(segments),
                "total_duration": total_duration,
                "total_words": total_words
            },
            "content_analysis": {
                "primary_focus": content_direction.get("primary_focus", ""),
                "key_themes": [elem["element"] for elem in content_direction.get("key_elements", [])],
                "structure_sections": len(content_direction.get("content_structure", [])),
                "complexity_level": self._assess_content_complexity(segments)
            },
            "production_notes": {
                "speakers_required": len(script_format.speakers),
                "voice_changes": len(set(seg.speaker for seg in segments)),
                "emotion_variations": len(set(seg.emotion for seg in segments)),
                "recommended_background_music": script_format.style in ["storytelling", "conversational"]
            },
            "quality_metrics": {
                "readability_score": self._calculate_readability_score(segments),
                "engagement_score": self._calculate_engagement_score(segments),
                "coherence_score": self._calculate_coherence_score(segments)
            }
        }
    
    def _assess_content_complexity(self, segments: List[ScriptSegment]) -> str:
        """Assess the complexity level of the content"""
        avg_word_count = sum(len(seg.content) for seg in segments) / len(segments)
        
        if avg_word_count > 100:
            return "high"
        elif avg_word_count > 50:
            return "medium"
        else:
            return "low"
    
    def _calculate_readability_score(self, segments: List[ScriptSegment]) -> float:
        """Calculate readability score (0-1)"""
        # Simplified readability calculation
        total_chars = sum(len(seg.content) for seg in segments)
        total_segments = len(segments)
        avg_length = total_chars / total_segments if total_segments > 0 else 0
        
        # Optimal range for Chinese audio content is 60-80 characters per segment
        if 60 <= avg_length <= 80:
            return 1.0
        elif 40 <= avg_length <= 100:
            return 0.8
        else:
            return 0.6
    
    def _calculate_engagement_score(self, segments: List[ScriptSegment]) -> float:
        """Calculate engagement score based on emotional variety"""
        unique_emotions = len(set(seg.emotion for seg in segments))
        max_emotions = 5  # Maximum expected emotion variety
        return min(1.0, unique_emotions / max_emotions)
    
    def _calculate_coherence_score(self, segments: List[ScriptSegment]) -> float:
        """Calculate coherence score based on structure"""
        # Simple coherence measure based on consistent progression
        if len(segments) < 3:
            return 0.5
        
        # Check for logical progression (introduction -> development -> conclusion)
        has_intro = any("opening" in seg.metadata.get("section_name", "") for seg in segments[:2])
        has_conclusion = any("recommendation" in seg.metadata.get("section_name", "") or 
                           "conclusion" in seg.metadata.get("section_name", "") for seg in segments[-2:])
        
        score = 0.6  # Base score
        if has_intro:
            score += 0.2
        if has_conclusion:
            score += 0.2
        
        return score