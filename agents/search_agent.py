"""
Search Agent - Searches for concepts and nouns mentioned by the user
Performs deep and parallel expansion, infers true intent, and prepares semantic context
"""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from .base_agent import BaseAgent, AgentTask, AgentResult
import json
import re

@dataclass
class SearchResult:
    """Represents a search result"""
    concept: str
    description: str
    relevance_score: float
    related_concepts: List[str]
    source: str
    metadata: Dict[str, Any]

@dataclass
class ConceptExpansion:
    """Represents expanded concept information"""
    original_concept: str
    expanded_concepts: List[str]
    semantic_context: Dict[str, Any]
    relationships: Dict[str, List[str]]

class SearchAgent(BaseAgent):
    """
    Search Agent for concept discovery and semantic expansion
    
    Responsibilities:
    - Extract key concepts and nouns from user input
    - Perform deep semantic expansion
    - Infer user's true intent
    - Prepare rich context for downstream agents
    """
    
    @property
    def agent_type(self) -> str:
        return "search"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["user_input", "domain_context"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "extracted_concepts": list,
            "expanded_concepts": list,
            "semantic_context": dict,
            "user_intent": dict,
            "search_results": list,
            "related_topics": list
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.search_engines = self._initialize_search_engines()
        self.concept_extractors = self._initialize_concept_extractors()
    
    def _initialize_search_engines(self) -> Dict[str, Any]:
        """Initialize various search engines and APIs"""
        return {
            "web_search": {
                "enabled": self.config.get("web_search_enabled", True),
                "api_key": self.config.get("web_search_api_key"),
                "max_results": self.config.get("max_web_results", 10)
            },
            "knowledge_graph": {
                "enabled": self.config.get("knowledge_graph_enabled", True),
                "api_key": self.config.get("knowledge_graph_api_key")
            },
            "semantic_search": {
                "enabled": self.config.get("semantic_search_enabled", True),
                "model": self.config.get("semantic_model", "text-embedding-ada-002")
            }
        }
    
    def _initialize_concept_extractors(self) -> Dict[str, Any]:
        """Initialize concept extraction tools"""
        return {
            "nlp_extractor": {
                "enabled": True,
                "language": self.config.get("language", "zh")
            },
            "entity_recognizer": {
                "enabled": True,
                "types": ["PERSON", "ORG", "GPE", "WORK_OF_ART", "EVENT"]
            }
        }
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute the search agent's main workflow"""
        try:
            user_input = task.input_data["user_input"]
            domain_context = task.input_data.get("domain_context", {})
            
            self.logger.info(f"Starting search for: {user_input}")
            
            # Step 1: Extract key concepts
            extracted_concepts = await self._extract_concepts(user_input)
            
            # Step 2: Infer user intent
            user_intent = await self._infer_user_intent(user_input, extracted_concepts)
            
            # Step 3: Perform parallel concept expansion
            expanded_concepts = await self._expand_concepts_parallel(extracted_concepts, domain_context)
            
            # Step 4: Build semantic context
            semantic_context = await self._build_semantic_context(extracted_concepts, expanded_concepts, user_intent)
            
            # Step 5: Search for detailed information
            search_results = await self._perform_comprehensive_search(extracted_concepts, expanded_concepts)
            
            # Step 6: Generate related topics
            related_topics = await self._generate_related_topics(semantic_context, search_results)
            
            result_data = {
                "extracted_concepts": extracted_concepts,
                "expanded_concepts": expanded_concepts,
                "semantic_context": semantic_context,
                "user_intent": user_intent,
                "search_results": search_results,
                "related_topics": related_topics
            }
            
            self.logger.info(f"Search completed. Found {len(extracted_concepts)} concepts, {len(search_results)} results")
            
            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    "concepts_count": len(extracted_concepts),
                    "results_count": len(search_results),
                    "intent_confidence": user_intent.get("confidence", 0.0)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Search execution failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"Search execution failed: {str(e)}"
            )
    
    async def _extract_concepts(self, user_input: str) -> List[str]:
        """Extract key concepts and entities from user input"""
        concepts = []
        
        # Basic keyword extraction
        # Remove common stop words and extract meaningful terms
        text = user_input.lower()
        
        # Extract book titles (text in Chinese quotes)
        book_titles = re.findall(r'《([^》]+)》', user_input)
        concepts.extend(book_titles)
        
        # Extract author names (pattern: 作者: name or - name)
        author_patterns = [r'作者[：:]\\s*([^，。\\n]+)', r'-\\s*([^，。\\n]+)']
        for pattern in author_patterns:
            authors = re.findall(pattern, user_input)
            concepts.extend(authors)
        
        # Extract general nouns and important terms
        # This is a simplified version - in production, use proper NLP
        important_terms = re.findall(r'[\\u4e00-\\u9fff]{2,}', user_input)
        
        # Filter and deduplicate
        for term in important_terms:
            if len(term) >= 2 and term not in concepts:
                concepts.append(term)
        
        return list(set(concepts))[:20]  # Limit to top 20 concepts
    
    async def _infer_user_intent(self, user_input: str, concepts: List[str]) -> Dict[str, Any]:
        """Infer the user's true intent from input and concepts"""
        intent = {
            "primary_intent": "unknown",
            "confidence": 0.0,
            "intent_categories": [],
            "content_type": "unknown",
            "target_audience": "general"
        }
        
        # Analyze intent patterns
        input_lower = user_input.lower()
        
        if any(word in input_lower for word in ["书评", "推荐", "评价", "介绍"]):
            intent["primary_intent"] = "book_review"
            intent["confidence"] = 0.9
            intent["content_type"] = "review"
        elif any(word in input_lower for word in ["播客", "对话", "访谈", "聊天"]):
            intent["primary_intent"] = "podcast_creation"
            intent["confidence"] = 0.8
            intent["content_type"] = "dialogue"
        elif any(word in input_lower for word in ["视频", "短片", "影片"]):
            intent["primary_intent"] = "video_creation"
            intent["confidence"] = 0.8
            intent["content_type"] = "video"
        else:
            intent["primary_intent"] = "general_content"
            intent["confidence"] = 0.5
        
        # Determine content categories
        if any(concept in ["小王子", "活着", "哈利波特"] for concept in concepts):
            intent["intent_categories"].append("literature")
        if any(word in input_lower for word in ["教育", "学习", "知识"]):
            intent["intent_categories"].append("educational")
        if any(word in input_lower for word in ["娱乐", "有趣", "幽默"]):
            intent["intent_categories"].append("entertainment")
        
        return intent
    
    async def _expand_concepts_parallel(self, concepts: List[str], domain_context: Dict[str, Any]) -> List[ConceptExpansion]:
        """Perform parallel expansion of concepts"""
        tasks = []
        
        for concept in concepts:
            task = self._expand_single_concept(concept, domain_context)
            tasks.append(task)
        
        expansions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and return valid expansions
        valid_expansions = []
        for expansion in expansions:
            if isinstance(expansion, ConceptExpansion):
                valid_expansions.append(expansion)
            else:
                self.logger.warning(f"Concept expansion failed: {expansion}")
        
        return valid_expansions
    
    async def _expand_single_concept(self, concept: str, domain_context: Dict[str, Any]) -> ConceptExpansion:
        """Expand a single concept with related information"""
        # This is a simplified implementation
        # In production, this would use proper knowledge graphs and semantic models
        
        expanded_concepts = []
        relationships = {}
        semantic_context = {}
        
        # Basic expansion based on known patterns
        if "小王子" in concept:
            expanded_concepts = ["安托万·德·圣埃克苏佩里", "法国文学", "童话", "哲学", "友谊", "玫瑰", "狐狸", "飞行员"]
            relationships = {
                "author": ["安托万·德·圣埃克苏佩里"],
                "characters": ["小王子", "狐狸", "玫瑰", "飞行员"],
                "themes": ["友谊", "爱情", "责任", "成长"]
            }
        elif "活着" in concept:
            expanded_concepts = ["余华", "中国当代文学", "农村生活", "苦难", "生存", "人性", "历史"]
            relationships = {
                "author": ["余华"],
                "characters": ["福贵", "家珍", "有庆", "凤霞"],
                "themes": ["生存", "苦难", "人性", "历史变迁"]
            }
        else:
            # Generic expansion
            expanded_concepts = [f"相关_{concept}", f"{concept}_背景", f"{concept}_历史"]
        
        semantic_context = {
            "domain": domain_context.get("domain", "literature"),
            "language": "zh",
            "cultural_context": "chinese",
            "time_period": "contemporary"
        }
        
        return ConceptExpansion(
            original_concept=concept,
            expanded_concepts=expanded_concepts,
            semantic_context=semantic_context,
            relationships=relationships
        )
    
    async def _build_semantic_context(self, extracted_concepts: List[str], 
                                    expanded_concepts: List[ConceptExpansion], 
                                    user_intent: Dict[str, Any]) -> Dict[str, Any]:
        """Build comprehensive semantic context"""
        context = {
            "primary_domain": "literature",
            "language": "chinese",
            "content_style": user_intent.get("content_type", "general"),
            "key_themes": [],
            "cultural_context": "chinese_literature",
            "target_format": user_intent.get("primary_intent", "general_content"),
            "concept_relationships": {},
            "semantic_clusters": []
        }
        
        # Extract themes from expanded concepts
        all_themes = []
        for expansion in expanded_concepts:
            if "themes" in expansion.relationships:
                all_themes.extend(expansion.relationships["themes"])
        
        context["key_themes"] = list(set(all_themes))
        
        # Build concept relationships
        for expansion in expanded_concepts:
            context["concept_relationships"][expansion.original_concept] = expansion.relationships
        
        return context
    
    async def _perform_comprehensive_search(self, extracted_concepts: List[str], 
                                          expanded_concepts: List[ConceptExpansion]) -> List[SearchResult]:
        """Perform comprehensive search across multiple sources"""
        all_results = []
        
        # Search for each concept
        for concept in extracted_concepts[:5]:  # Limit to top 5 concepts
            results = await self._search_concept(concept)
            all_results.extend(results)
        
        # Sort by relevance and deduplicate
        unique_results = self._deduplicate_results(all_results)
        sorted_results = sorted(unique_results, key=lambda x: x.relevance_score, reverse=True)
        
        return sorted_results[:20]  # Return top 20 results
    
    async def _search_concept(self, concept: str) -> List[SearchResult]:
        """Search for information about a specific concept"""
        results = []
        
        # Mock search results (in production, use real search APIs)
        if "小王子" in concept:
            results.append(SearchResult(
                concept=concept,
                description="《小王子》是法国作家安托万·德·圣埃克苏佩里创作的童话，讲述了一个来自小行星的小王子在宇宙中旅行的故事。",
                relevance_score=0.95,
                related_concepts=["安托万·德·圣埃克苏佩里", "童话", "哲学", "友谊"],
                source="literature_database",
                metadata={"publication_year": 1943, "language": "french", "genre": "童话"}
            ))
        elif "活着" in concept:
            results.append(SearchResult(
                concept=concept,
                description="《活着》是余华创作的长篇小说，以中国内战和新中国成立后历次政治运动为背景，讲述了福贵的人生经历。",
                relevance_score=0.93,
                related_concepts=["余华", "中国文学", "历史", "苦难"],
                source="literature_database",
                metadata={"publication_year": 1993, "language": "chinese", "genre": "现实主义小说"}
            ))
        else:
            # Generic result
            results.append(SearchResult(
                concept=concept,
                description=f"关于{concept}的详细信息和背景资料。",
                relevance_score=0.7,
                related_concepts=[],
                source="general_search",
                metadata={}
            ))
        
        return results
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate search results"""
        seen_concepts = set()
        unique_results = []
        
        for result in results:
            if result.concept not in seen_concepts:
                seen_concepts.add(result.concept)
                unique_results.append(result)
        
        return unique_results
    
    async def _generate_related_topics(self, semantic_context: Dict[str, Any], 
                                     search_results: List[SearchResult]) -> List[Dict[str, Any]]:
        """Generate related topics for user selection"""
        topics = []
        
        # Extract topics from search results
        for result in search_results:
            for related_concept in result.related_concepts:
                topic = {
                    "topic": related_concept,
                    "description": f"探索{related_concept}的相关内容",
                    "relevance_score": result.relevance_score * 0.8,
                    "source_concept": result.concept,
                    "suggested_content_types": ["book_review", "analysis", "background"]
                }
                topics.append(topic)
        
        # Add thematic topics
        for theme in semantic_context.get("key_themes", []):
            topic = {
                "topic": theme,
                "description": f"深入探讨{theme}这一主题",
                "relevance_score": 0.8,
                "source_concept": "thematic_analysis",
                "suggested_content_types": ["thematic_analysis", "philosophical_discussion"]
            }
            topics.append(topic)
        
        # Sort and deduplicate
        unique_topics = {topic["topic"]: topic for topic in topics}
        sorted_topics = sorted(unique_topics.values(), key=lambda x: x["relevance_score"], reverse=True)
        
        return sorted_topics[:15]  # Return top 15 topics