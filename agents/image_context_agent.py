"""
Image Context Agent - Generates contextual background images based on script content
Analyzes script themes and creates dynamic visual content that matches the narrative flow
"""

import asyncio
import aiohttp
import json
import base64
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from .base_agent import BaseAgent, AgentTask, AgentResult

@dataclass
class ImageGenerationRequest:
    """Represents an image generation request"""
    prompt: str
    style: str
    context: str
    timing: Dict[str, float]
    metadata: Dict[str, Any]

@dataclass
class GeneratedImage:
    """Represents a generated image with metadata"""
    image_id: str
    image_data: bytes
    prompt: str
    style: str
    context: str
    timing: Dict[str, float]
    dimensions: Tuple[int, int]
    metadata: Dict[str, Any]

@dataclass
class ImageTransition:
    """Represents a transition between images"""
    from_image_id: str
    to_image_id: str
    transition_time: float
    transition_type: str
    duration: float

class ImageContextAgent(BaseAgent):
    """
    Image Context Agent for generating contextual visual content
    
    Responsibilities:
    - Analyze script content for visual context
    - Generate thematic background images
    - Create dynamic image sequences
    - Plan image transitions based on narrative flow
    - Integrate with <PERSON><PERSON><PERSON>dream for high-quality generation
    """
    
    @property
    def agent_type(self) -> str:
        return "image_context"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["script_content", "selected_concepts"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "background_images": list,
            "cover_image": dict,
            "image_timeline": dict,
            "visual_metadata": dict
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.doubao_seedream_config = self._initialize_doubao_seedream_config()
        self.visual_styles = self._initialize_visual_styles()
        self.context_analyzers = self._initialize_context_analyzers()
        self.image_cache = {}
    
    def _initialize_doubao_seedream_config(self) -> Dict[str, Any]:
        """Initialize Doubao Seedream configuration"""
        return {
            "api_url": self.config.get("doubao_seedream_api_url", "https://ark.cn-beijing.volces.com/api/v3/models/model/text2image"),
            "api_key": self.config.get("doubao_seedream_api_key", ""),
            "model": self.config.get("doubao_seedream_model", "doubao-seedream-3"),
            "default_size": "1024x576",  # 16:9 aspect ratio for video
            "quality": "hd",
            "style": "natural",
            "steps": 30,
            "guidance_scale": 7.5
        }
    
    def _initialize_visual_styles(self) -> Dict[str, Dict[str, Any]]:
        """Initialize visual style configurations"""
        return {
            "literary_classic": {
                "style_prompt": "经典文学插画风格，优雅细腻，富有诗意",
                "color_palette": "warm_earth_tones",
                "composition": "balanced_classical",
                "lighting": "soft_natural",
                "mood": "contemplative"
            },
            "modern_minimalist": {
                "style_prompt": "现代简约风格，干净利落，色彩柔和",
                "color_palette": "minimal_pastels",
                "composition": "geometric_clean",
                "lighting": "even_diffused",
                "mood": "calm_focused"
            },
            "atmospheric_cinematic": {
                "style_prompt": "电影级画面，富有层次感和戏剧性",
                "color_palette": "cinematic_contrast",
                "composition": "dynamic_framing",
                "lighting": "dramatic_chiaroscuro",
                "mood": "emotional_depth"
            },
            "watercolor_artistic": {
                "style_prompt": "水彩艺术风格，流动自然，色彩融合",
                "color_palette": "soft_watercolor",
                "composition": "organic_flowing",
                "lighting": "translucent_soft",
                "mood": "dreamy_peaceful"
            },
            "abstract_conceptual": {
                "style_prompt": "抽象概念艺术，富有象征意义",
                "color_palette": "symbolic_contrast",
                "composition": "conceptual_abstract",
                "lighting": "mood_symbolic",
                "mood": "thought_provoking"
            }
        }
    
    def _initialize_context_analyzers(self) -> Dict[str, Any]:
        """Initialize context analysis patterns"""
        return {
            "character_patterns": {
                "小王子": ["小行星", "玫瑰花", "狐狸", "飞行员", "沙漠", "星空"],
                "狐狸": ["森林", "黄昏", "麦田", "友谊", "驯服"],
                "活着": ["农村", "田野", "家庭", "历史", "生存", "苦难"],
                "福贵": ["农民", "耕作", "家庭", "变迁"],
                "哈利波特": ["魔法", "城堡", "魔法世界", "冒险"],
                "霍格沃茨": ["古堡", "魔法学校", "神秘"]
            },
            "theme_patterns": {
                "友谊": ["温暖光线", "并肩", "信任", "陪伴"],
                "成长": ["道路", "远方", "蜕变", "选择"],
                "爱情": ["浪漫", "温柔", "守护", "相伴"],
                "生存": ["坚韧", "希望", "挣扎", "坚持"],
                "哲学": ["思考", "深度", "抽象", "智慧"],
                "历史": ["时代", "变迁", "记忆", "传承"]
            },
            "mood_patterns": {
                "happy": ["明亮", "温暖", "活力", "色彩丰富"],
                "contemplative": ["柔和", "深沉", "静谧", "层次丰富"],
                "melancholy": ["淡雅", "模糊", "忧郁", "单色调"],
                "dramatic": ["对比强烈", "戏剧性", "张力", "冲击力"],
                "peaceful": ["和谐", "平衡", "自然", "舒缓"]
            }
        }
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute image generation workflow"""
        try:
            script_content = task.input_data["script_content"]
            selected_concepts = task.input_data["selected_concepts"]
            
            self.logger.info("Starting contextual image generation")
            
            # Step 1: Analyze script context for visual cues
            visual_context = await self._analyze_visual_context(script_content, selected_concepts)
            
            # Step 2: Plan image sequence based on script timeline
            image_sequence_plan = await self._plan_image_sequence(visual_context, script_content)
            
            # Step 3: Generate background images
            background_images = await self._generate_background_images(image_sequence_plan)
            
            # Step 4: Generate cover image
            cover_image = await self._generate_cover_image(visual_context, selected_concepts)
            
            # Step 5: Create image timeline with transitions
            image_timeline = await self._create_image_timeline(background_images, script_content)
            
            # Step 6: Generate visual metadata
            visual_metadata = await self._create_visual_metadata(
                background_images, cover_image, visual_context, image_timeline
            )
            
            result_data = {
                "background_images": [img.__dict__ for img in background_images],
                "cover_image": cover_image.__dict__ if cover_image else None,
                "image_timeline": image_timeline,
                "visual_metadata": visual_metadata
            }
            
            self.logger.info(f"Image generation completed. Generated {len(background_images)} background images")
            
            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    "total_images": len(background_images) + (1 if cover_image else 0),
                    "visual_themes": len(visual_context.get("themes", [])),
                    "style_used": visual_context.get("primary_style", "unknown"),
                    "generation_quality": self._assess_generation_quality(background_images)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Image generation failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"Image generation failed: {str(e)}"
            )
    
    async def _analyze_visual_context(self, script_content: Dict[str, Any], 
                                    selected_concepts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze script content for visual context cues"""
        context = {
            "primary_themes": [],
            "visual_elements": [],
            "mood_progression": [],
            "character_contexts": [],
            "symbolic_elements": [],
            "primary_style": "literary_classic",
            "color_scheme": "warm_natural"
        }
        
        # Extract themes from selected concepts
        for concept in selected_concepts:
            concept_name = concept.get("concept", "")
            
            # Check for character patterns
            for character, elements in self.context_analyzers["character_patterns"].items():
                if character in concept_name:
                    context["character_contexts"].append({
                        "character": character,
                        "visual_elements": elements,
                        "priority": concept.get("priority", 1)
                    })
            
            # Extract thematic elements
            for theme, elements in self.context_analyzers["theme_patterns"].items():
                if theme in concept_name or any(theme in str(concept) for concept in selected_concepts):
                    context["primary_themes"].append({
                        "theme": theme,
                        "visual_cues": elements,
                        "relevance": concept.get("priority", 1)
                    })
        
        # Analyze script sections for mood progression
        if "sections" in script_content:
            for section_name, section_data in script_content["sections"].items():
                emotion = section_data.get("emotion", "neutral")
                mood_elements = self.context_analyzers["mood_patterns"].get(emotion, [])
                
                context["mood_progression"].append({
                    "section": section_name,
                    "emotion": emotion,
                    "visual_mood": mood_elements,
                    "duration": section_data.get("duration", 30)
                })
        
        # Determine primary visual style based on content
        context["primary_style"] = await self._determine_visual_style(context, script_content)
        
        # Extract symbolic elements
        context["symbolic_elements"] = await self._extract_symbolic_elements(context, selected_concepts)
        
        return context
    
    async def _determine_visual_style(self, context: Dict[str, Any], 
                                    script_content: Dict[str, Any]) -> str:
        """Determine the most appropriate visual style"""
        
        # Style selection based on content characteristics
        character_contexts = context.get("character_contexts", [])
        primary_themes = context.get("primary_themes", [])
        
        # Check for specific character-based styles
        if any("小王子" in char["character"] for char in character_contexts):
            return "watercolor_artistic"
        elif any("活着" in char["character"] for char in character_contexts):
            return "atmospheric_cinematic"
        elif any("哈利波特" in char["character"] for char in character_contexts):
            return "modern_minimalist"
        
        # Check for theme-based styles
        philosophical_themes = ["哲学", "思考", "智慧"]
        if any(theme["theme"] in philosophical_themes for theme in primary_themes):
            return "abstract_conceptual"
        
        # Default to literary classic
        return "literary_classic"
    
    async def _extract_symbolic_elements(self, context: Dict[str, Any], 
                                       selected_concepts: List[Dict[str, Any]]) -> List[str]:
        """Extract symbolic visual elements"""
        symbolic_elements = []
        
        # Common symbolic mappings
        symbol_map = {
            "友谊": ["握手", "并肩行走", "温暖光芒"],
            "成长": ["萌芽", "道路", "蝴蝶", "阶梯"],
            "爱情": ["玫瑰", "心形", "拥抱", "守护"],
            "生存": ["根系", "石头", "流水", "火焰"],
            "时间": ["沙漏", "钟表", "季节变化", "年轮"],
            "希望": ["曙光", "灯塔", "种子", "彩虹"]
        }
        
        for concept in selected_concepts:
            concept_name = concept.get("concept", "")
            for symbol_key, symbols in symbol_map.items():
                if symbol_key in concept_name:
                    symbolic_elements.extend(symbols)
        
        return list(set(symbolic_elements))  # Remove duplicates
    
    async def _plan_image_sequence(self, visual_context: Dict[str, Any], 
                                 script_content: Dict[str, Any]) -> List[ImageGenerationRequest]:
        """Plan the sequence of images based on script timeline"""
        image_requests = []
        
        # Calculate optimal image change intervals (10-20 seconds)
        total_duration = sum(
            section.get("duration", 30) 
            for section in script_content.get("sections", {}).values()
        )
        
        # Plan 4-6 images for typical content
        target_images = min(6, max(3, int(total_duration / 15)))
        image_interval = total_duration / target_images
        
        current_time = 0
        image_id = 0
        
        mood_progression = visual_context.get("mood_progression", [])
        character_contexts = visual_context.get("character_contexts", [])
        primary_style = visual_context.get("primary_style", "literary_classic")
        
        for i in range(target_images):
            # Determine context for this time period
            relevant_mood = self._get_mood_at_time(current_time, mood_progression)
            relevant_characters = self._get_characters_for_segment(i, character_contexts)
            
            # Generate prompt for this image
            prompt = await self._generate_image_prompt(
                relevant_mood, relevant_characters, visual_context, i, target_images
            )
            
            image_request = ImageGenerationRequest(
                prompt=prompt,
                style=primary_style,
                context=f"segment_{i}",
                timing={
                    "start_time": current_time,
                    "end_time": current_time + image_interval,
                    "duration": image_interval
                },
                metadata={
                    "sequence_index": i,
                    "total_images": target_images,
                    "mood": relevant_mood,
                    "characters": relevant_characters
                }
            )
            image_requests.append(image_request)
            
            current_time += image_interval
            image_id += 1
        
        return image_requests
    
    def _get_mood_at_time(self, time: float, mood_progression: List[Dict[str, Any]]) -> str:
        """Get the dominant mood at a specific time"""
        current_time = 0
        for mood_data in mood_progression:
            duration = mood_data.get("duration", 30)
            if current_time <= time < current_time + duration:
                return mood_data.get("emotion", "neutral")
            current_time += duration
        
        return "neutral"  # Default
    
    def _get_characters_for_segment(self, segment_index: int, 
                                  character_contexts: List[Dict[str, Any]]) -> List[str]:
        """Get relevant characters for a specific segment"""
        if not character_contexts:
            return []
        
        # Distribute characters across segments
        characters_per_segment = max(1, len(character_contexts) // 3)
        start_idx = (segment_index * characters_per_segment) % len(character_contexts)
        end_idx = min(start_idx + characters_per_segment, len(character_contexts))
        
        return [ctx["character"] for ctx in character_contexts[start_idx:end_idx]]
    
    async def _generate_image_prompt(self, mood: str, characters: List[str], 
                                   visual_context: Dict[str, Any], 
                                   segment_index: int, total_segments: int) -> str:
        """Generate detailed prompt for image generation"""
        
        style_config = self.visual_styles[visual_context["primary_style"]]
        base_style = style_config["style_prompt"]
        
        # Build prompt components
        prompt_parts = []
        
        # Add style foundation
        prompt_parts.append(base_style)
        
        # Add character-specific elements
        if characters:
            character_elements = []
            for character in characters:
                if character in self.context_analyzers["character_patterns"]:
                    elements = self.context_analyzers["character_patterns"][character]
                    character_elements.extend(elements[:2])  # Take top 2 elements
            
            if character_elements:
                prompt_parts.append(f"画面包含：{', '.join(character_elements)}")
        
        # Add mood and atmosphere
        mood_elements = self.context_analyzers["mood_patterns"].get(mood, ["柔和", "平衡"])
        prompt_parts.append(f"氛围：{', '.join(mood_elements[:2])}")
        
        # Add symbolic elements if this is a key segment
        if segment_index == 0 or segment_index == total_segments - 1:
            symbolic_elements = visual_context.get("symbolic_elements", [])
            if symbolic_elements:
                prompt_parts.append(f"象征元素：{symbolic_elements[0]}")
        
        # Add composition and technical requirements
        prompt_parts.append("构图：16:9宽屏比例，适合视频背景")
        prompt_parts.append("画质：高清晰度，细节丰富")
        prompt_parts.append("色彩：和谐统一，适合长时间观看")
        
        return "，".join(prompt_parts)
    
    async def _generate_background_images(self, image_requests: List[ImageGenerationRequest]) -> List[GeneratedImage]:
        """Generate all background images"""
        generated_images = []
        
        for i, request in enumerate(image_requests):
            try:
                image_data = await self._generate_single_image(request)
                
                generated_image = GeneratedImage(
                    image_id=f"bg_{i:03d}",
                    image_data=image_data,
                    prompt=request.prompt,
                    style=request.style,
                    context=request.context,
                    timing=request.timing,
                    dimensions=(1024, 576),  # 16:9 aspect ratio
                    metadata=request.metadata
                )
                generated_images.append(generated_image)
                
                self.logger.info(f"Generated background image {i+1}/{len(image_requests)}")
                
                # Rate limiting
                await asyncio.sleep(1.0)
                
            except Exception as e:
                self.logger.error(f"Failed to generate image {i}: {str(e)}")
                # Create placeholder image
                placeholder = await self._create_placeholder_image(request)
                generated_images.append(placeholder)
        
        return generated_images
    
    async def _generate_single_image(self, request: ImageGenerationRequest) -> bytes:
        """Generate a single image using Doubao Seedream"""
        
        # Check cache first
        cache_key = hashlib.md5(request.prompt.encode()).hexdigest()
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]
        
        # Prepare API request
        headers = {
            "Authorization": f"Bearer {self.doubao_seedream_config['api_key']}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.doubao_seedream_config["model"],
            "prompt": request.prompt,
            "size": self.doubao_seedream_config["default_size"],
            "quality": self.doubao_seedream_config["quality"],
            "style": self.doubao_seedream_config["style"],
            "n": 1,
            "response_format": "b64_json"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.doubao_seedream_config["api_url"],
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        image_data = base64.b64decode(result["data"][0]["b64_json"])
                        
                        # Cache the result
                        self.image_cache[cache_key] = image_data
                        
                        return image_data
                    else:
                        error_text = await response.text()
                        raise Exception(f"Doubao Seedream API error {response.status}: {error_text}")
        
        except Exception as e:
            self.logger.error(f"Image generation API call failed: {str(e)}")
            raise
    
    async def _create_placeholder_image(self, request: ImageGenerationRequest) -> GeneratedImage:
        """Create a placeholder image when generation fails"""
        # Create a simple colored rectangle as placeholder
        # In production, this could be a template image or a solid color
        placeholder_data = self._generate_solid_color_image((1024, 576), (100, 150, 200))
        
        return GeneratedImage(
            image_id=f"placeholder_{hash(request.prompt) % 1000:03d}",
            image_data=placeholder_data,
            prompt=request.prompt,
            style=request.style,
            context=request.context,
            timing=request.timing,
            dimensions=(1024, 576),
            metadata={**request.metadata, "is_placeholder": True}
        )
    
    def _generate_solid_color_image(self, dimensions: Tuple[int, int], color: Tuple[int, int, int]) -> bytes:
        """Generate a solid color image as placeholder"""
        # Simple implementation - in production use PIL or similar
        width, height = dimensions
        r, g, b = color
        
        # Create minimal PNG data (this is a simplified version)
        # In production, use proper image libraries
        return b'\x89PNG\r\n\x1a\n' + b'\x00' * 100  # Placeholder PNG data
    
    async def _generate_cover_image(self, visual_context: Dict[str, Any], 
                                  selected_concepts: List[Dict[str, Any]]) -> Optional[GeneratedImage]:
        """Generate a cover image for the video"""
        
        if not selected_concepts:
            return None
        
        # Create comprehensive cover prompt
        primary_concept = selected_concepts[0].get("concept", "")
        themes = [theme["theme"] for theme in visual_context.get("primary_themes", [])]
        
        cover_prompt_parts = [
            visual_context.get("primary_style", "literary_classic"),
            f"主题：{primary_concept}",
            "封面设计：精美典雅，具有吸引力",
            "构图：居中对称，适合作为视频封面",
            "文字区域：为标题留出空间",
            "高品质：细节丰富，色彩和谐"
        ]
        
        if themes:
            cover_prompt_parts.append(f"主要元素：{', '.join(themes[:2])}")
        
        cover_prompt = "，".join(cover_prompt_parts)
        
        cover_request = ImageGenerationRequest(
            prompt=cover_prompt,
            style=visual_context.get("primary_style", "literary_classic"),
            context="cover",
            timing={"start_time": 0, "end_time": 0, "duration": 0},
            metadata={
                "type": "cover",
                "primary_concept": primary_concept,
                "themes": themes
            }
        )
        
        try:
            image_data = await self._generate_single_image(cover_request)
            
            return GeneratedImage(
                image_id="cover_001",
                image_data=image_data,
                prompt=cover_prompt,
                style=cover_request.style,
                context="cover",
                timing=cover_request.timing,
                dimensions=(1024, 576),
                metadata=cover_request.metadata
            )
            
        except Exception as e:
            self.logger.error(f"Cover image generation failed: {str(e)}")
            return await self._create_placeholder_image(cover_request)
    
    async def _create_image_timeline(self, background_images: List[GeneratedImage], 
                                   script_content: Dict[str, Any]) -> Dict[str, Any]:
        """Create image timeline with transitions"""
        timeline = {
            "total_duration": 0,
            "image_segments": [],
            "transitions": [],
            "synchronization_points": []
        }
        
        for i, image in enumerate(background_images):
            segment = {
                "image_id": image.image_id,
                "start_time": image.timing["start_time"],
                "end_time": image.timing["end_time"],
                "duration": image.timing["duration"],
                "context": image.context,
                "style": image.style,
                "prompt_preview": image.prompt[:100] + "..." if len(image.prompt) > 100 else image.prompt
            }
            timeline["image_segments"].append(segment)
            
            # Add transition to next image
            if i < len(background_images) - 1:
                next_image = background_images[i + 1]
                transition = ImageTransition(
                    from_image_id=image.image_id,
                    to_image_id=next_image.image_id,
                    transition_time=image.timing["end_time"],
                    transition_type="crossfade",
                    duration=1.0  # 1 second crossfade
                )
                timeline["transitions"].append(transition.__dict__)
            
            # Add synchronization points
            sync_point = {
                "time": image.timing["start_time"],
                "image_id": image.image_id,
                "type": "image_change",
                "context": image.context
            }
            timeline["synchronization_points"].append(sync_point)
        
        if background_images:
            timeline["total_duration"] = background_images[-1].timing["end_time"]
        
        return timeline
    
    async def _create_visual_metadata(self, background_images: List[GeneratedImage],
                                    cover_image: Optional[GeneratedImage],
                                    visual_context: Dict[str, Any],
                                    image_timeline: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive visual metadata"""
        
        total_images = len(background_images) + (1 if cover_image else 0)
        successful_generations = len([img for img in background_images if not img.metadata.get("is_placeholder", False)])
        
        metadata = {
            "generation_summary": {
                "total_images": total_images,
                "background_images": len(background_images),
                "cover_image": 1 if cover_image else 0,
                "successful_generations": successful_generations,
                "placeholder_count": len(background_images) - successful_generations,
                "success_rate": successful_generations / len(background_images) if background_images else 0
            },
            "visual_analysis": {
                "primary_style": visual_context.get("primary_style", "unknown"),
                "themes_covered": len(visual_context.get("primary_themes", [])),
                "characters_featured": len(visual_context.get("character_contexts", [])),
                "mood_variations": len(set(mood["emotion"] for mood in visual_context.get("mood_progression", []))),
                "symbolic_elements_count": len(visual_context.get("symbolic_elements", []))
            },
            "technical_specs": {
                "image_dimensions": "1024x576",
                "aspect_ratio": "16:9",
                "format": "PNG/JPEG",
                "quality": "HD",
                "total_file_size": sum(len(img.image_data) for img in background_images)
            },
            "timeline_analysis": {
                "total_duration": image_timeline.get("total_duration", 0),
                "average_image_duration": (
                    image_timeline.get("total_duration", 0) / len(background_images) 
                    if background_images else 0
                ),
                "transition_count": len(image_timeline.get("transitions", [])),
                "transition_type": "crossfade"
            },
            "quality_assessment": {
                "generation_quality": self._assess_generation_quality(background_images),
                "style_consistency": self._assess_style_consistency(background_images),
                "context_relevance": self._assess_context_relevance(background_images, visual_context),
                "overall_score": 0.0  # Will be calculated
            },
            "recommendations": []
        }
        
        # Calculate overall quality score
        quality_scores = [
            metadata["quality_assessment"]["generation_quality"],
            metadata["quality_assessment"]["style_consistency"],
            metadata["quality_assessment"]["context_relevance"]
        ]
        metadata["quality_assessment"]["overall_score"] = sum(quality_scores) / len(quality_scores)
        
        # Generate recommendations
        if metadata["generation_summary"]["success_rate"] < 0.8:
            metadata["recommendations"].append("Consider checking Doubao Seedream API connectivity and credits")
        
        if metadata["visual_analysis"]["mood_variations"] < 2:
            metadata["recommendations"].append("Consider adding more emotional variety to enhance visual interest")
        
        if metadata["timeline_analysis"]["average_image_duration"] > 25:
            metadata["recommendations"].append("Consider increasing image frequency for better visual engagement")
        
        return metadata
    
    def _assess_generation_quality(self, images: List[GeneratedImage]) -> float:
        """Assess the quality of generated images"""
        if not images:
            return 0.0
        
        successful_count = len([img for img in images if not img.metadata.get("is_placeholder", False)])
        return successful_count / len(images)
    
    def _assess_style_consistency(self, images: List[GeneratedImage]) -> float:
        """Assess style consistency across images"""
        if not images:
            return 0.0
        
        styles = [img.style for img in images]
        unique_styles = set(styles)
        
        # Higher score for more consistent style usage
        consistency_score = 1.0 - (len(unique_styles) - 1) * 0.2
        return max(0.0, consistency_score)
    
    def _assess_context_relevance(self, images: List[GeneratedImage], 
                                visual_context: Dict[str, Any]) -> float:
        """Assess how well images match the visual context"""
        if not images:
            return 0.0
        
        # Simple relevance assessment based on whether images were successfully generated
        # and contain expected context elements
        relevant_images = 0
        
        for image in images:
            if not image.metadata.get("is_placeholder", False):
                # Check if image context matches expected themes
                image_context = image.metadata.get("characters", [])
                expected_characters = [ctx["character"] for ctx in visual_context.get("character_contexts", [])]
                
                if any(char in str(image_context) for char in expected_characters):
                    relevant_images += 1
                else:
                    relevant_images += 0.5  # Partial credit for successful generation
        
        return relevant_images / len(images) if images else 0.0