"""
MCP Agent (Multi-Context Provider) - Integrates multiple AI providers and context sources
Manages API calls, context switching, and provider fallbacks for robust content generation
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from .base_agent import BaseAgent, AgentTask, AgentResult

class ProviderType(Enum):
    """Types of AI providers"""
    TEXT_GENERATION = "text_generation"
    IMAGE_GENERATION = "image_generation"
    SPEECH_SYNTHESIS = "speech_synthesis"
    EMBEDDING = "embedding"
    TRANSLATION = "translation"

@dataclass
class ProviderConfig:
    """Configuration for an AI provider"""
    provider_id: str
    provider_name: str
    provider_type: ProviderType
    api_url: str
    api_key: str
    model_name: str
    max_requests_per_minute: int = 60
    timeout_seconds: int = 30
    fallback_providers: List[str] = field(default_factory=list)
    capabilities: List[str] = field(default_factory=list)
    cost_per_request: float = 0.0

@dataclass
class ContextWindow:
    """Represents a context window for a provider"""
    provider_id: str
    context_id: str
    context_data: Dict[str, Any]
    created_at: datetime
    expires_at: datetime
    token_count: int = 0
    max_tokens: int = 4096

@dataclass
class ProviderResponse:
    """Response from an AI provider"""
    provider_id: str
    success: bool
    data: Dict[str, Any]
    error_message: Optional[str] = None
    response_time: float = 0.0
    token_usage: Dict[str, int] = field(default_factory=dict)
    cost: float = 0.0

class MCPAgent(BaseAgent):
    """
    Multi-Context Provider Agent for managing multiple AI services
    
    Responsibilities:
    - Manage multiple AI provider configurations
    - Route requests to appropriate providers
    - Handle provider fallbacks and error recovery
    - Maintain context windows across providers
    - Optimize cost and performance across providers
    - Aggregate responses from multiple providers
    """
    
    @property
    def agent_type(self) -> str:
        return "mcp"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["provider_request"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {
            "provider_response": dict,
            "context_state": dict,
            "performance_metrics": dict,
            "cost_analysis": dict
        }
    
    def __init__(self, agent_id: str = None, config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.providers = self._initialize_providers()
        self.context_windows = {}
        self.request_queues = {}
        self.performance_stats = {}
        self.cost_tracker = {}
        self._initialize_rate_limiters()
    
    def _initialize_providers(self) -> Dict[str, ProviderConfig]:
        """Initialize AI provider configurations"""
        providers = {}
        
        # Doubao (Bytedance) Providers
        providers["doubao_text"] = ProviderConfig(
            provider_id="doubao_text",
            provider_name="Doubao Text Generation",
            provider_type=ProviderType.TEXT_GENERATION,
            api_url="https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            api_key=self.config.get("doubao_api_key", ""),
            model_name="doubao-pro-32k",
            max_requests_per_minute=100,
            timeout_seconds=60,
            capabilities=["chinese_text", "long_context", "structured_output"],
            cost_per_request=0.01
        )
        
        providers["doubao_seedream"] = ProviderConfig(
            provider_id="doubao_seedream",
            provider_name="Doubao Seedream Image Generation",
            provider_type=ProviderType.IMAGE_GENERATION,
            api_url="https://ark.cn-beijing.volces.com/api/v3/models/model/text2image",
            api_key=self.config.get("doubao_seedream_api_key", ""),
            model_name="doubao-seedream-3",
            max_requests_per_minute=30,
            timeout_seconds=120,
            capabilities=["high_quality", "chinese_prompts", "style_control"],
            cost_per_request=0.05
        )
        
        providers["doubao_tts"] = ProviderConfig(
            provider_id="doubao_tts",
            provider_name="Doubao TTS",
            provider_type=ProviderType.SPEECH_SYNTHESIS,
            api_url="wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
            api_key=self.config.get("doubao_tts_token", ""),
            model_name="multi_emotion_voices",
            max_requests_per_minute=50,
            timeout_seconds=60,
            capabilities=["multi_emotion", "chinese_voices", "high_quality"],
            cost_per_request=0.02
        )
        
        # OpenAI Fallback Providers
        providers["openai_text"] = ProviderConfig(
            provider_id="openai_text",
            provider_name="OpenAI GPT-4",
            provider_type=ProviderType.TEXT_GENERATION,
            api_url="https://api.openai.com/v1/chat/completions",
            api_key=self.config.get("openai_api_key", ""),
            model_name="gpt-4-turbo-preview",
            max_requests_per_minute=100,
            timeout_seconds=60,
            capabilities=["english_text", "reasoning", "structured_output"],
            cost_per_request=0.03,
            fallback_providers=["doubao_text"]
        )
        
        providers["openai_dalle"] = ProviderConfig(
            provider_id="openai_dalle",
            provider_name="OpenAI DALL-E 3",
            provider_type=ProviderType.IMAGE_GENERATION,
            api_url="https://api.openai.com/v1/images/generations",
            api_key=self.config.get("openai_api_key", ""),
            model_name="dall-e-3",
            max_requests_per_minute=30,
            timeout_seconds=120,
            capabilities=["photorealistic", "artistic", "detailed_prompts"],
            cost_per_request=0.08,
            fallback_providers=["doubao_seedream"]
        )
        
        # Claude Anthropic Provider
        providers["claude_text"] = ProviderConfig(
            provider_id="claude_text",
            provider_name="Claude 3.5 Sonnet",
            provider_type=ProviderType.TEXT_GENERATION,
            api_url="https://api.anthropic.com/v1/messages",
            api_key=self.config.get("anthropic_api_key", ""),
            model_name="claude-3-5-sonnet-20241022",
            max_requests_per_minute=50,
            timeout_seconds=60,
            capabilities=["analysis", "reasoning", "long_context", "structured_output"],
            cost_per_request=0.025,
            fallback_providers=["doubao_text", "openai_text"]
        )
        
        return providers
    
    def _initialize_rate_limiters(self):
        """Initialize rate limiting for each provider"""
        for provider_id, provider_config in self.providers.items():
            self.request_queues[provider_id] = asyncio.Queue()
            self.performance_stats[provider_id] = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "total_response_time": 0.0,
                "last_request_time": None,
                "rate_limit_hits": 0
            }
            self.cost_tracker[provider_id] = {
                "total_cost": 0.0,
                "total_requests": 0,
                "cost_per_hour": {}
            }
    
    async def execute(self, task: AgentTask) -> AgentResult:
        """Execute MCP request routing and management"""
        try:
            provider_request = task.input_data["provider_request"]
            
            self.logger.info(f"Processing MCP request: {provider_request.get('type', 'unknown')}")
            
            # Step 1: Parse and validate request
            parsed_request = await self._parse_provider_request(provider_request)
            
            # Step 2: Select optimal provider
            selected_provider = await self._select_provider(parsed_request)
            
            # Step 3: Manage context if needed
            context_state = await self._manage_context(parsed_request, selected_provider)
            
            # Step 4: Execute request with fallback handling
            provider_response = await self._execute_with_fallback(parsed_request, selected_provider)
            
            # Step 5: Update performance metrics
            await self._update_metrics(selected_provider, provider_response)
            
            # Step 6: Calculate costs
            cost_analysis = await self._analyze_costs(provider_response)
            
            # Step 7: Generate performance metrics
            performance_metrics = await self._generate_performance_metrics()
            
            result_data = {
                "provider_response": provider_response.__dict__,
                "context_state": context_state,
                "performance_metrics": performance_metrics,
                "cost_analysis": cost_analysis
            }
            
            self.logger.info(f"MCP request completed using provider: {provider_response.provider_id}")
            
            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    "provider_used": provider_response.provider_id,
                    "response_time": provider_response.response_time,
                    "success": provider_response.success,
                    "cost": provider_response.cost
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCP execution failed: {str(e)}")
            return AgentResult(
                success=False,
                error_message=f"MCP execution failed: {str(e)}"
            )
    
    async def _parse_provider_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Parse and validate incoming provider request"""
        parsed = {
            "type": request.get("type", "unknown"),
            "provider_type": None,
            "data": request.get("data", {}),
            "preferences": request.get("preferences", {}),
            "context_id": request.get("context_id"),
            "priority": request.get("priority", "normal"),
            "timeout": request.get("timeout", 30),
            "fallback_enabled": request.get("fallback_enabled", True)
        }
        
        # Map request types to provider types
        type_mapping = {
            "text_generation": ProviderType.TEXT_GENERATION,
            "content_generation": ProviderType.TEXT_GENERATION,
            "script_generation": ProviderType.TEXT_GENERATION,
            "image_generation": ProviderType.IMAGE_GENERATION,
            "background_generation": ProviderType.IMAGE_GENERATION,
            "speech_synthesis": ProviderType.SPEECH_SYNTHESIS,
            "voice_generation": ProviderType.SPEECH_SYNTHESIS,
            "translation": ProviderType.TRANSLATION,
            "embedding": ProviderType.EMBEDDING
        }
        
        parsed["provider_type"] = type_mapping.get(parsed["type"], ProviderType.TEXT_GENERATION)
        
        return parsed
    
    async def _select_provider(self, request: Dict[str, Any]) -> str:
        """Select optimal provider based on request characteristics"""
        provider_type = request["provider_type"]
        preferences = request["preferences"]
        
        # Filter providers by type
        available_providers = [
            p_id for p_id, config in self.providers.items() 
            if config.provider_type == provider_type and config.api_key
        ]
        
        if not available_providers:
            raise Exception(f"No providers available for type: {provider_type}")
        
        # Score providers based on various factors
        provider_scores = {}
        
        for provider_id in available_providers:
            config = self.providers[provider_id]
            stats = self.performance_stats[provider_id]
            
            score = 100.0  # Base score
            
            # Performance scoring
            if stats["total_requests"] > 0:
                success_rate = stats["successful_requests"] / stats["total_requests"]
                avg_response_time = stats["total_response_time"] / stats["total_requests"]
                
                score += success_rate * 50  # Success rate bonus
                score -= min(avg_response_time, 10) * 5  # Response time penalty
                score -= stats["rate_limit_hits"] * 10  # Rate limit penalty
            
            # Capability scoring
            required_capabilities = preferences.get("capabilities", [])
            matching_capabilities = len(set(required_capabilities) & set(config.capabilities))
            score += matching_capabilities * 20
            
            # Cost scoring (lower cost = higher score)
            if preferences.get("optimize_cost", False):
                score -= config.cost_per_request * 100
            
            # Provider preference
            preferred_provider = preferences.get("preferred_provider")
            if preferred_provider == provider_id:
                score += 30
            
            # Language preference scoring
            language = preferences.get("language", "chinese")
            if language == "chinese" and "chinese" in provider_id:
                score += 25
            elif language == "english" and "openai" in provider_id:
                score += 25
            
            provider_scores[provider_id] = score
        
        # Select provider with highest score
        selected_provider = max(provider_scores.items(), key=lambda x: x[1])[0]
        
        self.logger.info(f"Selected provider: {selected_provider} (score: {provider_scores[selected_provider]:.1f})")
        
        return selected_provider
    
    async def _manage_context(self, request: Dict[str, Any], provider_id: str) -> Dict[str, Any]:
        """Manage context windows for providers"""
        context_id = request.get("context_id")
        
        if not context_id:
            return {"context_managed": False, "context_id": None}
        
        provider_config = self.providers[provider_id]
        
        # Check for existing context window
        window_key = f"{provider_id}_{context_id}"
        
        if window_key in self.context_windows:
            window = self.context_windows[window_key]
            
            # Check if context window is still valid
            if datetime.now() < window.expires_at:
                # Update context with new data
                window.context_data.update(request.get("data", {}))
                window.token_count += self._estimate_tokens(request.get("data", {}))
                
                # Check token limits
                if window.token_count > window.max_tokens:
                    # Truncate or create new window
                    await self._truncate_context_window(window)
                
                return {
                    "context_managed": True,
                    "context_id": context_id,
                    "window_id": window_key,
                    "token_count": window.token_count,
                    "expires_at": window.expires_at.isoformat()
                }
            else:
                # Context expired, remove it
                del self.context_windows[window_key]
        
        # Create new context window
        new_window = ContextWindow(
            provider_id=provider_id,
            context_id=context_id,
            context_data=request.get("data", {}),
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=1),  # 1 hour expiry
            token_count=self._estimate_tokens(request.get("data", {})),
            max_tokens=8192  # Default max tokens
        )
        
        self.context_windows[window_key] = new_window
        
        return {
            "context_managed": True,
            "context_id": context_id,
            "window_id": window_key,
            "token_count": new_window.token_count,
            "created_at": new_window.created_at.isoformat(),
            "expires_at": new_window.expires_at.isoformat()
        }
    
    def _estimate_tokens(self, data: Dict[str, Any]) -> int:
        """Estimate token count for data"""
        # Simple estimation - in production use proper tokenizers
        text_content = str(data)
        # Rough estimation: 1 token per 4 characters for Chinese, 1 token per 4 characters for English
        return len(text_content) // 3
    
    async def _truncate_context_window(self, window: ContextWindow):
        """Truncate context window when it exceeds limits"""
        # Simple truncation strategy - keep most recent data
        context_str = json.dumps(window.context_data)
        max_chars = window.max_tokens * 3  # Rough conversion
        
        if len(context_str) > max_chars:
            # Keep the last portion of the context
            truncated_str = context_str[-max_chars:]
            try:
                # Try to parse back to valid JSON
                window.context_data = json.loads(truncated_str)
            except:
                # If parsing fails, keep basic structure
                window.context_data = {"truncated": True, "remaining_content": truncated_str}
        
        window.token_count = self._estimate_tokens(window.context_data)
    
    async def _execute_with_fallback(self, request: Dict[str, Any], primary_provider: str) -> ProviderResponse:
        """Execute request with fallback handling"""
        providers_to_try = [primary_provider]
        
        # Add fallback providers if enabled
        if request.get("fallback_enabled", True):
            primary_config = self.providers[primary_provider]
            providers_to_try.extend(primary_config.fallback_providers)
        
        last_error = None
        
        for provider_id in providers_to_try:
            if provider_id not in self.providers:
                continue
            
            try:
                self.logger.info(f"Attempting request with provider: {provider_id}")
                
                # Check rate limits
                if not await self._check_rate_limit(provider_id):
                    self.performance_stats[provider_id]["rate_limit_hits"] += 1
                    continue
                
                # Execute request
                response = await self._execute_provider_request(request, provider_id)
                
                if response.success:
                    return response
                else:
                    last_error = response.error_message
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"Provider {provider_id} failed: {str(e)}")
                continue
        
        # All providers failed
        return ProviderResponse(
            provider_id=primary_provider,
            success=False,
            data={},
            error_message=f"All providers failed. Last error: {last_error}",
            response_time=0.0
        )
    
    async def _check_rate_limit(self, provider_id: str) -> bool:
        """Check if provider is within rate limits"""
        config = self.providers[provider_id]
        stats = self.performance_stats[provider_id]
        
        if not stats["last_request_time"]:
            return True
        
        # Simple rate limiting - check requests per minute
        time_since_last = (datetime.now() - stats["last_request_time"]).total_seconds()
        min_interval = 60.0 / config.max_requests_per_minute
        
        return time_since_last >= min_interval
    
    async def _execute_provider_request(self, request: Dict[str, Any], provider_id: str) -> ProviderResponse:
        """Execute request for specific provider"""
        start_time = datetime.now()
        config = self.providers[provider_id]
        
        try:
            if config.provider_type == ProviderType.TEXT_GENERATION:
                response_data = await self._call_text_generation_api(request, config)
            elif config.provider_type == ProviderType.IMAGE_GENERATION:
                response_data = await self._call_image_generation_api(request, config)
            elif config.provider_type == ProviderType.SPEECH_SYNTHESIS:
                response_data = await self._call_speech_synthesis_api(request, config)
            else:
                raise Exception(f"Unsupported provider type: {config.provider_type}")
            
            response_time = (datetime.now() - start_time).total_seconds()
            
            return ProviderResponse(
                provider_id=provider_id,
                success=True,
                data=response_data,
                response_time=response_time,
                cost=config.cost_per_request
            )
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            
            return ProviderResponse(
                provider_id=provider_id,
                success=False,
                data={},
                error_message=str(e),
                response_time=response_time,
                cost=0.0
            )
    
    async def _call_text_generation_api(self, request: Dict[str, Any], config: ProviderConfig) -> Dict[str, Any]:
        """Call text generation API"""
        data = request["data"]
        
        # Prepare request based on provider
        if "doubao" in config.provider_id:
            payload = {
                "model": config.model_name,
                "messages": data.get("messages", []),
                "temperature": data.get("temperature", 0.7),
                "max_tokens": data.get("max_tokens", 2048),
                "top_p": data.get("top_p", 0.9)
            }
        elif "openai" in config.provider_id:
            payload = {
                "model": config.model_name,
                "messages": data.get("messages", []),
                "temperature": data.get("temperature", 0.7),
                "max_tokens": data.get("max_tokens", 2048)
            }
        elif "claude" in config.provider_id:
            payload = {
                "model": config.model_name,
                "messages": data.get("messages", []),
                "max_tokens": data.get("max_tokens", 2048),
                "temperature": data.get("temperature", 0.7)
            }
        else:
            raise Exception(f"Unknown text provider: {config.provider_id}")
        
        headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                config.api_url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        "generated_text": result.get("choices", [{}])[0].get("message", {}).get("content", ""),
                        "usage": result.get("usage", {}),
                        "model": config.model_name
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"API error {response.status}: {error_text}")
    
    async def _call_image_generation_api(self, request: Dict[str, Any], config: ProviderConfig) -> Dict[str, Any]:
        """Call image generation API"""
        data = request["data"]
        
        # Prepare request based on provider
        if "doubao" in config.provider_id:
            payload = {
                "model": config.model_name,
                "prompt": data.get("prompt", ""),
                "size": data.get("size", "1024x576"),
                "quality": data.get("quality", "hd"),
                "n": 1,
                "response_format": "b64_json"
            }
        elif "openai" in config.provider_id:
            payload = {
                "model": config.model_name,
                "prompt": data.get("prompt", ""),
                "size": data.get("size", "1024x1024"),
                "quality": data.get("quality", "standard"),
                "n": 1,
                "response_format": "b64_json"
            }
        else:
            raise Exception(f"Unknown image provider: {config.provider_id}")
        
        headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                config.api_url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=config.timeout_seconds)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        "image_data": result.get("data", [{}])[0].get("b64_json", ""),
                        "prompt": data.get("prompt", ""),
                        "model": config.model_name
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"API error {response.status}: {error_text}")
    
    async def _call_speech_synthesis_api(self, request: Dict[str, Any], config: ProviderConfig) -> Dict[str, Any]:
        """Call speech synthesis API"""
        data = request["data"]
        
        # This would integrate with the existing Doubao TTS implementation
        # For now, return a mock response
        return {
            "audio_data": b"",  # Would contain actual audio data
            "text": data.get("text", ""),
            "voice": data.get("voice", "default"),
            "emotion": data.get("emotion", "neutral"),
            "duration": len(data.get("text", "")) * 0.1  # Estimated duration
        }
    
    async def _update_metrics(self, provider_id: str, response: ProviderResponse):
        """Update performance metrics for provider"""
        stats = self.performance_stats[provider_id]
        cost_tracker = self.cost_tracker[provider_id]
        
        stats["total_requests"] += 1
        stats["total_response_time"] += response.response_time
        stats["last_request_time"] = datetime.now()
        
        if response.success:
            stats["successful_requests"] += 1
        else:
            stats["failed_requests"] += 1
        
        # Update cost tracking
        cost_tracker["total_cost"] += response.cost
        cost_tracker["total_requests"] += 1
        
        # Hourly cost tracking
        current_hour = datetime.now().strftime("%Y-%m-%d_%H")
        if current_hour not in cost_tracker["cost_per_hour"]:
            cost_tracker["cost_per_hour"][current_hour] = 0.0
        cost_tracker["cost_per_hour"][current_hour] += response.cost
    
    async def _analyze_costs(self, response: ProviderResponse) -> Dict[str, Any]:
        """Analyze cost implications"""
        provider_costs = self.cost_tracker[response.provider_id]
        
        return {
            "current_request_cost": response.cost,
            "total_provider_cost": provider_costs["total_cost"],
            "average_cost_per_request": (
                provider_costs["total_cost"] / provider_costs["total_requests"]
                if provider_costs["total_requests"] > 0 else 0.0
            ),
            "hourly_costs": provider_costs["cost_per_hour"],
            "cost_efficiency": "high" if response.cost < 0.02 else "medium" if response.cost < 0.05 else "low"
        }
    
    async def _generate_performance_metrics(self) -> Dict[str, Any]:
        """Generate comprehensive performance metrics"""
        total_requests = sum(stats["total_requests"] for stats in self.performance_stats.values())
        total_successful = sum(stats["successful_requests"] for stats in self.performance_stats.values())
        
        provider_performance = {}
        for provider_id, stats in self.performance_stats.items():
            if stats["total_requests"] > 0:
                provider_performance[provider_id] = {
                    "success_rate": stats["successful_requests"] / stats["total_requests"],
                    "average_response_time": stats["total_response_time"] / stats["total_requests"],
                    "total_requests": stats["total_requests"],
                    "rate_limit_hits": stats["rate_limit_hits"]
                }
        
        return {
            "overall_metrics": {
                "total_requests": total_requests,
                "overall_success_rate": total_successful / total_requests if total_requests > 0 else 0.0,
                "active_providers": len([p for p, s in self.performance_stats.items() if s["total_requests"] > 0]),
                "total_providers": len(self.providers)
            },
            "provider_performance": provider_performance,
            "context_windows": {
                "active_windows": len(self.context_windows),
                "total_token_usage": sum(w.token_count for w in self.context_windows.values())
            },
            "recommendations": await self._generate_recommendations()
        }
    
    async def _generate_recommendations(self) -> List[str]:
        """Generate performance and cost optimization recommendations"""
        recommendations = []
        
        # Analyze provider performance
        for provider_id, stats in self.performance_stats.items():
            if stats["total_requests"] > 10:  # Only analyze providers with sufficient data
                success_rate = stats["successful_requests"] / stats["total_requests"]
                
                if success_rate < 0.8:
                    recommendations.append(f"Provider {provider_id} has low success rate ({success_rate:.1%}). Consider checking configuration.")
                
                if stats["rate_limit_hits"] > 5:
                    recommendations.append(f"Provider {provider_id} hitting rate limits frequently. Consider upgrading plan.")
        
        # Cost analysis
        total_cost = sum(tracker["total_cost"] for tracker in self.cost_tracker.values())
        if total_cost > 10.0:  # $10 threshold
            recommendations.append("High API costs detected. Consider optimizing request frequency or using more cost-effective providers.")
        
        # Context management
        if len(self.context_windows) > 50:
            recommendations.append("High number of active context windows. Consider implementing more aggressive cleanup.")
        
        return recommendations
    
    async def get_provider_status(self, provider_id: str) -> Dict[str, Any]:
        """Get detailed status for a specific provider"""
        if provider_id not in self.providers:
            return {"error": "Provider not found"}
        
        config = self.providers[provider_id]
        stats = self.performance_stats[provider_id]
        costs = self.cost_tracker[provider_id]
        
        return {
            "provider_info": {
                "provider_id": provider_id,
                "provider_name": config.provider_name,
                "provider_type": config.provider_type.value,
                "model_name": config.model_name,
                "capabilities": config.capabilities
            },
            "performance": stats,
            "costs": costs,
            "status": "active" if config.api_key else "inactive",
            "last_request": stats["last_request_time"].isoformat() if stats["last_request_time"] else None
        }
    
    async def cleanup_expired_contexts(self):
        """Clean up expired context windows"""
        now = datetime.now()
        expired_windows = [
            window_id for window_id, window in self.context_windows.items()
            if now > window.expires_at
        ]
        
        for window_id in expired_windows:
            del self.context_windows[window_id]
            self.logger.info(f"Cleaned up expired context window: {window_id}")
        
        return len(expired_windows)