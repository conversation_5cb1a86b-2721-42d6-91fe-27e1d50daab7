#!/usr/bin/env python3
"""
Start the Flask server for testing
"""

import subprocess
import sys
import time
import os

def start_server():
    """Start the Flask server on port 8888"""
    print("🚀 Starting Flask server on port 8888...")
    
    # Set environment variable for port
    env = os.environ.copy()
    env['PORT'] = '8888'
    
    try:
        # Start the Flask server
        process = subprocess.Popen(
            [sys.executable, 'app.py'],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1
        )
        
        print("⏳ Waiting for server to start...")
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Server started successfully!")
            print("📍 API endpoint: http://localhost:8888")
            print("📍 Web interface: http://localhost:8888/ui")
            print("\n Press Ctrl+C to stop the server")
            
            try:
                # Print server output
                while True:
                    output = process.stdout.readline()
                    if output:
                        print(f"[SERVER] {output.strip()}")
                    
                    error = process.stderr.readline()
                    if error:
                        print(f"[ERROR] {error.strip()}")
                        
                    # Check if process is still running
                    if process.poll() is not None:
                        break
                        
            except KeyboardInterrupt:
                print("\n🛑 Stopping server...")
                process.terminate()
                process.wait()
                print("✅ Server stopped")
        else:
            print("❌ Server failed to start")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"Output: {stdout}")
            if stderr:
                print(f"Error: {stderr}")
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    start_server()