#!/usr/bin/env python3
"""
Voice Discovery Script for Doubao TTS
Tests all configured voices to determine which ones work with current app_id credentials
"""

import os
import sys
import time
from src.doubao_tts import DoubaoTTS

def test_all_voices():
    """Test all configured voices to see which ones work"""
    
    print("🔍 Doubao TTS Voice Discovery")
    print("=" * 50)
    
    # Initialize TTS service
    tts = DoubaoTTS()
    
    if not tts.is_configured:
        print("❌ Doubao TTS not configured. Please set app_id and access_token.")
        return
    
    print(f"🔑 App ID: {tts.app_id}")
    print(f"🎫 Access Token: {tts.access_token[:10]}...")
    print()
    
    # Get all voices
    voices = tts.get_available_voices()
    
    print(f"📊 Testing {len(voices)} voices...")
    print()
    
    # Test text
    test_text = "你好，这是一个测试"
    
    working_voices = []
    failed_voices = []
    
    # Group voices by category
    categories = {}
    for voice_id, voice_info in voices.items():
        category = voice_info.get('category', 'unknown')
        if category not in categories:
            categories[category] = []
        categories[category].append((voice_id, voice_info))
    
    # Test each category
    for category, voice_list in categories.items():
        print(f"\n🎭 Testing {category.upper()} voices ({len(voice_list)} voices):")
        print("-" * 40)
        
        for voice_id, voice_info in voice_list:
            name = voice_info.get('name', voice_id)
            emotion_support = voice_info.get('emotion_support', False)
            
            print(f"Testing: {name} ({voice_id})")
            print(f"  Emotion support: {'✅' if emotion_support else '❌'}")
            
            try:
                # Test basic synthesis
                audio_data = tts.synthesize_speech(test_text, voice_id, "neutral")
                
                if audio_data and len(audio_data) > 0:
                    print(f"  ✅ SUCCESS - Generated {len(audio_data)} bytes")
                    working_voices.append((voice_id, voice_info))
                    
                    # Test emotion synthesis if supported
                    if emotion_support:
                        emotions = voice_info.get('emotions', [])
                        print(f"  🎭 Testing emotions: {emotions[:3]}...")  # Test first 3 emotions
                        
                        emotion_results = []
                        for emotion in emotions[:3]:  # Limit to avoid too many API calls
                            try:
                                emotion_audio = tts.synthesize_speech(test_text, voice_id, emotion)
                                if emotion_audio and len(emotion_audio) > 0:
                                    emotion_results.append(f"✅{emotion}")
                                else:
                                    emotion_results.append(f"❌{emotion}")
                            except Exception as e:
                                emotion_results.append(f"❌{emotion}")
                        
                        print(f"  🎭 Emotion results: {' '.join(emotion_results)}")
                else:
                    print(f"  ❌ FAILED - No audio data received")
                    failed_voices.append((voice_id, voice_info, "No audio data"))
                    
            except Exception as e:
                error_msg = str(e)
                print(f"  ❌ FAILED - {error_msg}")
                failed_voices.append((voice_id, voice_info, error_msg))
            
            # Small delay to avoid rate limiting
            time.sleep(0.5)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VOICE DISCOVERY SUMMARY")
    print("=" * 50)
    
    print(f"\n✅ WORKING VOICES ({len(working_voices)}):")
    for voice_id, voice_info in working_voices:
        name = voice_info.get('name', voice_id)
        category = voice_info.get('category', 'unknown')
        emotion_support = voice_info.get('emotion_support', False)
        print(f"  • {name} ({category}) {'🎭' if emotion_support else ''}")
    
    print(f"\n❌ FAILED VOICES ({len(failed_voices)}):")
    for voice_id, voice_info, error in failed_voices:
        name = voice_info.get('name', voice_id)
        category = voice_info.get('category', 'unknown')
        print(f"  • {name} ({category}) - {error}")
    
    # Multi-emotion voice analysis
    multi_emotion_voices = [v for v in working_voices if v[1].get('emotion_support')]
    print(f"\n🎭 MULTI-EMOTION VOICES WORKING ({len(multi_emotion_voices)}):")
    for voice_id, voice_info in multi_emotion_voices:
        name = voice_info.get('name', voice_id)
        emotions = voice_info.get('emotions', [])
        print(f"  • {name}: {len(emotions)} emotions ({', '.join(emotions[:5])}{'...' if len(emotions) > 5 else ''})")
    
    # Success rate
    success_rate = len(working_voices) / len(voices) * 100
    print(f"\n📈 SUCCESS RATE: {success_rate:.1f}% ({len(working_voices)}/{len(voices)})")
    
    return working_voices, failed_voices

if __name__ == "__main__":
    test_all_voices()