"""
Enhanced Deep Research System
增强版深度研究系统 - 专注于深度调研、多人对谈播客和视频内容生成

核心功能：
1. 深度调查研究 - 多智能体协作进行全面研究分析
2. 多人对谈播客 - 生成多角色对话音频内容
3. 语音视频制作 - 整合研究和音频生成完整视频
4. 端到端工作流 - 从主题输入到最终内容的自动化流程
"""

from flask import Flask, render_template, request, jsonify, send_file, session
from flask_cors import CORS
import asyncio
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
import logging
import os

# 导入核心组件
import sys
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from enhanced_workflow_pipeline import (
        EnhancedWorkflowPipeline,
        WorkflowRequest,
        WorkflowStep,
        OutputFormat
    )
    from universal_research_template import ResearchScenario
    from video_config import VideoConfig
    from voice_config import VoiceManager
    from reviewer_personalities import PersonalityManager
    from deep_research_agent import DeepResearchAgent

    print("✅ 核心深度研究组件加载成功")

except ImportError as e:
    print(f"⚠️  部分核心组件未找到: {str(e)}")
    print("⚠️  系统将以基础模式运行")

    # 创建模拟组件
    class EnhancedWorkflowPipeline:
        async def execute_workflow(self, request):
            return {"success": False, "error": "核心组件未加载"}

    class WorkflowRequest:
        pass

    class OutputFormat:
        RESEARCH_REPORT = "research_report"
        VIDEO_SCRIPT = "video_script"
        PODCAST_SCRIPT = "podcast_script"
        COMPLETE_VIDEO = "complete_video"
        AUDIO_PODCAST = "audio_podcast"

    class ResearchScenario:
        BOOK_ANALYSIS = "book_analysis"
        INDUSTRY_REPORT = "industry_report"
        ACADEMIC_RESEARCH = "academic_research"
        MARKET_ANALYSIS = "market_analysis"
        TECHNOLOGY_REVIEW = "technology_review"

    print("✅ 所有核心组件和智能体加载成功")
except ImportError as e:
    print(f"❌ 核心组件导入失败: {str(e)}")
    raise

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)
app.secret_key = 'enhanced_research_system_2024'

# 全局组件
pipeline = EnhancedWorkflowPipeline()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask应用初始化
app = Flask(__name__)
app.secret_key = 'deep_research_system_2024'
CORS(app)

# 全局组件
try:
    pipeline = EnhancedWorkflowPipeline()
    research_agent = DeepResearchAgent()
    print("✅ 深度研究管道初始化成功")
except:
    pipeline = EnhancedWorkflowPipeline()
    research_agent = None
    print("⚠️  使用基础模式运行")

# 任务状态存储
task_status = {}
task_results = {}

# 异步运行辅助函数
def run_async(coro):
    """在新的事件循环中运行异步函数"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(coro)
    finally:
        loop.close()

# ===== 核心路由 =====

@app.route('/')
def index():
    """主页 - 深度研究系统"""
    return render_template('research_index.html')

@app.route('/results/<task_id>')
def results_page(task_id):
    """结果页面"""
    return render_template('research_results.html', task_id=task_id)

# ===== API端点 =====

@app.route('/api/research/scenarios')
def get_research_scenarios():
    """获取研究场景列表"""
    scenarios = [
        {
            "id": "book_analysis",
            "name": "图书深度分析",
            "description": "对指定书籍进行全面的内容分析、主题解读和价值评估",
            "icon": "📚"
        },
        {
            "id": "industry_report",
            "name": "行业研究报告",
            "description": "深入分析特定行业的发展现状、趋势和机遇挑战",
            "icon": "🏭"
        },
        {
            "id": "academic_research",
            "name": "学术研究分析",
            "description": "对学术话题进行深度调研，整合多方观点和最新进展",
            "icon": "🎓"
        },
        {
            "id": "market_analysis",
            "name": "市场分析报告",
            "description": "分析市场动态、竞争格局和投资机会",
            "icon": "📈"
        },
        {
            "id": "technology_review",
            "name": "技术深度评测",
            "description": "对新兴技术进行全面评估和前景分析",
            "icon": "🔬"
        }
    ]

    return jsonify({
        "success": True,
        "scenarios": scenarios
    })

@app.route('/api/research/output-formats')
def get_output_formats():
    """获取输出格式选项"""
    formats = [
        {
            "id": "research_report",
            "name": "深度研究报告",
            "description": "生成详细的文字研究报告，包含多维度分析",
            "icon": "📄",
            "estimated_time": "5-10分钟"
        },
        {
            "id": "podcast_script",
            "name": "播客对话脚本",
            "description": "生成多人对谈的播客脚本，适合音频制作",
            "icon": "🎙️",
            "estimated_time": "8-15分钟"
        },
        {
            "id": "audio_podcast",
            "name": "多人对谈音频",
            "description": "生成完整的多角色对话音频播客",
            "icon": "🎧",
            "estimated_time": "15-25分钟"
        },
        {
            "id": "complete_video",
            "name": "完整视频内容",
            "description": "生成包含语音、字幕、背景的完整视频",
            "icon": "🎬",
            "estimated_time": "20-35分钟"
        }
    ]

    return jsonify({
        "success": True,
        "formats": formats
    })

@app.route('/api/research/start', methods=['POST'])
def start_research():
    """启动深度研究工作流"""
    try:
        data = request.json

        # 验证必需参数
        required_fields = ['user_input', 'research_scenario', 'output_format', 'target_audience']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "success": False,
                    "error": f"缺少必需参数: {field}"
                }), 400

        # 创建任务ID
        task_id = str(uuid.uuid4())

        # 创建工作流请求
        workflow_request = WorkflowRequest(
            user_input=data['user_input'],
            research_scenario=data['research_scenario'],
            output_format=data['output_format'],
            target_audience=data['target_audience'],
            additional_requirements=data.get('additional_requirements', ''),
            task_id=task_id
        )

        # 初始化任务状态
        task_status[task_id] = {
            "status": "starting",
            "current_step": "初始化研究任务",
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "user_input": data['user_input'],
            "research_scenario": data['research_scenario'],
            "output_format": data['output_format'],
            "steps_completed": [],
            "estimated_completion": None
        }

        # 异步执行工作流
        def execute_workflow_async():
            try:
                @run_async
                async def run_workflow():
                    return await pipeline.execute_workflow(workflow_request)

                result = run_workflow()

                # 更新任务状态
                if result and result.get('success'):
                    task_status[task_id].update({
                        "status": "completed",
                        "current_step": "研究完成",
                        "progress": 100,
                        "completed_at": datetime.now().isoformat(),
                        "result_files": result.get('output_files', {}),
                        "summary": result.get('summary', ''),
                        "quality_score": result.get('quality_score', 0)
                    })
                    task_results[task_id] = result
                else:
                    task_status[task_id].update({
                        "status": "failed",
                        "current_step": "执行失败",
                        "error": result.get('error', '未知错误') if result else '工作流执行失败',
                        "failed_at": datetime.now().isoformat()
                    })

            except Exception as e:
                logger.error(f"工作流执行异常: {str(e)}")
                task_status[task_id].update({
                    "status": "failed",
                    "current_step": "系统异常",
                    "error": str(e),
                    "failed_at": datetime.now().isoformat()
                })

        # 启动异步执行
        thread = threading.Thread(target=execute_workflow_async)
        thread.start()

        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "深度研究任务已启动",
            "estimated_time": _get_estimated_time(data['output_format'])
        })

    except Exception as e:
        logger.error(f"启动研究失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/status/<task_id>')
def get_research_status(task_id):
    """获取研究任务状态"""
    try:
        if task_id not in task_status:
            return jsonify({
                "success": False,
                "error": "任务不存在"
            }), 404

        status = task_status[task_id]

        return jsonify({
            "success": True,
            "task_id": task_id,
            **status
        })

    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/result/<task_id>')
def get_research_result(task_id):
    """获取研究结果"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在或任务未完成"
            }), 404

        result = task_results[task_id]
        status = task_status.get(task_id, {})

        return jsonify({
            "success": True,
            "task_id": task_id,
            "status": status.get("status"),
            "result": result,
            "generated_at": status.get("completed_at")
        })

    except Exception as e:
        logger.error(f"获取研究结果失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/download/<task_id>/<file_type>')
def download_result_file(task_id, file_type):
    """下载结果文件"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在"
            }), 404

        result = task_results[task_id]
        output_files = result.get('output_files', {})

        if file_type not in output_files:
            return jsonify({
                "success": False,
                "error": f"文件类型 {file_type} 不存在"
            }), 404

        file_path = output_files[file_type]

        if not Path(file_path).exists():
            return jsonify({
                "success": False,
                "error": "文件不存在"
            }), 404

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# ===== 辅助函数 =====

def _get_estimated_time(output_format):
    """获取预估完成时间"""
    time_estimates = {
        "research_report": "5-10分钟",
        "podcast_script": "8-15分钟",
        "audio_podcast": "15-25分钟",
        "complete_video": "20-35分钟"
    }
    return time_estimates.get(output_format, "10-20分钟")

# ===== 兼容性路由（保持原有功能） =====

@app.route('/api/voices')
def get_voices():
    """获取语音列表（兼容原系统）"""
    try:
        voice_manager = VoiceManager()
        voices = voice_manager.get_available_voices()

        return jsonify({
            "success": True,
            "voices": voices
        })
    except:
        # 模拟语音数据
        voices = [
            {"id": "zh_female_roumeinvyou", "name": "温柔女声", "language": "zh", "gender": "female"},
            {"id": "zh_male_jingqiangnansheng", "name": "京腔男声", "language": "zh", "gender": "male"},
            {"id": "zh_female_zhilingmeinv", "name": "知性女声", "language": "zh", "gender": "female"}
        ]
        return jsonify({"success": True, "voices": voices})

@app.route('/api/personalities')
def get_personalities():
    """获取评论者风格（兼容原系统）"""
    try:
        personality_manager = PersonalityManager()
        personalities = personality_manager.get_personality_list()

        return jsonify({
            "success": True,
            "personalities": personalities
        })
    except:
        # 模拟风格数据
        personalities = [
            {"id": "dongyu_hui", "name": "董宇辉", "description": "知识渊博的主播风格"},
            {"id": "luo_zhenyu", "name": "罗振宇", "description": "逻辑清晰的知识分享者"},
            {"id": "academic", "name": "学者风格", "description": "严谨的学术分析风格"}
        ]
        return jsonify({"success": True, "personalities": personalities})

# ===== 工作流处理函数 =====

def process_workflow_task(task_id: str, workflow_request):
    """处理工作流任务"""
    try:
        # 更新状态为处理中
        task_status[task_id]["status"] = "processing"
        task_status[task_id]["current_step"] = "开始深度研究"
        task_status[task_id]["progress"] = 10

        # 模拟工作流步骤
        steps = [
            (20, "分析用户输入"),
            (40, "执行深度研究"),
            (60, "生成研究报告"),
            (80, "制作音频内容"),
            (100, "完成视频制作")
        ]

        for progress, step_name in steps:
            if task_status[task_id]["status"] != "processing":
                break

            task_status[task_id]["progress"] = progress
            task_status[task_id]["current_step"] = step_name
            task_status[task_id]["steps_completed"].append(step_name)

            # 模拟处理时间
            time.sleep(2)

        # 完成任务
        task_status[task_id]["status"] = "completed"
        task_status[task_id]["current_step"] = "研究完成"
        task_status[task_id]["progress"] = 100
        task_status[task_id]["completed_at"] = datetime.now().isoformat()

        # 模拟结果
        task_results[task_id] = {
            "success": True,
            "output_files": {
                "research_report": f"output/research_report_{task_id}.txt",
                "audio_podcast": f"output/podcast_{task_id}.mp3",
                "complete_video": f"output/video_{task_id}.mp4"
            },
            "summary": "深度研究已完成，生成了详细的分析报告和多媒体内容。",
            "quality_score": 85
        }

    except Exception as e:
        logger.error(f"工作流执行异常: {str(e)}")
        task_status[task_id]["status"] = "failed"
        task_status[task_id]["error"] = str(e)
        task_status[task_id]["failed_at"] = datetime.now().isoformat()

if __name__ == '__main__':
    # 确保必要目录存在
    for dir_name in ['output', 'temp', 'logs', 'templates', 'static']:
        Path(dir_name).mkdir(exist_ok=True)

    print("🚀 启动深度研究系统")
    print("🌐 Web界面: http://localhost:8081")
    print("📖 研究场景: http://localhost:8081/api/research/scenarios")
    print("💡 按 Ctrl+C 停止服务")

    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=8081,
        debug=True,
        threaded=True
    )
    """获取支持的研究场景"""
    try:
        scenarios = {}
        for scenario in ResearchScenario:
            scenarios[scenario.value] = {
                "name": scenario.value,
                "display_name": get_scenario_display_name(scenario),
                "description": get_scenario_description(scenario)
            }
        
        output_formats = {}
        for format_enum in OutputFormat:
            output_formats[format_enum.value] = {
                "name": format_enum.value,
                "display_name": get_format_display_name(format_enum),
                "description": get_format_description(format_enum)
            }
        
        return jsonify({
            "success": True,
            "scenarios": scenarios,
            "output_formats": output_formats,
            "audiences": {
                "general": {"name": "general", "display_name": "一般读者"},
                "professional": {"name": "professional", "display_name": "专业人士"},
                "academic": {"name": "academic", "display_name": "学术研究者"},
                "business": {"name": "business", "display_name": "商业决策者"},
                "student": {"name": "student", "display_name": "学生"}
            }
        })
    except Exception as e:
        logger.error(f"Get scenarios failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/start', methods=['POST'])
def start_workflow():
    """启动工作流"""
    try:
        data = request.json
        
        # 验证必需参数
        if not data.get('user_input'):
            return jsonify({"success": False, "error": "用户输入不能为空"}), 400
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 解析请求参数
        user_input = data['user_input']
        research_scenario = ResearchScenario(data.get('research_scenario', 'book_analysis'))
        output_format = OutputFormat(data.get('output_format', 'research_report'))
        audience = data.get('audience', 'general')
        
        # 创建视频配置
        video_config = None
        if output_format in [OutputFormat.FULL_VIDEO]:
            video_config = VideoConfig()
            video_config.aspect_ratio = data.get('aspect_ratio', '16:9')
            video_config.resolution = data.get('resolution', '1920x1080')
        
        # 创建工作流请求
        workflow_request = WorkflowRequest(
            user_input=user_input,
            research_scenario=research_scenario,
            output_format=output_format,
            audience=audience,
            video_config=video_config,
            additional_requirements=data.get('additional_requirements', {})
        )
        
        # 初始化任务状态
        task_status[task_id] = {
            "status": TaskStatus.PENDING,
            "current_step": "初始化",
            "progress": 0,
            "message": "任务已创建，等待处理",
            "created_at": datetime.now().isoformat(),
            "request": {
                "user_input": user_input,
                "research_scenario": research_scenario.value,
                "output_format": output_format.value,
                "audience": audience
            },
            "steps": []
        }
        
        # 异步处理任务
        thread = threading.Thread(
            target=process_workflow_task, 
            args=(task_id, workflow_request)
        )
        thread.start()
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "工作流已启动",
            "estimated_time": "3-8分钟"
        })
        
    except Exception as e:
        logger.error(f"Start workflow failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/status/<task_id>')
def get_workflow_status(task_id):
    """获取工作流状态"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        
        response = {
            "success": True,
            "task_id": task_id,
            "status": status["status"],
            "current_step": status["current_step"],
            "progress": status["progress"],
            "message": status["message"],
            "created_at": status["created_at"],
            "steps": status["steps"]
        }
        
        # 添加结果信息（如果已完成）
        if status["status"] == TaskStatus.COMPLETED and task_id in task_results:
            result = task_results[task_id]
            response["result"] = {
                "quality_score": result.quality_score,
                "total_processing_time": result.total_processing_time,
                "media_files": result.media_files,
                "final_output_preview": result.final_output[:500] + "..."
            }
        
        # 添加错误信息（如果失败）
        if status["status"] == TaskStatus.FAILED:
            response["error"] = status.get("error", "未知错误")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Get workflow status failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/result/<task_id>')
def get_workflow_result(task_id):
    """获取完整的工作流结果"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        if status["status"] != TaskStatus.COMPLETED:
            return jsonify({"success": False, "error": "任务尚未完成"}), 400
        
        if task_id not in task_results:
            return jsonify({"success": False, "error": "结果不存在"}), 404
        
        result = task_results[task_id]
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "result": {
                "request": {
                    "user_input": result.request.user_input,
                    "research_scenario": result.request.research_scenario.value,
                    "output_format": result.request.output_format.value,
                    "audience": result.request.audience
                },
                "quality_score": result.quality_score,
                "total_processing_time": result.total_processing_time,
                "final_output": result.final_output,
                "media_files": result.media_files,
                "steps": [
                    {
                        "step": step.step.value,
                        "success": step.success,
                        "processing_time": step.processing_time,
                        "content_preview": step.content[:200] + "..." if len(step.content) > 200 else step.content
                    }
                    for step in result.steps
                ],
                "timestamp": result.timestamp.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"Get workflow result failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/download/<task_id>/<int:file_index>')
def download_file(task_id, file_index):
    """下载生成的文件"""
    try:
        if task_id not in task_results:
            return jsonify({"success": False, "error": "任务结果不存在"}), 404
        
        result = task_results[task_id]
        
        if file_index >= len(result.media_files):
            return jsonify({"success": False, "error": "文件索引超出范围"}), 400
        
        file_path = result.media_files[file_index]
        
        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"}), 404
        
        return send_file(file_path, as_attachment=True)
        
    except Exception as e:
        logger.error(f"Download file failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/system/stats')
def get_system_stats():
    """获取系统统计信息"""
    try:
        # 获取工作流统计
        workflow_stats = pipeline.get_workflow_stats()
        
        # 获取任务状态统计
        task_stats = {}
        for status in [TaskStatus.PENDING, TaskStatus.PROCESSING, TaskStatus.COMPLETED, TaskStatus.FAILED]:
            task_stats[status] = sum(1 for t in task_status.values() if t["status"] == status)
        
        return jsonify({
            "success": True,
            "stats": {
                "workflow": workflow_stats,
                "tasks": task_stats,
                "total_tasks": len(task_status),
                "active_tasks": task_stats[TaskStatus.PENDING] + task_stats[TaskStatus.PROCESSING]
            }
        })
        
    except Exception as e:
        logger.error(f"Get system stats failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/voices')
def get_voices():
    """获取可用语音列表"""
    try:
        # 使用真实的语音管理器
        voice_manager = VoiceManager()
        voices = voice_manager.get_voice_list()
        categories = voice_manager.get_voice_categories()
        
        return jsonify({
            "success": True,
            "voices": voices,
            "categories": categories
        })
        
    except Exception as e:
        logger.error(f"Get voices failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/personalities')
def get_personalities():
    """获取评论者风格列表"""
    try:
        # 使用真实的评论者风格管理器
        personality_manager = PersonalityManager()
        personalities = personality_manager.get_personality_list()
        categories = personality_manager.get_personality_categories()
        
        return jsonify({
            "success": True,
            "personalities": personalities,
            "categories": categories
        })
        
    except Exception as e:
        logger.error(f"Get personalities failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/bgm')
def get_bgm():
    """获取背景音乐列表"""
    try:
        # 使用真实的BGM数据
        bgm_metadata_path = Path(__file__).parent / "assets/bgm/bgm_metadata.json"

        if bgm_metadata_path.exists():
            with open(bgm_metadata_path, 'r', encoding='utf-8') as f:
                bgm_data = json.load(f)

            bgm_files = []
            for bgm_item in bgm_data["bgm_files"]:
                # 检查实际文件是否存在
                bgm_file_path = Path(__file__).parent / "assets/bgm" / bgm_item["filename"]

                bgm_files.append({
                    "id": bgm_item["style"],
                    "name": bgm_item["name"],
                    "description": bgm_item["description"],
                    "style": bgm_item["style"],
                    "duration": bgm_item["duration"],
                    "tags": bgm_item["tags"],
                    "filename": bgm_item["filename"],
                    "path": f"/static/bgm/{bgm_item['filename']}",
                    "exists": bgm_file_path.exists()
                })
        else:
            # 回退到模拟数据
            bgm_files = [
                {
                    "id": "gentle",
                    "name": "温柔诗意",
                    "description": "轻柔舒缓的背景音乐",
                    "style": "gentle",
                    "duration": 180,
                    "tags": ["温柔", "舒缓"],
                    "filename": "gentle_bgm.mp3",
                    "path": "/static/bgm/gentle_bgm.mp3",
                    "exists": False
                }
            ]

        return jsonify({
            "success": True,
            "bgm_files": bgm_files,
            "total_count": len(bgm_files)
        })

    except Exception as e:
        logger.error(f"Get BGM failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

# ===== 智能体管理API =====

@app.route('/api/agents')
def get_agents():
    """获取所有智能体状态"""
    try:
        agents_status = agent_manager.get_all_agents_status()

        # 添加智能体能力描述
        agents_info = {}
        for agent_type, status in agents_status.items():
            agent = agent_manager.get_agent(agent_type)
            agents_info[agent_type] = {
                **status,
                "required_inputs": agent.required_inputs,
                "output_schema": {k: v.__name__ for k, v in agent.output_schema.items()},
                "capabilities": _get_agent_capabilities(agent_type)
            }

        return jsonify({
            "success": True,
            "agents": agents_info,
            "total_agents": len(agents_info)
        })

    except Exception as e:
        logger.error(f"Get agents failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/agents/<agent_type>/config', methods=['GET', 'POST'])
def manage_agent_config(agent_type):
    """管理智能体配置"""
    try:
        agent = agent_manager.get_agent(agent_type)
        if not agent:
            return jsonify({"success": False, "error": "智能体不存在"}), 404

        if request.method == 'GET':
            return jsonify({
                "success": True,
                "agent_type": agent_type,
                "config": agent.config,
                "status": agent.get_status()
            })

        elif request.method == 'POST':
            new_config = request.json.get('config', {})
            agent.config.update(new_config)

            return jsonify({
                "success": True,
                "message": f"智能体 {agent_type} 配置已更新",
                "config": agent.config
            })

    except Exception as e:
        logger.error(f"Manage agent config failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/mcp/providers')
def get_mcp_providers():
    """获取MCP提供商配置"""
    try:
        mcp_agent = agent_manager.get_agent('mcp')
        if not mcp_agent:
            return jsonify({"success": False, "error": "MCP智能体未初始化"}), 500

        # 获取提供商信息
        providers_info = {}
        for provider_id, provider_config in mcp_agent.providers.items():
            providers_info[provider_id] = {
                "provider_id": provider_config.provider_id,
                "provider_name": provider_config.provider_name,
                "provider_type": provider_config.provider_type.value,
                "model_name": provider_config.model_name,
                "max_requests_per_minute": provider_config.max_requests_per_minute,
                "timeout_seconds": provider_config.timeout_seconds,
                "capabilities": provider_config.capabilities,
                "cost_per_request": provider_config.cost_per_request,
                "fallback_providers": provider_config.fallback_providers
            }

        provider_types = [ptype.value for ptype in ProviderType]

        return jsonify({
            "success": True,
            "providers": providers_info,
            "provider_types": provider_types,
            "total_providers": len(providers_info)
        })

    except Exception as e:
        logger.error(f"Get MCP providers failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/mcp/providers', methods=['POST'])
def add_mcp_provider():
    """添加MCP提供商"""
    try:
        data = request.json

        provider_config = ProviderConfig(
            provider_id=data['provider_id'],
            provider_name=data['provider_name'],
            provider_type=ProviderType(data['provider_type']),
            api_url=data['api_url'],
            api_key=data['api_key'],
            model_name=data['model_name'],
            max_requests_per_minute=data.get('max_requests_per_minute', 60),
            timeout_seconds=data.get('timeout_seconds', 30),
            fallback_providers=data.get('fallback_providers', []),
            capabilities=data.get('capabilities', []),
            cost_per_request=data.get('cost_per_request', 0.0)
        )

        mcp_agent = agent_manager.get_agent('mcp')

        # 直接添加到提供商字典
        mcp_agent.providers[provider_config.provider_id] = provider_config

        return jsonify({
            "success": True,
            "message": f"提供商 {data['provider_name']} 添加成功"
        })

    except Exception as e:
        logger.error(f"Add MCP provider failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/mcp/providers/<provider_id>', methods=['DELETE'])
def remove_mcp_provider(provider_id):
    """删除MCP提供商"""
    try:
        mcp_agent = agent_manager.get_agent('mcp')

        if provider_id in mcp_agent.providers:
            del mcp_agent.providers[provider_id]
            return jsonify({
                "success": True,
                "message": f"提供商 {provider_id} 删除成功"
            })
        else:
            return jsonify({"success": False, "error": "提供商不存在"}), 404

    except Exception as e:
        logger.error(f"Remove MCP provider failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/templates')
def get_workflow_templates():
    """获取工作流模板"""
    try:
        workflow_agent = agent_manager.workflow_agent
        templates = workflow_agent.workflow_templates

        # 转换为更友好的格式
        templates_info = {}
        for template_name, steps in templates.items():
            templates_info[template_name] = {
                "name": template_name,
                "steps": [
                    {
                        "step_id": step.step_id,
                        "agent_type": step.agent_type,
                        "depends_on": step.depends_on,
                        "required": step.required
                    } for step in steps
                ],
                "total_steps": len(steps)
            }

        return jsonify({
            "success": True,
            "templates": templates_info,
            "total_templates": len(templates_info)
        })

    except Exception as e:
        logger.error(f"Get workflow templates failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/execute', methods=['POST'])
def execute_custom_workflow():
    """执行自定义工作流"""
    try:
        data = request.json
        workflow_name = data.get('workflow_name')
        initial_data = data.get('initial_data', {})

        if not workflow_name:
            return jsonify({"success": False, "error": "工作流名称不能为空"}), 400

        # 创建任务ID
        task_id = str(uuid.uuid4())

        # 异步执行工作流
        def execute_workflow_async():
            try:
                @run_async
                async def run_workflow():
                    return await agent_manager.workflow_agent.execute_workflow_by_name(
                        workflow_name, initial_data
                    )

                result = run_workflow()

                # 更新任务状态
                task_status[task_id] = {
                    "status": TaskStatus.COMPLETED if result.success else TaskStatus.FAILED,
                    "result": result.output_data if result.success else None,
                    "error": result.error_message if not result.success else None,
                    "completed_at": datetime.now().isoformat()
                }

            except Exception as e:
                task_status[task_id] = {
                    "status": TaskStatus.FAILED,
                    "error": str(e),
                    "failed_at": datetime.now().isoformat()
                }

        # 初始化任务状态
        task_status[task_id] = {
            "status": TaskStatus.PROCESSING,
            "workflow_name": workflow_name,
            "created_at": datetime.now().isoformat()
        }

        # 启动异步执行
        thread = threading.Thread(target=execute_workflow_async)
        thread.start()

        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": f"工作流 {workflow_name} 已启动"
        })

    except Exception as e:
        logger.error(f"Execute custom workflow failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/agents/<agent_type>/execute', methods=['POST'])
def execute_single_agent(agent_type):
    """执行单个智能体任务"""
    try:
        agent = agent_manager.get_agent(agent_type)
        if not agent:
            return jsonify({"success": False, "error": "智能体不存在"}), 404

        data = request.json
        input_data = data.get('input_data', {})
        priority = TaskPriority(data.get('priority', 'MEDIUM'))

        # 创建任务ID
        task_id = str(uuid.uuid4())

        # 异步执行智能体任务
        def execute_agent_async():
            try:
                @run_async
                async def run_agent():
                    return await agent.run(input_data, priority)

                result = run_agent()

                # 更新任务状态
                task_status[task_id] = {
                    "status": TaskStatus.COMPLETED if result.success else TaskStatus.FAILED,
                    "result": result.output_data if result.success else None,
                    "error": result.error_message if not result.success else None,
                    "processing_time": result.processing_time,
                    "completed_at": datetime.now().isoformat()
                }

            except Exception as e:
                task_status[task_id] = {
                    "status": TaskStatus.FAILED,
                    "error": str(e),
                    "failed_at": datetime.now().isoformat()
                }

        # 初始化任务状态
        task_status[task_id] = {
            "status": TaskStatus.PROCESSING,
            "agent_type": agent_type,
            "created_at": datetime.now().isoformat()
        }

        # 启动异步执行
        thread = threading.Thread(target=execute_agent_async)
        thread.start()

        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": f"智能体 {agent_type} 任务已启动"
        })

    except Exception as e:
        logger.error(f"Execute single agent failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

# ===== 辅助函数 =====

def _get_agent_capabilities(agent_type: str) -> List[str]:
    """获取智能体能力描述"""
    capabilities_map = {
        'audio': [
            "多情感语音合成",
            "音频时间轴管理",
            "声音一致性控制",
            "背景音乐集成"
        ],
        'image_context': [
            "上下文图像生成",
            "动态背景序列",
            "视觉叙事规划",
            "图像转场效果"
        ],
        'mcp': [
            "多提供商管理",
            "智能路由选择",
            "成本优化",
            "故障回退机制"
        ],
        'search': [
            "概念语义扩展",
            "智能搜索增强",
            "用户意图推理",
            "相关主题发现"
        ],
        'video': [
            "专业视频组装",
            "动态效果处理",
            "多格式输出",
            "质量报告生成"
        ],
        'hitl': [
            "人机交互管理",
            "用户选择收集",
            "偏好学习",
            "决策支持"
        ],
        'scripting': [
            "多风格脚本生成",
            "多语言支持",
            "受众适配",
            "内容结构优化"
        ]
    }
    return capabilities_map.get(agent_type, [])

def process_workflow_task(task_id: str, workflow_request: WorkflowRequest):
    """处理工作流任务"""
    try:
        # 更新状态为处理中
        task_status[task_id]["status"] = TaskStatus.PROCESSING
        task_status[task_id]["current_step"] = "开始处理"
        task_status[task_id]["progress"] = 5
        
        # 运行异步工作流
        @run_async
        async def run_workflow():
            return await pipeline.execute_workflow(workflow_request)
        
        # 定义步骤映射和进度
        step_progress = {
            WorkflowStep.USER_INPUT: (10, "处理用户输入"),
            WorkflowStep.PROMPT_ENHANCEMENT: (25, "增强提示词"),
            WorkflowStep.DEEP_RESEARCH: (50, "执行深度研究"),
            WorkflowStep.SCRIPT_GENERATION: (75, "生成脚本"),
            WorkflowStep.MEDIA_PRODUCTION: (90, "制作媒体")
        }
        
        # 执行工作流，但我们需要监控进度
        # 由于异步执行无法直接监控，我们模拟进度更新
        def update_progress():
            steps = list(step_progress.keys())
            for i, step in enumerate(steps):
                if task_status[task_id]["status"] != TaskStatus.PROCESSING:
                    break
                    
                progress, message = step_progress[step]
                task_status[task_id]["progress"] = progress
                task_status[task_id]["current_step"] = message
                task_status[task_id]["steps"].append({
                    "step": step.value,
                    "status": "processing",
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                })
                
                # 模拟处理时间
                if i < len(steps) - 1:  # 不在最后一步等待
                    time.sleep(2)
        
        # 启动进度更新线程
        progress_thread = threading.Thread(target=update_progress)
        progress_thread.start()
        
        # 执行实际工作流
        result = run_workflow()
        
        # 等待进度线程完成
        progress_thread.join()
        
        # 更新完成状态
        task_status[task_id]["status"] = TaskStatus.COMPLETED
        task_status[task_id]["progress"] = 100
        task_status[task_id]["current_step"] = "完成"
        task_status[task_id]["message"] = "工作流已成功完成"
        task_status[task_id]["completed_at"] = datetime.now().isoformat()
        
        # 存储结果
        task_results[task_id] = result
        
        # 更新步骤完成状态
        for step_result in result.steps:
            for step_status in task_status[task_id]["steps"]:
                if step_status["step"] == step_result.step.value:
                    step_status["status"] = "completed" if step_result.success else "failed"
                    step_status["processing_time"] = step_result.processing_time
                    break
        
    except Exception as e:
        logger.error(f"Process workflow task failed: {str(e)}")
        task_status[task_id]["status"] = TaskStatus.FAILED
        task_status[task_id]["message"] = f"任务失败: {str(e)}"
        task_status[task_id]["error"] = str(e)
        task_status[task_id]["failed_at"] = datetime.now().isoformat()

def get_scenario_display_name(scenario: ResearchScenario) -> str:
    """获取场景显示名称"""
    names = {
        ResearchScenario.BOOK_ANALYSIS: "图书深度分析",
        ResearchScenario.INDUSTRY_REPORT: "行业研究报告",
        ResearchScenario.RESEARCH_PROGRESS: "研究进展分析",
        ResearchScenario.NEWS_ANALYSIS: "新闻深度解读",
        ResearchScenario.MARKET_ANALYSIS: "市场分析报告",
        ResearchScenario.BOOK_RECOMMENDATION: "图书推荐评价"
    }
    return names.get(scenario, scenario.value)

def get_scenario_description(scenario: ResearchScenario) -> str:
    """获取场景描述"""
    descriptions = {
        ResearchScenario.BOOK_ANALYSIS: "基于您的原始DeepResearch设计，对书籍进行6个维度的深度分析",
        ResearchScenario.INDUSTRY_REPORT: "专业的行业分析报告，包含市场规模、竞争格局、发展趋势等",
        ResearchScenario.RESEARCH_PROGRESS: "学术研究进展的系统梳理和前沿动态分析",
        ResearchScenario.NEWS_ANALYSIS: "新闻事件的深度解读和影响分析",
        ResearchScenario.MARKET_ANALYSIS: "市场供需、价格走势和投资机会分析",
        ResearchScenario.BOOK_RECOMMENDATION: "图书推荐理由、适读人群和价值评估"
    }
    return descriptions.get(scenario, "深度研究分析")

def get_format_display_name(format_enum: OutputFormat) -> str:
    """获取格式显示名称"""
    names = {
        OutputFormat.RESEARCH_REPORT: "研究报告",
        OutputFormat.VIDEO_SCRIPT: "视频脚本",
        OutputFormat.PODCAST_SCRIPT: "播客脚本",
        OutputFormat.FULL_VIDEO: "完整视频",
        OutputFormat.AUDIO_PODCAST: "音频播客"
    }
    return names.get(format_enum, format_enum.value)

def get_format_description(format_enum: OutputFormat) -> str:
    """获取格式描述"""
    descriptions = {
        OutputFormat.RESEARCH_REPORT: "详细的文字研究报告",
        OutputFormat.VIDEO_SCRIPT: "适合视频制作的脚本，包含视觉提示",
        OutputFormat.PODCAST_SCRIPT: "适合音频播放的对话式脚本",
        OutputFormat.FULL_VIDEO: "自动生成的完整视频文件",
        OutputFormat.AUDIO_PODCAST: "自动生成的音频播客文件"
    }
    return descriptions.get(format_enum, "未知格式")

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "页面或接口不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "内部服务器错误"}), 500

if __name__ == '__main__':
    # 确保必要目录存在
    for dir_name in ['output', 'temp', 'logs', 'templates', 'static']:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("🚀 启动增强版深度研究系统")
    print("🌐 Web界面: http://localhost:8081")
    print("📖 API文档: http://localhost:8081/api/scenarios")
    print("💡 按 Ctrl+C 停止服务")
    
    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=8081,
        debug=True,
        threaded=True
    )