"""
Enhanced App
增强版应用 - 集成深度研究Agent的统一Web UI

提供完整的工作流：用户输入 → 提示词增强 → 深度研究 → 脚本生成 → 媒体制作
"""

from flask import Flask, render_template, request, jsonify, send_file, session
from flask_cors import CORS
import asyncio
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
import logging
import os

# 导入核心组件
import sys
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from enhanced_workflow_pipeline import (
        EnhancedWorkflowPipeline, 
        WorkflowRequest,
        WorkflowStep,
        OutputFormat
    )
    from universal_research_template import ResearchScenario
    from video_config import VideoConfig
    from voice_config import VoiceManager
    from reviewer_personalities import PersonalityManager
    print("✅ 所有核心组件加载成功")
except ImportError as e:
    print(f"❌ 核心组件导入失败: {str(e)}")
    raise

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)
app.secret_key = 'enhanced_research_system_2024'

# 全局组件
pipeline = EnhancedWorkflowPipeline()

# 任务状态存储
task_status = {}
task_results = {}

class TaskStatus:
    PENDING = "pending"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"

def run_async(func):
    """异步函数装饰器"""
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(func(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

# ===== Web UI Routes =====

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/workflow')
def workflow_page():
    """工作流页面"""
    return render_template('workflow.html')

@app.route('/results/<task_id>')
def results_page(task_id):
    """结果页面"""
    return render_template('results.html', task_id=task_id)

# ===== API Routes =====

@app.route('/api/scenarios')
def get_scenarios():
    """获取支持的研究场景"""
    try:
        scenarios = {}
        for scenario in ResearchScenario:
            scenarios[scenario.value] = {
                "name": scenario.value,
                "display_name": get_scenario_display_name(scenario),
                "description": get_scenario_description(scenario)
            }
        
        output_formats = {}
        for format_enum in OutputFormat:
            output_formats[format_enum.value] = {
                "name": format_enum.value,
                "display_name": get_format_display_name(format_enum),
                "description": get_format_description(format_enum)
            }
        
        return jsonify({
            "success": True,
            "scenarios": scenarios,
            "output_formats": output_formats,
            "audiences": {
                "general": {"name": "general", "display_name": "一般读者"},
                "professional": {"name": "professional", "display_name": "专业人士"},
                "academic": {"name": "academic", "display_name": "学术研究者"},
                "business": {"name": "business", "display_name": "商业决策者"},
                "student": {"name": "student", "display_name": "学生"}
            }
        })
    except Exception as e:
        logger.error(f"Get scenarios failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/start', methods=['POST'])
def start_workflow():
    """启动工作流"""
    try:
        data = request.json
        
        # 验证必需参数
        if not data.get('user_input'):
            return jsonify({"success": False, "error": "用户输入不能为空"}), 400
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 解析请求参数
        user_input = data['user_input']
        research_scenario = ResearchScenario(data.get('research_scenario', 'book_analysis'))
        output_format = OutputFormat(data.get('output_format', 'research_report'))
        audience = data.get('audience', 'general')
        
        # 创建视频配置
        video_config = None
        if output_format in [OutputFormat.FULL_VIDEO]:
            video_config = VideoConfig()
            video_config.aspect_ratio = data.get('aspect_ratio', '16:9')
            video_config.resolution = data.get('resolution', '1920x1080')
        
        # 创建工作流请求
        workflow_request = WorkflowRequest(
            user_input=user_input,
            research_scenario=research_scenario,
            output_format=output_format,
            audience=audience,
            video_config=video_config,
            additional_requirements=data.get('additional_requirements', {})
        )
        
        # 初始化任务状态
        task_status[task_id] = {
            "status": TaskStatus.PENDING,
            "current_step": "初始化",
            "progress": 0,
            "message": "任务已创建，等待处理",
            "created_at": datetime.now().isoformat(),
            "request": {
                "user_input": user_input,
                "research_scenario": research_scenario.value,
                "output_format": output_format.value,
                "audience": audience
            },
            "steps": []
        }
        
        # 异步处理任务
        thread = threading.Thread(
            target=process_workflow_task, 
            args=(task_id, workflow_request)
        )
        thread.start()
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "工作流已启动",
            "estimated_time": "3-8分钟"
        })
        
    except Exception as e:
        logger.error(f"Start workflow failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/status/<task_id>')
def get_workflow_status(task_id):
    """获取工作流状态"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        
        response = {
            "success": True,
            "task_id": task_id,
            "status": status["status"],
            "current_step": status["current_step"],
            "progress": status["progress"],
            "message": status["message"],
            "created_at": status["created_at"],
            "steps": status["steps"]
        }
        
        # 添加结果信息（如果已完成）
        if status["status"] == TaskStatus.COMPLETED and task_id in task_results:
            result = task_results[task_id]
            response["result"] = {
                "quality_score": result.quality_score,
                "total_processing_time": result.total_processing_time,
                "media_files": result.media_files,
                "final_output_preview": result.final_output[:500] + "..."
            }
        
        # 添加错误信息（如果失败）
        if status["status"] == TaskStatus.FAILED:
            response["error"] = status.get("error", "未知错误")
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Get workflow status failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/result/<task_id>')
def get_workflow_result(task_id):
    """获取完整的工作流结果"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        if status["status"] != TaskStatus.COMPLETED:
            return jsonify({"success": False, "error": "任务尚未完成"}), 400
        
        if task_id not in task_results:
            return jsonify({"success": False, "error": "结果不存在"}), 404
        
        result = task_results[task_id]
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "result": {
                "request": {
                    "user_input": result.request.user_input,
                    "research_scenario": result.request.research_scenario.value,
                    "output_format": result.request.output_format.value,
                    "audience": result.request.audience
                },
                "quality_score": result.quality_score,
                "total_processing_time": result.total_processing_time,
                "final_output": result.final_output,
                "media_files": result.media_files,
                "steps": [
                    {
                        "step": step.step.value,
                        "success": step.success,
                        "processing_time": step.processing_time,
                        "content_preview": step.content[:200] + "..." if len(step.content) > 200 else step.content
                    }
                    for step in result.steps
                ],
                "timestamp": result.timestamp.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"Get workflow result failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/workflow/download/<task_id>/<int:file_index>')
def download_file(task_id, file_index):
    """下载生成的文件"""
    try:
        if task_id not in task_results:
            return jsonify({"success": False, "error": "任务结果不存在"}), 404
        
        result = task_results[task_id]
        
        if file_index >= len(result.media_files):
            return jsonify({"success": False, "error": "文件索引超出范围"}), 400
        
        file_path = result.media_files[file_index]
        
        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"}), 404
        
        return send_file(file_path, as_attachment=True)
        
    except Exception as e:
        logger.error(f"Download file failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/system/stats')
def get_system_stats():
    """获取系统统计信息"""
    try:
        # 获取工作流统计
        workflow_stats = pipeline.get_workflow_stats()
        
        # 获取任务状态统计
        task_stats = {}
        for status in [TaskStatus.PENDING, TaskStatus.PROCESSING, TaskStatus.COMPLETED, TaskStatus.FAILED]:
            task_stats[status] = sum(1 for t in task_status.values() if t["status"] == status)
        
        return jsonify({
            "success": True,
            "stats": {
                "workflow": workflow_stats,
                "tasks": task_stats,
                "total_tasks": len(task_status),
                "active_tasks": task_stats[TaskStatus.PENDING] + task_stats[TaskStatus.PROCESSING]
            }
        })
        
    except Exception as e:
        logger.error(f"Get system stats failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/voices')
def get_voices():
    """获取可用语音列表"""
    try:
        # 使用真实的语音管理器
        voice_manager = VoiceManager()
        voices = voice_manager.get_voice_list()
        categories = voice_manager.get_voice_categories()
        
        return jsonify({
            "success": True,
            "voices": voices,
            "categories": categories
        })
        
    except Exception as e:
        logger.error(f"Get voices failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/personalities')
def get_personalities():
    """获取评论者风格列表"""
    try:
        # 使用真实的评论者风格管理器
        personality_manager = PersonalityManager()
        personalities = personality_manager.get_personality_list()
        categories = personality_manager.get_personality_categories()
        
        return jsonify({
            "success": True,
            "personalities": personalities,
            "categories": categories
        })
        
    except Exception as e:
        logger.error(f"Get personalities failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/bgm')
def get_bgm():
    """获取背景音乐列表"""
    try:
        # 使用真实的BGM数据
        bgm_metadata_path = Path(__file__).parent / "assets/bgm/bgm_metadata.json"
        
        if bgm_metadata_path.exists():
            with open(bgm_metadata_path, 'r', encoding='utf-8') as f:
                bgm_data = json.load(f)
            
            bgm_files = []
            for bgm_item in bgm_data["bgm_files"]:
                # 检查实际文件是否存在
                bgm_file_path = Path(__file__).parent / "assets/bgm" / bgm_item["filename"]
                
                bgm_files.append({
                    "id": bgm_item["style"],
                    "name": bgm_item["name"],
                    "description": bgm_item["description"],
                    "style": bgm_item["style"],
                    "duration": bgm_item["duration"],
                    "tags": bgm_item["tags"],
                    "filename": bgm_item["filename"],
                    "path": f"/static/bgm/{bgm_item['filename']}",
                    "exists": bgm_file_path.exists()
                })
        else:
            # 回退到模拟数据
            bgm_files = [
                {
                    "id": "gentle",
                    "name": "温柔诗意",
                    "description": "轻柔舒缓的背景音乐",
                    "style": "gentle",
                    "duration": 180,
                    "tags": ["温柔", "舒缓"],
                    "filename": "gentle_bgm.mp3",
                    "path": "/static/bgm/gentle_bgm.mp3",
                    "exists": False
                }
            ]
        
        return jsonify({
            "success": True,
            "bgm_files": bgm_files,
            "total_count": len(bgm_files)
        })
        
    except Exception as e:
        logger.error(f"Get BGM failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

# ===== 辅助函数 =====

def process_workflow_task(task_id: str, workflow_request: WorkflowRequest):
    """处理工作流任务"""
    try:
        # 更新状态为处理中
        task_status[task_id]["status"] = TaskStatus.PROCESSING
        task_status[task_id]["current_step"] = "开始处理"
        task_status[task_id]["progress"] = 5
        
        # 运行异步工作流
        @run_async
        async def run_workflow():
            return await pipeline.execute_workflow(workflow_request)
        
        # 定义步骤映射和进度
        step_progress = {
            WorkflowStep.USER_INPUT: (10, "处理用户输入"),
            WorkflowStep.PROMPT_ENHANCEMENT: (25, "增强提示词"),
            WorkflowStep.DEEP_RESEARCH: (50, "执行深度研究"),
            WorkflowStep.SCRIPT_GENERATION: (75, "生成脚本"),
            WorkflowStep.MEDIA_PRODUCTION: (90, "制作媒体")
        }
        
        # 执行工作流，但我们需要监控进度
        # 由于异步执行无法直接监控，我们模拟进度更新
        def update_progress():
            steps = list(step_progress.keys())
            for i, step in enumerate(steps):
                if task_status[task_id]["status"] != TaskStatus.PROCESSING:
                    break
                    
                progress, message = step_progress[step]
                task_status[task_id]["progress"] = progress
                task_status[task_id]["current_step"] = message
                task_status[task_id]["steps"].append({
                    "step": step.value,
                    "status": "processing",
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                })
                
                # 模拟处理时间
                if i < len(steps) - 1:  # 不在最后一步等待
                    time.sleep(2)
        
        # 启动进度更新线程
        progress_thread = threading.Thread(target=update_progress)
        progress_thread.start()
        
        # 执行实际工作流
        result = run_workflow()
        
        # 等待进度线程完成
        progress_thread.join()
        
        # 更新完成状态
        task_status[task_id]["status"] = TaskStatus.COMPLETED
        task_status[task_id]["progress"] = 100
        task_status[task_id]["current_step"] = "完成"
        task_status[task_id]["message"] = "工作流已成功完成"
        task_status[task_id]["completed_at"] = datetime.now().isoformat()
        
        # 存储结果
        task_results[task_id] = result
        
        # 更新步骤完成状态
        for step_result in result.steps:
            for step_status in task_status[task_id]["steps"]:
                if step_status["step"] == step_result.step.value:
                    step_status["status"] = "completed" if step_result.success else "failed"
                    step_status["processing_time"] = step_result.processing_time
                    break
        
    except Exception as e:
        logger.error(f"Process workflow task failed: {str(e)}")
        task_status[task_id]["status"] = TaskStatus.FAILED
        task_status[task_id]["message"] = f"任务失败: {str(e)}"
        task_status[task_id]["error"] = str(e)
        task_status[task_id]["failed_at"] = datetime.now().isoformat()

def get_scenario_display_name(scenario: ResearchScenario) -> str:
    """获取场景显示名称"""
    names = {
        ResearchScenario.BOOK_ANALYSIS: "图书深度分析",
        ResearchScenario.INDUSTRY_REPORT: "行业研究报告",
        ResearchScenario.RESEARCH_PROGRESS: "研究进展分析",
        ResearchScenario.NEWS_ANALYSIS: "新闻深度解读",
        ResearchScenario.MARKET_ANALYSIS: "市场分析报告",
        ResearchScenario.BOOK_RECOMMENDATION: "图书推荐评价"
    }
    return names.get(scenario, scenario.value)

def get_scenario_description(scenario: ResearchScenario) -> str:
    """获取场景描述"""
    descriptions = {
        ResearchScenario.BOOK_ANALYSIS: "基于您的原始DeepResearch设计，对书籍进行6个维度的深度分析",
        ResearchScenario.INDUSTRY_REPORT: "专业的行业分析报告，包含市场规模、竞争格局、发展趋势等",
        ResearchScenario.RESEARCH_PROGRESS: "学术研究进展的系统梳理和前沿动态分析",
        ResearchScenario.NEWS_ANALYSIS: "新闻事件的深度解读和影响分析",
        ResearchScenario.MARKET_ANALYSIS: "市场供需、价格走势和投资机会分析",
        ResearchScenario.BOOK_RECOMMENDATION: "图书推荐理由、适读人群和价值评估"
    }
    return descriptions.get(scenario, "深度研究分析")

def get_format_display_name(format_enum: OutputFormat) -> str:
    """获取格式显示名称"""
    names = {
        OutputFormat.RESEARCH_REPORT: "研究报告",
        OutputFormat.VIDEO_SCRIPT: "视频脚本",
        OutputFormat.PODCAST_SCRIPT: "播客脚本",
        OutputFormat.FULL_VIDEO: "完整视频",
        OutputFormat.AUDIO_PODCAST: "音频播客"
    }
    return names.get(format_enum, format_enum.value)

def get_format_description(format_enum: OutputFormat) -> str:
    """获取格式描述"""
    descriptions = {
        OutputFormat.RESEARCH_REPORT: "详细的文字研究报告",
        OutputFormat.VIDEO_SCRIPT: "适合视频制作的脚本，包含视觉提示",
        OutputFormat.PODCAST_SCRIPT: "适合音频播放的对话式脚本",
        OutputFormat.FULL_VIDEO: "自动生成的完整视频文件",
        OutputFormat.AUDIO_PODCAST: "自动生成的音频播客文件"
    }
    return descriptions.get(format_enum, "未知格式")

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "页面或接口不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "内部服务器错误"}), 500

if __name__ == '__main__':
    # 确保必要目录存在
    for dir_name in ['output', 'temp', 'logs', 'templates', 'static']:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("🚀 启动增强版深度研究系统")
    print("🌐 Web界面: http://localhost:8081")
    print("📖 API文档: http://localhost:8081/api/scenarios")
    print("💡 按 Ctrl+C 停止服务")
    
    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=8081,
        debug=True,
        threaded=True
    )