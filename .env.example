# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Bytedance Doubao API Configuration
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_IMAGE_ENDPOINT=https://api.doubao.com/v1/images/generate
DOUBAO_TTS_ENDPOINT=https://api.doubao.com/v1/audio/speech

# Optional: Backup TTS service
EDGE_TTS_VOICE=zh-CN-XiaoxiaoNeural

# Flask Configuration
FLASK_PORT=5000
FLASK_DEBUG=False

# Output Configuration
OUTPUT_DIR=./outputs
TEMP_DIR=./temp