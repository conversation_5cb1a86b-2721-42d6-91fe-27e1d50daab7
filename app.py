from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import threading
from src.pipeline import BookReviewPipeline
from src.config import Config
from src.video_config import VideoConfig, VideoConfigManager, AspectRatio, VideoQuality, SubtitleStyle
from src.doubao_tts import DoubaoTTS
from src.reviewer_personalities import ReviewerPersonalities

app = Flask(__name__)
CORS(app)

# Store job status
jobs = {}

def process_content_async(job_id, prompt_text, video_config, voice_id=None, personality_id=None, use_web_data=True, bgm_path=None):
    """Process content in background thread"""
    pipeline = BookReviewPipeline(video_config, voice_id, personality_id, use_web_data)
    result = pipeline.process_content(prompt_text, bgm_path, job_id)
    jobs[job_id] = result

@app.route('/')
def index():
    """Render main page"""
    return render_template('index.html')

@app.route('/api/generate', methods=['POST'])
def generate_video():
    """API endpoint to start video generation"""
    try:
        data = request.json
        prompt_text = data.get('prompt_text', '').strip()
        bgm_path = data.get('bgm_path')
        
        # Parse video configuration
        video_config_data = data.get('video_config', {})
        video_config = parse_video_config(video_config_data)
        
        # Parse voice selection
        voice_id = data.get('voice_id')
        print(f"🎯 Voice selection: {voice_id}")
        
        # Parse personality selection
        personality_id = data.get('personality_id')
        print(f"🎭 Personality selection: {personality_id}")
        
        # Parse web data enhancement option
        use_web_data = data.get('use_web_data', True)
        print(f"🌐 Use web data enhancement: {use_web_data}")
        
        # Validate voice ID against Doubao TTS only
        if voice_id:
            doubao_tts_temp = DoubaoTTS()
            if not doubao_tts_temp.is_configured:
                return jsonify({"error": "Doubao TTS not configured. Please set DOUBAO_APP_ID and DOUBAO_ACCESS_TOKEN."}), 503
            
            is_doubao_voice = doubao_tts_temp.validate_voice(voice_id)
            print(f"📍 Doubao TTS validation for {voice_id}: {is_doubao_voice}")
            
            if not is_doubao_voice:
                print(f"❌ Voice ID {voice_id} is not valid for Doubao TTS")
                return jsonify({"error": "Invalid voice ID"}), 400
        
        if personality_id and not ReviewerPersonalities.validate_personality_id(personality_id):
            return jsonify({"error": "Invalid personality ID"}), 400
        
        if not prompt_text:
            return jsonify({"error": "Content prompt is required"}), 400
        
        # Validate video configuration
        if not VideoConfigManager.validate_config(video_config):
            return jsonify({"error": "Invalid video configuration"}), 400
        
        # Create job
        import uuid
        job_id = str(uuid.uuid4())
        jobs[job_id] = {
            "status": "processing", 
            "message": "Starting video generation...",
            "video_config": video_config_data,
            "voice_id": voice_id,
            "personality_id": personality_id,
            "use_web_data": use_web_data
        }
        
        # Start processing in background
        thread = threading.Thread(
            target=process_content_async,
            args=(job_id, prompt_text, video_config, voice_id, personality_id, use_web_data, bgm_path)
        )
        thread.start()
        
        return jsonify({"job_id": job_id, "status": "processing"})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def parse_video_config(config_data: dict) -> VideoConfig:
    """Parse video configuration from request data"""
    # Handle preset
    preset_name = config_data.get('preset')
    if preset_name:
        return VideoConfigManager.create_preset(preset_name)
    
    # Parse individual settings
    try:
        aspect_ratio = AspectRatio(config_data.get('aspect_ratio', '9:16'))
        quality = VideoQuality(config_data.get('quality', 'Full HD'))
        subtitle_style = SubtitleStyle(config_data.get('subtitle_style', 'bottom_center'))
        
        return VideoConfig(
            aspect_ratio=aspect_ratio,
            quality=quality,
            fps=config_data.get('fps', 30),
            subtitle_style=subtitle_style,
            subtitle_font_size=config_data.get('subtitle_font_size', 42),
            bgm_volume=config_data.get('bgm_volume', 0.2),
            opening_duration=config_data.get('opening_duration', 2.0),
            enable_subtitles=config_data.get('enable_subtitles', True),
            enable_fade_effects=config_data.get('enable_fade_effects', True)
        )
    except ValueError as e:
        # Return default config if parsing fails
        print(f"Invalid config, using default: {e}")
        return VideoConfig()

@app.route('/api/status/<job_id>')
def get_status(job_id):
    """Get job status"""
    if job_id not in jobs:
        return jsonify({"error": "Job not found"}), 404
    
    return jsonify(jobs[job_id])

@app.route('/api/config/presets')
def get_video_presets():
    """Get available video configuration presets"""
    presets = VideoConfigManager.get_available_presets()
    return jsonify({"presets": presets})

@app.route('/api/config/options')
def get_config_options():
    """Get available video configuration options"""
    return jsonify({
        "aspect_ratios": [ratio.value for ratio in AspectRatio],
        "qualities": [quality.value for quality in VideoQuality],
        "subtitle_styles": [style.value for style in SubtitleStyle],
        "fps_options": [24, 30, 60],
        "font_size_range": {"min": 20, "max": 80, "default": 42},
        "bgm_volume_range": {"min": 0.0, "max": 1.0, "default": 0.2},
        "opening_duration_range": {"min": 0.5, "max": 10.0, "default": 2.0}
    })

@app.route('/api/voices')
def get_voices():
    """Get available TTS voices from Doubao TTS only"""
    doubao_tts = DoubaoTTS()
    
    if not doubao_tts.is_configured:
        return jsonify({
            "error": "Doubao TTS not configured. Please set DOUBAO_APP_ID and DOUBAO_ACCESS_TOKEN.",
            "voices": [],
            "categories": {},
            "default": None,
            "providers": {}
        }), 503
    
    doubao_voices = []
    doubao_voices_raw = doubao_tts.get_available_voices()
    
    # Convert Doubao voices to standard format
    for voice_id, voice_info in doubao_voices_raw.items():
        doubao_voices.append({
            "id": voice_id,
            "name": voice_info['name'],
            "gender": voice_info.get("gender", "未知"),
            "description": voice_info.get("description", ""),
            "provider": "doubao",
            "accent": voice_info.get("accent", "标准"),
            "emotion_support": voice_info.get("emotion_support", False),
            "emotions": voice_info.get("emotions", [])
        })
    
    # Create Doubao voice categories
    categories = {
        "多情感音色": [],
        "方言音色": [],
        "角色扮演": [],
        "专业音色": [],
        "多语言": [],
        "标准音色": []
    }
    
    for voice_id, voice_info in doubao_voices_raw.items():
        if voice_info.get("emotion_support"):
            categories["多情感音色"].append(voice_id)
        elif voice_info.get("accent") and voice_info["accent"] != "标准":
            categories["方言音色"].append(voice_id)
        elif voice_info.get("persona"):
            categories["角色扮演"].append(voice_id)
        elif voice_info.get("use_case"):
            categories["专业音色"].append(voice_id)
        elif voice_info.get("languages") and len(voice_info.get("languages", [])) > 1:
            categories["多语言"].append(voice_id)
        else:
            categories["标准音色"].append(voice_id)
    
    # Get first available voice as default
    default_voice = list(doubao_voices_raw.keys())[0] if doubao_voices_raw else None
    
    return jsonify({
        "voices": doubao_voices,
        "categories": categories,
        "default": default_voice,
        "providers": {
            "doubao": {"name": "豆包TTS", "description": "字节跳动官方TTS服务，支持多种情感"}
        }
    })

@app.route('/api/voices/<voice_id>/preview')
def get_voice_preview(voice_id):
    """Generate and serve voice preview for Doubao TTS"""
    try:
        doubao_tts = DoubaoTTS()
        
        # Check if Doubao TTS is configured
        if not doubao_tts.is_configured:
            return jsonify({"error": "Doubao TTS not configured. Please set DOUBAO_APP_ID and DOUBAO_ACCESS_TOKEN environment variables."}), 503
        
        # Check if it's a valid Doubao TTS voice
        if not doubao_tts.validate_voice(voice_id):
            return jsonify({"error": "Invalid voice ID"}), 404
        
        # Generate preview file
        import uuid
        preview_filename = f"voice_preview_{voice_id}_{uuid.uuid4().hex[:8]}.mp3"
        preview_path = os.path.join(Config.OUTPUT_DIR, preview_filename)
        
        # Ensure output directory exists
        os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
        
        # Generate preview using Doubao TTS
        preview_text = "朋友们，今天和大家聊聊这本书。读完之后，我想说，真正的好书，就像一位智慧的老友。"
        success = doubao_tts.synthesize_to_file(preview_text, preview_path, voice_id, "neutral")
        
        if success and os.path.exists(preview_path):
            return send_file(
                preview_path,
                as_attachment=False,
                download_name=f"{voice_id}_preview.mp3",
                mimetype='audio/mpeg'
            )
        else:
            return jsonify({"error": "Failed to generate voice preview"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/personalities')
def get_personalities():
    """Get available reviewer personalities"""
    personalities = ReviewerPersonalities.get_personality_list()
    categories = ReviewerPersonalities.get_personalities_by_category()
    return jsonify({
        "personalities": personalities,
        "categories": categories,
        "default": ReviewerPersonalities.get_default_personality()
    })

@app.route('/api/bgm')
def get_bgm_list():
    """Get available BGM files with metadata"""
    # Create a temporary pipeline just for BGM list
    temp_pipeline = BookReviewPipeline()
    bgm_list = temp_pipeline.get_sample_bgm_list() if hasattr(temp_pipeline, 'get_sample_bgm_list') else []
    
    # Group BGM by style for better organization
    bgm_by_style = {}
    for bgm in bgm_list:
        style = bgm.get('style', 'general')
        if style not in bgm_by_style:
            bgm_by_style[style] = []
        bgm_by_style[style].append(bgm)
    
    return jsonify({
        "bgm_files": bgm_list,
        "bgm_by_style": bgm_by_style,
        "total_count": len(bgm_list)
    })

@app.route('/api/bgm/<filename>/preview')
def get_bgm_preview(filename):
    """Generate and serve BGM preview"""
    try:
        bgm_dir = "./assets/bgm"
        bgm_path = os.path.join(bgm_dir, filename)
        
        # Validate filename and check if file exists
        if not filename.endswith(('.mp3', '.wav', '.m4a')):
            return jsonify({"error": "Invalid BGM file format"}), 400
        
        if not os.path.exists(bgm_path):
            return jsonify({"error": "BGM file not found"}), 404
        
        # Check file size (optional - to prevent serving huge files)
        file_size = os.path.getsize(bgm_path)
        if file_size > 50 * 1024 * 1024:  # 50MB limit
            return jsonify({"error": "BGM file too large for preview"}), 413
        
        # Serve the BGM file directly for preview
        return send_file(
            bgm_path,
            as_attachment=False,
            download_name=f"{filename}_preview",
            mimetype='audio/mpeg' if filename.endswith('.mp3') else 'audio/wav'
        )
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/download/<filename>')
def download_video(filename):
    """Download generated video"""
    file_path = os.path.join(Config.OUTPUT_DIR, filename)
    
    if not os.path.exists(file_path):
        return jsonify({"error": "File not found"}), 404
    
    return send_file(
        file_path,
        mimetype='video/mp4',
        as_attachment=True,
        download_name=filename
    )

@app.route('/preview/<filename>')
def preview_video(filename):
    """Stream video for preview"""
    file_path = os.path.join(Config.OUTPUT_DIR, filename)
    
    if not os.path.exists(file_path):
        return jsonify({"error": "File not found"}), 404
    
    return send_file(
        file_path,
        mimetype='video/mp4',
        as_attachment=False
    )

@app.route('/cover/<path:filename>')
def serve_cover_image(filename):
    """Serve cover images for preview"""
    print(f"Requesting cover image: {filename}")
    
    # Look in temp directories for cover images
    import glob
    search_pattern = os.path.join(Config.TEMP_DIR, "*", filename)
    matches = glob.glob(search_pattern)
    print(f"Search pattern: {search_pattern}")
    print(f"Found matches: {matches}")
    
    if matches:
        image_path = matches[0]
        print(f"Serving image from: {image_path}")
        
        # Check if file actually exists and is readable
        if os.path.exists(image_path) and os.path.getsize(image_path) > 0:
            return send_file(
                image_path,
                mimetype='image/jpeg',
                as_attachment=False
            )
    
    # Try looking in outputs directory as backup
    output_pattern = os.path.join(Config.OUTPUT_DIR, filename)
    if os.path.exists(output_pattern):
        print(f"Found in outputs: {output_pattern}")
        return send_file(
            output_pattern,
            mimetype='image/jpeg',
            as_attachment=False
        )
    
    # Also check if it's a direct path
    if os.path.exists(filename):
        print(f"Found direct path: {filename}")
        return send_file(
            filename,
            mimetype='image/jpeg',
            as_attachment=False
        )
    
    print(f"Cover image not found: {filename}")
    print(f"Temp dir contents: {os.listdir(Config.TEMP_DIR) if os.path.exists(Config.TEMP_DIR) else 'No temp dir'}")
    
    return jsonify({"error": f"Cover image not found: {filename}"}), 404

@app.route('/audio/<job_id>/<filename>')
def serve_audio_file(job_id, filename):
    """Serve audio files for preview"""
    print(f"Requesting audio file: {job_id}/{filename}")
    
    # Look for audio files in temp directories
    import glob
    
    # Try different possible locations
    search_patterns = [
        os.path.join(Config.TEMP_DIR, job_id, filename),
        os.path.join(Config.TEMP_DIR, job_id, "*.mp3"),
        os.path.join(Config.TEMP_DIR, "*", filename),
        os.path.join(Config.OUTPUT_DIR, filename)
    ]
    
    for pattern in search_patterns:
        matches = glob.glob(pattern)
        print(f"Searching pattern: {pattern}, matches: {matches}")
        
        if matches:
            audio_path = matches[0]
            print(f"Serving audio from: {audio_path}")
            
            if os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                return send_file(
                    audio_path,
                    mimetype='audio/mpeg',
                    as_attachment=False
                )
    
    # If no individual file found, try to create a combined audio file
    temp_job_dir = os.path.join(Config.TEMP_DIR, job_id)
    if os.path.exists(temp_job_dir):
        print(f"Looking in job directory: {temp_job_dir}")
        audio_files = glob.glob(os.path.join(temp_job_dir, "*.mp3"))
        
        if audio_files:
            # If we have multiple audio files, serve the first one as a preview
            return send_file(
                audio_files[0],
                mimetype='audio/mpeg',
                as_attachment=False
            )
    
    print(f"Audio file not found: {job_id}/{filename}")
    return jsonify({"error": f"Audio file not found: {job_id}/{filename}"}), 404

if __name__ == '__main__':
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    app.run(host='0.0.0.0', port=port, debug=debug)