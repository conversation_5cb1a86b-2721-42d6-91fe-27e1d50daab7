#!/usr/bin/env python3
"""
测试深度研究系统的文件生成和下载功能
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:8081"

def test_research_workflow():
    """测试完整的研究工作流"""
    print("🧪 开始测试深度研究系统...")
    
    # 1. 测试系统诊断
    print("\n1. 测试系统诊断...")
    try:
        response = requests.get(f"{BASE_URL}/api/system/diagnostics")
        if response.status_code == 200:
            diagnostics = response.json()
            print(f"✅ 系统状态: {diagnostics['diagnostics']['system_status']}")
            print(f"✅ 输出目录存在: {diagnostics['diagnostics']['output_directory']['exists']}")
            print(f"✅ 输出目录可写: {diagnostics['diagnostics']['output_directory']['writable']}")
        else:
            print(f"❌ 系统诊断失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 系统诊断异常: {str(e)}")
        return False
    
    # 2. 启动研究任务
    print("\n2. 启动研究任务...")
    research_data = {
        "user_input": "分析人工智能在教育领域的应用现状和发展趋势",
        "research_scenario": "technology_review",
        "output_format": "research_report",
        "target_audience": "professional",
        "additional_requirements": "请重点关注实际应用案例"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/research/start",
            json=research_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['task_id']
            print(f"✅ 研究任务已启动，任务ID: {task_id}")
        else:
            print(f"❌ 启动研究失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 启动研究异常: {str(e)}")
        return False
    
    # 3. 监控任务进度
    print("\n3. 监控任务进度...")
    max_wait_time = 30  # 最多等待30秒
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{BASE_URL}/api/research/status/{task_id}")
            if response.status_code == 200:
                status = response.json()
                print(f"📊 进度: {status.get('progress', 0)}% - {status.get('current_step', '处理中')}")
                
                if status.get('status') == 'completed':
                    print("✅ 研究任务完成！")
                    break
                elif status.get('status') == 'failed':
                    print(f"❌ 研究任务失败: {status.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ 获取状态失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 监控异常: {str(e)}")
            return False
        
        time.sleep(2)
    
    # 4. 获取研究结果
    print("\n4. 获取研究结果...")
    try:
        response = requests.get(f"{BASE_URL}/api/research/result/{task_id}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 研究质量评分: {result['result']['quality_score']}")
            print(f"✅ 生成文件数量: {len(result['result']['output_files'])}")
            
            output_files = result['result']['output_files']
            for file_type, file_path in output_files.items():
                print(f"📁 {file_type}: {file_path}")
        else:
            print(f"❌ 获取结果失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取结果异常: {str(e)}")
        return False
    
    # 5. 测试文件下载
    print("\n5. 测试文件下载...")
    for file_type in output_files.keys():
        try:
            response = requests.get(f"{BASE_URL}/api/research/download/{task_id}/{file_type}")
            if response.status_code == 200:
                print(f"✅ {file_type} 下载成功，文件大小: {len(response.content)} 字节")
            else:
                error_info = response.json() if response.headers.get('content-type') == 'application/json' else response.text
                print(f"❌ {file_type} 下载失败: {response.status_code} - {error_info}")
                return False
        except Exception as e:
            print(f"❌ {file_type} 下载异常: {str(e)}")
            return False
    
    print("\n🎉 所有测试通过！深度研究系统工作正常。")
    return True

def test_multiple_formats():
    """测试多种输出格式"""
    print("\n🧪 测试多种输出格式...")
    
    formats = [
        ("research_report", "深度研究报告"),
        ("podcast_script", "播客对话脚本"),
        ("audio_podcast", "多人对谈音频"),
        ("complete_video", "完整视频内容")
    ]
    
    for format_id, format_name in formats:
        print(f"\n测试格式: {format_name}")
        
        research_data = {
            "user_input": f"测试{format_name}生成功能",
            "research_scenario": "academic_research",
            "output_format": format_id,
            "target_audience": "general"
        }
        
        try:
            # 启动任务
            response = requests.post(
                f"{BASE_URL}/api/research/start",
                json=research_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                task_id = response.json()['task_id']
                print(f"✅ {format_name} 任务启动成功: {task_id}")
                
                # 等待完成
                time.sleep(12)  # 等待任务完成
                
                # 检查结果
                result_response = requests.get(f"{BASE_URL}/api/research/result/{task_id}")
                if result_response.status_code == 200:
                    print(f"✅ {format_name} 生成成功")
                else:
                    print(f"❌ {format_name} 生成失败")
            else:
                print(f"❌ {format_name} 启动失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {format_name} 测试异常: {str(e)}")

if __name__ == "__main__":
    print("深度研究系统测试工具")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/api/system/diagnostics", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行或无法访问")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print("请确保研究系统正在运行: python3 research_app.py")
        sys.exit(1)
    
    # 运行基础测试
    if test_research_workflow():
        print("\n" + "=" * 50)
        print("✅ 基础功能测试通过！")
        
        # 询问是否运行扩展测试
        try:
            choice = input("\n是否运行多格式测试？(y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                test_multiple_formats()
        except KeyboardInterrupt:
            print("\n测试被用户中断")
    else:
        print("\n" + "=" * 50)
        print("❌ 基础功能测试失败！")
        sys.exit(1)
