"""
Production Deep Research System
生产级深度研究系统 - 使用真实AI服务和数据处理

核心功能：
1. 真实的深度调查研究 - 使用多个AI模型进行深度分析
2. 真实的多人对谈播客 - 使用豆包TTS生成多角色音频
3. 真实的语音视频制作 - 完整的视频制作管道
4. 真实的端到端工作流 - 无模拟，全部使用生产级组件
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import asyncio
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
import logging
import os
import sys
import requests
from typing import Dict, List, Optional, Any
from datetime import timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# 导入生产级组件
try:
    # 尝试导入核心组件
    from src.enhanced_workflow_pipeline import EnhancedWorkflowPipeline, WorkflowRequest, OutputFormat
    from src.universal_research_template import ResearchScenario
    from src.deep_research_agent import DeepResearchAgent
    from src.doubao_tts import DoubaoTTS
    from src.image_generator import ImageGenerator
    from src.video_assembler import VideoAssembler
    from src.video_config import VideoConfig
    from src.voice_config import VoiceManager
    from src.reviewer_personalities import PersonalityManager
    from src.pipeline import BookReviewPipeline

    PRODUCTION_MODE = True
    print("✅ 生产级组件加载成功")

except ImportError as e:
    print(f"❌ 生产级组件加载失败: {str(e)}")

    # 尝试直接导入（如果在src目录中运行）
    try:
        from enhanced_workflow_pipeline import EnhancedWorkflowPipeline, WorkflowRequest, OutputFormat
        from universal_research_template import ResearchScenario
        from deep_research_agent import DeepResearchAgent
        from doubao_tts import DoubaoTTS
        from image_generator import ImageGenerator
        from video_assembler import VideoAssembler
        from video_config import VideoConfig
        from voice_config import VoiceManager
        from reviewer_personalities import PersonalityManager
        from pipeline import BookReviewPipeline

        PRODUCTION_MODE = True
        print("✅ 生产级组件加载成功（直接导入）")

    except ImportError as e2:
        print(f"❌ 生产级组件加载完全失败: {str(e2)}")
        print("❌ 请确保所有依赖组件都已正确安装和配置")
        print("❌ 或者在正确的目录中运行此应用")
        sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/research_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Flask应用初始化
app = Flask(__name__)
app.secret_key = 'production_deep_research_system_2024'
CORS(app)

# 全局生产级组件
try:
    # 初始化核心组件
    workflow_pipeline = EnhancedWorkflowPipeline()
    research_agent = DeepResearchAgent()
    doubao_tts = DoubaoTTS()
    image_generator = ImageGenerator()
    video_assembler = VideoAssembler()
    book_review_pipeline = BookReviewPipeline()
    
    # 初始化配置管理器
    voice_manager = VoiceManager()
    personality_manager = PersonalityManager()
    
    logger.info("✅ 所有生产级组件初始化成功")
    
except Exception as e:
    logger.error(f"❌ 生产级组件初始化失败: {str(e)}")
    sys.exit(1)

# 任务状态存储
task_status = {}
task_results = {}

# 异步运行辅助函数
def run_async(coro):
    """在新的事件循环中运行异步函数"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(coro)
    finally:
        loop.close()

# ===== 核心路由 =====

@app.route('/')
def index():
    """主页 - 生产级深度研究系统"""
    return render_template('production_research_index.html')

@app.route('/results/<task_id>')
def results_page(task_id):
    """结果页面"""
    return render_template('production_research_results.html', task_id=task_id)

# ===== 生产级API端点 =====

@app.route('/api/research/scenarios')
def get_research_scenarios():
    """获取真实的研究场景列表"""
    try:
        # 从ResearchScenario枚举获取真实场景
        scenarios = []
        for scenario in ResearchScenario:
            scenario_info = {
                "id": scenario.value,
                "name": _get_scenario_name(scenario),
                "description": _get_scenario_description(scenario),
                "icon": _get_scenario_icon(scenario),
                "estimated_depth": _get_scenario_depth(scenario),
                "required_sources": _get_scenario_sources(scenario)
            }
            scenarios.append(scenario_info)
        
        return jsonify({
            "success": True,
            "scenarios": scenarios,
            "total_scenarios": len(scenarios)
        })
        
    except Exception as e:
        logger.error(f"获取研究场景失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/output-formats')
def get_output_formats():
    """获取真实的输出格式选项"""
    try:
        # 从OutputFormat枚举获取真实格式
        formats = []
        for output_format in OutputFormat:
            format_info = {
                "id": output_format.value,
                "name": _get_format_name(output_format),
                "description": _get_format_description(output_format),
                "icon": _get_format_icon(output_format),
                "estimated_time": _get_format_time(output_format),
                "output_files": _get_format_outputs(output_format),
                "ai_models_used": _get_format_models(output_format)
            }
            formats.append(format_info)
        
        return jsonify({
            "success": True,
            "formats": formats,
            "total_formats": len(formats)
        })
        
    except Exception as e:
        logger.error(f"获取输出格式失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/voices')
def get_available_voices():
    """获取可用的TTS语音"""
    try:
        voices = voice_manager.get_available_voices()
        
        # 测试豆包TTS连接
        tts_status = doubao_tts.test_connection()
        
        return jsonify({
            "success": True,
            "voices": voices,
            "tts_service_status": "connected" if tts_status else "disconnected",
            "total_voices": len(voices)
        })
        
    except Exception as e:
        logger.error(f"获取语音列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/personalities')
def get_reviewer_personalities():
    """获取评论者风格"""
    try:
        personalities = personality_manager.get_personality_list()
        
        return jsonify({
            "success": True,
            "personalities": personalities,
            "total_personalities": len(personalities)
        })
        
    except Exception as e:
        logger.error(f"获取评论者风格失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/start', methods=['POST'])
def start_production_research():
    """启动生产级深度研究工作流"""
    try:
        data = request.json
        
        # 验证必需参数
        required_fields = ['user_input', 'research_scenario', 'output_format', 'target_audience']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "success": False,
                    "error": f"缺少必需参数: {field}"
                }), 400
        
        # 验证枚举值
        try:
            research_scenario = ResearchScenario(data['research_scenario'])
            output_format = OutputFormat(data['output_format'])
        except ValueError as e:
            return jsonify({
                "success": False,
                "error": f"无效的参数值: {str(e)}"
            }), 400
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 创建视频配置（包含语音和风格设置）
        video_config = VideoConfig()
        if data.get('voice_config', {}).get('voice_id'):
            video_config.voice_id = data['voice_config']['voice_id']

        # 创建生产级工作流请求
        workflow_request = WorkflowRequest(
            user_input=data['user_input'],
            research_scenario=research_scenario,
            output_format=output_format,
            audience=data['target_audience'],
            video_config=video_config,
            additional_requirements={
                'personality_style': data.get('personality_style', 'academic'),
                'custom_requirements': data.get('additional_requirements', '')
            }
        )
        
        # 初始化任务状态
        task_status[task_id] = {
            "status": "initializing",
            "current_step": "初始化生产级研究任务",
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "user_input": data['user_input'],
            "research_scenario": research_scenario.value,
            "output_format": output_format.value,
            "target_audience": data['target_audience'],
            "steps_completed": [],
            "estimated_completion": _calculate_completion_time(output_format),
            "ai_models_used": [],
            "processing_details": []
        }
        
        # 异步执行生产级工作流
        def execute_production_workflow():
            try:
                logger.info(f"开始执行生产级工作流，任务ID: {task_id}")
                
                @run_async
                async def run_production_workflow():
                    return await workflow_pipeline.execute_workflow(workflow_request)
                
                # 执行真实的工作流
                result = run_production_workflow()
                
                # 处理结果
                if result and result.success:
                    task_status[task_id].update({
                        "status": "completed",
                        "current_step": "生产级研究完成",
                        "progress": 100,
                        "completed_at": datetime.now().isoformat(),
                        "result_files": result.output_files,
                        "summary": result.summary,
                        "quality_score": result.quality_score,
                        "processing_time": result.processing_time,
                        "tokens_used": result.tokens_used,
                        "cost_breakdown": result.cost_breakdown
                    })
                    task_results[task_id] = result
                    logger.info(f"生产级工作流完成，任务ID: {task_id}")
                else:
                    error_msg = result.error_message if result else "工作流执行失败"
                    task_status[task_id].update({
                        "status": "failed",
                        "current_step": "执行失败",
                        "error": error_msg,
                        "failed_at": datetime.now().isoformat()
                    })
                    logger.error(f"生产级工作流失败，任务ID: {task_id}, 错误: {error_msg}")
                    
            except Exception as e:
                logger.error(f"生产级工作流执行异常: {str(e)}")
                task_status[task_id].update({
                    "status": "failed",
                    "current_step": "系统异常",
                    "error": str(e),
                    "failed_at": datetime.now().isoformat()
                })
        
        # 启动异步执行
        thread = threading.Thread(target=execute_production_workflow)
        thread.start()
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "生产级深度研究任务已启动",
            "estimated_time": _get_format_time(output_format),
            "ai_models": _get_format_models(output_format),
            "expected_outputs": _get_format_outputs(output_format)
        })
        
    except Exception as e:
        logger.error(f"启动生产级研究失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/status/<task_id>')
def get_production_research_status(task_id):
    """获取生产级研究任务状态"""
    try:
        if task_id not in task_status:
            return jsonify({
                "success": False,
                "error": "任务不存在"
            }), 404

        status = task_status[task_id]

        # 如果任务正在运行，尝试获取实时进度
        if status.get('status') in ['initializing', 'processing']:
            # 这里可以添加从工作流管道获取实时进度的逻辑
            pass

        return jsonify({
            "success": True,
            "task_id": task_id,
            **status
        })

    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/result/<task_id>')
def get_production_research_result(task_id):
    """获取生产级研究结果"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在或任务未完成"
            }), 404

        result = task_results[task_id]
        status = task_status.get(task_id, {})

        return jsonify({
            "success": True,
            "task_id": task_id,
            "status": status.get("status"),
            "result": {
                "output_files": result.output_files,
                "summary": result.summary,
                "quality_score": result.quality_score,
                "processing_time": result.processing_time,
                "tokens_used": result.tokens_used,
                "cost_breakdown": result.cost_breakdown,
                "ai_models_used": result.ai_models_used,
                "research_depth": result.research_depth,
                "sources_analyzed": result.sources_analyzed
            },
            "generated_at": status.get("completed_at")
        })

    except Exception as e:
        logger.error(f"获取研究结果失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/download/<task_id>/<file_type>')
def download_production_result_file(task_id, file_type):
    """下载生产级结果文件"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在"
            }), 404

        result = task_results[task_id]
        output_files = result.output_files

        if file_type not in output_files:
            return jsonify({
                "success": False,
                "error": f"文件类型 {file_type} 不存在"
            }), 404

        file_path = output_files[file_type]

        if not Path(file_path).exists():
            return jsonify({
                "success": False,
                "error": "文件不存在"
            }), 404

        # 记录下载日志
        logger.info(f"下载文件: {file_path}, 任务ID: {task_id}, 文件类型: {file_type}")

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/system/health')
def system_health_check():
    """生产级系统健康检查"""
    try:
        health_status = {
            "system_status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "workflow_pipeline": _check_component_health(workflow_pipeline),
                "research_agent": _check_component_health(research_agent),
                "doubao_tts": _check_component_health(doubao_tts),
                "image_generator": _check_component_health(image_generator),
                "video_assembler": _check_component_health(video_assembler),
                "book_review_pipeline": _check_component_health(book_review_pipeline)
            },
            "services": {
                "doubao_tts_api": _test_service_connection(doubao_tts),
                "image_generation_api": _test_service_connection(image_generator)
            },
            "resources": {
                "active_tasks": len([t for t in task_status.values() if t.get('status') in ['initializing', 'processing']]),
                "completed_tasks": len([t for t in task_status.values() if t.get('status') == 'completed']),
                "failed_tasks": len([t for t in task_status.values() if t.get('status') == 'failed']),
                "output_directory_writable": os.access("output", os.W_OK) if os.path.exists("output") else False
            }
        }

        # 检查是否有任何组件不健康
        unhealthy_components = [
            name for name, status in health_status["components"].items()
            if not status
        ]

        if unhealthy_components or not all(health_status["services"].values()):
            health_status["system_status"] = "degraded"
            health_status["issues"] = unhealthy_components

        return jsonify({
            "success": True,
            "health": health_status
        })

    except Exception as e:
        logger.error(f"系统健康检查失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "system_status": "unhealthy"
        }), 500

# ===== 生产级辅助函数 =====

def _check_component_health(component) -> bool:
    """检查组件健康状态"""
    try:
        if component is None:
            return False
        if hasattr(component, 'health_check'):
            return component.health_check()
        elif hasattr(component, 'test_connection'):
            return component.test_connection()
        else:
            # 基本检查：组件是否存在且可调用
            return True
    except Exception as e:
        logger.debug(f"组件健康检查失败: {str(e)}")
        return False

def _test_service_connection(service) -> bool:
    """测试服务连接"""
    try:
        if service is None:
            return False
        if hasattr(service, 'test_connection'):
            return service.test_connection()
        elif hasattr(service, 'health_check'):
            return service.health_check()
        else:
            # 基本检查：服务是否存在
            return True
    except Exception as e:
        logger.debug(f"服务连接测试失败: {str(e)}")
        return False

def _get_scenario_name(scenario: ResearchScenario) -> str:
    """获取研究场景名称"""
    scenario_names = {
        ResearchScenario.BOOK_ANALYSIS: "图书深度分析",
        ResearchScenario.INDUSTRY_REPORT: "行业研究报告",
        ResearchScenario.RESEARCH_PROGRESS: "研究进展分析",
        ResearchScenario.NEWS_ANALYSIS: "新闻深度分析",
        ResearchScenario.MARKET_ANALYSIS: "市场分析报告",
        ResearchScenario.BOOK_RECOMMENDATION: "图书推荐分析",
        ResearchScenario.COMPETITIVE_INTELLIGENCE: "竞争情报分析",
        ResearchScenario.TECHNOLOGY_ASSESSMENT: "技术评估报告",
        ResearchScenario.INVESTMENT_ANALYSIS: "投资分析报告",
        ResearchScenario.TREND_ANALYSIS: "趋势分析报告"
    }
    return scenario_names.get(scenario, scenario.value)

def _get_scenario_description(scenario: ResearchScenario) -> str:
    """获取研究场景描述"""
    descriptions = {
        ResearchScenario.BOOK_ANALYSIS: "对指定书籍进行全面的内容分析、主题解读和价值评估，使用多个AI模型进行深度挖掘",
        ResearchScenario.INDUSTRY_REPORT: "深入分析特定行业的发展现状、趋势和机遇挑战，整合多方数据源",
        ResearchScenario.RESEARCH_PROGRESS: "跟踪和分析特定研究领域的最新进展，整合学术和产业动态",
        ResearchScenario.NEWS_ANALYSIS: "对重要新闻事件进行深度分析，提供多角度解读和影响评估",
        ResearchScenario.MARKET_ANALYSIS: "分析市场动态、竞争格局和投资机会，提供数据驱动的洞察",
        ResearchScenario.BOOK_RECOMMENDATION: "基于用户偏好和内容分析，提供个性化的图书推荐和评价",
        ResearchScenario.COMPETITIVE_INTELLIGENCE: "收集和分析竞争对手信息，提供战略决策支持",
        ResearchScenario.TECHNOLOGY_ASSESSMENT: "对新兴技术进行全面评估和前景分析，包含技术原理和应用场景",
        ResearchScenario.INVESTMENT_ANALYSIS: "分析投资标的的价值和风险，提供专业的投资建议",
        ResearchScenario.TREND_ANALYSIS: "识别和分析行业趋势，预测未来发展方向"
    }
    return descriptions.get(scenario, "专业级深度研究分析")

def _get_scenario_icon(scenario: ResearchScenario) -> str:
    """获取研究场景图标"""
    icons = {
        ResearchScenario.BOOK_ANALYSIS: "📚",
        ResearchScenario.INDUSTRY_REPORT: "🏭",
        ResearchScenario.RESEARCH_PROGRESS: "🔬",
        ResearchScenario.NEWS_ANALYSIS: "📰",
        ResearchScenario.MARKET_ANALYSIS: "📈",
        ResearchScenario.BOOK_RECOMMENDATION: "📖",
        ResearchScenario.COMPETITIVE_INTELLIGENCE: "🕵️",
        ResearchScenario.TECHNOLOGY_ASSESSMENT: "⚙️",
        ResearchScenario.INVESTMENT_ANALYSIS: "💰",
        ResearchScenario.TREND_ANALYSIS: "📊"
    }
    return icons.get(scenario, "🔍")

def _get_scenario_depth(scenario: ResearchScenario) -> str:
    """获取研究场景深度"""
    depths = {
        ResearchScenario.BOOK_ANALYSIS: "深度",
        ResearchScenario.INDUSTRY_REPORT: "全面",
        ResearchScenario.RESEARCH_PROGRESS: "专业",
        ResearchScenario.NEWS_ANALYSIS: "深度",
        ResearchScenario.MARKET_ANALYSIS: "数据驱动",
        ResearchScenario.BOOK_RECOMMENDATION: "个性化",
        ResearchScenario.COMPETITIVE_INTELLIGENCE: "战略",
        ResearchScenario.TECHNOLOGY_ASSESSMENT: "技术深度",
        ResearchScenario.INVESTMENT_ANALYSIS: "专业",
        ResearchScenario.TREND_ANALYSIS: "预测性"
    }
    return depths.get(scenario, "标准")

def _get_scenario_sources(scenario: ResearchScenario) -> List[str]:
    """获取研究场景所需数据源"""
    sources = {
        ResearchScenario.BOOK_ANALYSIS: ["书籍内容", "评论分析", "作者背景", "相关文献"],
        ResearchScenario.INDUSTRY_REPORT: ["行业数据", "市场报告", "专家观点", "政策文件"],
        ResearchScenario.RESEARCH_PROGRESS: ["学术论文", "研究报告", "专家访谈", "数据库"],
        ResearchScenario.NEWS_ANALYSIS: ["新闻报道", "媒体分析", "专家评论", "社交媒体"],
        ResearchScenario.MARKET_ANALYSIS: ["市场数据", "财务报告", "竞争分析", "趋势预测"],
        ResearchScenario.BOOK_RECOMMENDATION: ["图书数据", "用户评价", "内容分析", "推荐算法"],
        ResearchScenario.COMPETITIVE_INTELLIGENCE: ["竞争对手", "市场情报", "战略分析", "行业报告"],
        ResearchScenario.TECHNOLOGY_ASSESSMENT: ["技术文档", "专利信息", "应用案例", "专家评估"],
        ResearchScenario.INVESTMENT_ANALYSIS: ["财务数据", "市场分析", "风险评估", "投资报告"],
        ResearchScenario.TREND_ANALYSIS: ["趋势数据", "预测模型", "历史分析", "专家观点"]
    }
    return sources.get(scenario, ["多源数据"])

def _get_format_name(output_format: OutputFormat) -> str:
    """获取输出格式名称"""
    format_names = {
        OutputFormat.RESEARCH_REPORT: "深度研究报告",
        OutputFormat.PODCAST_SCRIPT: "播客对话脚本",
        OutputFormat.AUDIO_PODCAST: "多人对谈音频",
        OutputFormat.FULL_VIDEO: "完整视频内容"
    }
    return format_names.get(output_format, output_format.value)

def _get_format_description(output_format: OutputFormat) -> str:
    """获取输出格式描述"""
    descriptions = {
        OutputFormat.RESEARCH_REPORT: "生成详细的文字研究报告，包含多维度分析、数据支撑和专业洞察",
        OutputFormat.PODCAST_SCRIPT: "生成多人对谈的播客脚本，包含角色设定、对话流程和专业讨论",
        OutputFormat.AUDIO_PODCAST: "生成完整的多角色对话音频播客，使用豆包TTS多声音合成",
        OutputFormat.FULL_VIDEO: "生成包含语音、字幕、背景的完整视频，集成所有多媒体元素"
    }
    return descriptions.get(output_format, "专业内容输出")

def _get_format_icon(output_format: OutputFormat) -> str:
    """获取输出格式图标"""
    icons = {
        OutputFormat.RESEARCH_REPORT: "📄",
        OutputFormat.PODCAST_SCRIPT: "🎙️",
        OutputFormat.AUDIO_PODCAST: "🎧",
        OutputFormat.FULL_VIDEO: "🎬"
    }
    return icons.get(output_format, "📋")

def _get_format_time(output_format: OutputFormat) -> str:
    """获取输出格式预估时间"""
    times = {
        OutputFormat.RESEARCH_REPORT: "8-15分钟",
        OutputFormat.PODCAST_SCRIPT: "12-20分钟",
        OutputFormat.AUDIO_PODCAST: "20-35分钟",
        OutputFormat.FULL_VIDEO: "30-50分钟"
    }
    return times.get(output_format, "15-25分钟")

def _get_format_outputs(output_format: OutputFormat) -> List[str]:
    """获取输出格式生成的文件类型"""
    outputs = {
        OutputFormat.RESEARCH_REPORT: ["research_report.pdf", "research_report.docx", "summary.txt"],
        OutputFormat.PODCAST_SCRIPT: ["podcast_script.txt", "character_profiles.json", "timeline.json"],
        OutputFormat.AUDIO_PODCAST: ["podcast_audio.mp3", "transcript.txt", "chapters.json"],
        OutputFormat.FULL_VIDEO: ["final_video.mp4", "subtitles.srt", "audio_track.mp3", "thumbnail.jpg"]
    }
    return outputs.get(output_format, ["output.txt"])

def _get_format_models(output_format: OutputFormat) -> List[str]:
    """获取输出格式使用的AI模型"""
    models = {
        OutputFormat.RESEARCH_REPORT: ["GPT-4", "Claude-3", "豆包大模型", "搜索增强"],
        OutputFormat.PODCAST_SCRIPT: ["GPT-4", "Claude-3", "豆包大模型", "角色生成模型"],
        OutputFormat.AUDIO_PODCAST: ["豆包TTS", "语音合成", "音频处理", "背景音乐AI"],
        OutputFormat.FULL_VIDEO: ["豆包TTS", "图像生成", "视频合成", "字幕生成"]
    }
    return models.get(output_format, ["通用AI模型"])

def _calculate_completion_time(output_format: OutputFormat) -> str:
    """计算预估完成时间"""
    base_time = datetime.now()
    time_deltas = {
        OutputFormat.RESEARCH_REPORT: 15,  # 15分钟
        OutputFormat.PODCAST_SCRIPT: 20,   # 20分钟
        OutputFormat.AUDIO_PODCAST: 35,    # 35分钟
        OutputFormat.FULL_VIDEO: 50        # 50分钟
    }

    minutes = time_deltas.get(output_format, 25)
    completion_time = base_time + timedelta(minutes=minutes)
    return completion_time.isoformat()

if __name__ == '__main__':
    # 确保必要目录存在
    required_dirs = ['output', 'temp', 'logs', 'templates', 'static', 'assets/bgm', 'assets/fonts']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(parents=True, exist_ok=True)

    # 检查环境变量
    required_env_vars = ['DOUBAO_APP_ID', 'DOUBAO_ACCESS_TOKEN']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.warning(f"缺少环境变量: {', '.join(missing_vars)}")
        logger.warning("某些功能可能无法正常工作")

    # 系统启动信息
    print("🚀 启动生产级深度研究系统")
    print("=" * 60)
    print("🌐 Web界面: http://localhost:8081")
    print("🔧 系统健康检查: http://localhost:8081/api/system/health")
    print("📖 研究场景: http://localhost:8081/api/research/scenarios")
    print("🎙️ 可用语音: http://localhost:8081/api/research/voices")
    print("=" * 60)
    print("💡 按 Ctrl+C 停止服务")
    print()

    # 启动服务器
    try:
        app.run(
            host='0.0.0.0',
            port=8081,
            debug=False,  # 生产模式不使用debug
            threaded=True
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        sys.exit(1)
