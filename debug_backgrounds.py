#!/usr/bin/env python3
"""
Debug background generation issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_background_generation():
    """Debug the background generation process"""
    
    print("🔍 Debugging Background Generation Process")
    print("-" * 50)
    
    try:
        from src.dynamic_background_generator import DynamicBackgroundGenerator
        from src.context_analyzer import ContextualThemeAnalyzer
        
        # Test book metadata
        book_metadata = {
            "title": "《小王子》- 安托万·德·圣埃克苏佩里",
            "author": "安托万·德·圣埃克苏佩里"
        }
        
        # Create generator
        generator = DynamicBackgroundGenerator(book_metadata=book_metadata)
        
        # Create test alignment data
        alignment_data = [
            {"text": "小王子来自B-612小行星，那里有一朵玫瑰花", "start": 0, "end": 5000},
            {"text": "他遇到了狐狸，狐狸教会了他驯养的秘密", "start": 5000, "end": 10000},
            {"text": "在沙漠中，小王子和飞行员成为了朋友", "start": 10000, "end": 15000},
        ]
        
        print("\n1. Testing background sequence planning...")
        segments = generator.plan_background_sequence(alignment_data, 20.0)
        
        print(f"\n📊 Generated {len(segments)} segments:")
        for i, segment in enumerate(segments):
            print(f"\nSegment {i+1}:")
            print(f"  Time: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s")
            print(f"  Duration: {segment['duration']:.1f}s")
            print(f"  Prompt: {segment['image_prompt'][:100]}...")
            print(f"  Characters: {[c['name'] for c in segment.get('characters', [])]}")
            print(f"  Locations: {[l['name'] for l in segment.get('locations', [])]}")
            print(f"  Importance: {segment.get('importance', 0):.2f}")
        
        if not segments:
            print("❌ No segments generated!")
            return False
        
        print("\n2. Testing image generation...")
        temp_dir = "./temp/debug_backgrounds"
        os.makedirs(temp_dir, exist_ok=True)
        
        # Test generating one image
        test_segment = segments[0]
        image_path = os.path.join(temp_dir, "test_background.jpg")
        
        print(f"\nTesting image generation...")
        print(f"Prompt: {test_segment['image_prompt']}")
        print(f"Output: {image_path}")
        
        # Try to generate the image
        try:
            success = generator._generate_background_image(test_segment['image_prompt'], image_path)
            
            if success and os.path.exists(image_path):
                print(f"✅ Image generated successfully: {image_path}")
                file_size = os.path.getsize(image_path)
                print(f"   File size: {file_size} bytes")
            else:
                print(f"❌ Image generation failed")
                
                # Try fallback
                print("🔄 Testing fallback image creation...")
                fallback_path = generator._create_fallback_image(image_path)
                if fallback_path and os.path.exists(fallback_path):
                    print(f"✅ Fallback image created: {fallback_path}")
                else:
                    print(f"❌ Fallback image creation also failed")
                    
        except Exception as e:
            print(f"❌ Image generation error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n3. Testing background sequence creation...")
        
        # Generate all images for the segments
        generated_segments = generator.generate_background_images(segments, temp_dir)
        
        success_count = sum(1 for seg in generated_segments if seg.get('generation_success', False))
        total_count = len(generated_segments)
        
        print(f"\n📈 Image generation results:")
        print(f"   Successful: {success_count}/{total_count}")
        
        for i, seg in enumerate(generated_segments):
            status = "✅" if seg.get('generation_success', False) else "❌"
            image_path = seg.get('image_path', 'N/A')
            print(f"   {status} Segment {i+1}: {image_path}")
        
        print("\n4. Testing background transitions...")
        
        try:
            from src.background_transitions import BackgroundTransitions
            
            transitions = BackgroundTransitions()
            background_clips = transitions.create_background_sequence(
                generated_segments, 
                (1080, 1920)  # Video dimensions
            )
            
            print(f"✅ Created {len(background_clips)} background clips")
            
            for i, clip in enumerate(background_clips):
                start_time = getattr(clip, 'start', 0)
                duration = getattr(clip, 'duration', 0)
                print(f"   Clip {i+1}: {start_time:.1f}s - {start_time + duration:.1f}s (duration: {duration:.1f}s)")
            
            return len(background_clips) > 0
            
        except Exception as e:
            print(f"❌ Background transition error: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_image_generator():
    """Check if image generator is working"""
    
    print("\n🖼️ Testing Image Generator")
    print("-" * 30)
    
    try:
        from src.image_generator import ImageGenerator
        
        generator = ImageGenerator()
        
        # Test simple image generation
        test_prompt = "beautiful landscape with mountains and trees"
        output_path = "./temp/test_image.jpg"
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        print(f"Testing prompt: {test_prompt}")
        print(f"Output path: {output_path}")
        
        success = generator.generate_cover_image(test_prompt, output_path)
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ Image generator working! File size: {file_size} bytes")
            return True
        else:
            print(f"❌ Image generator failed")
            return False
            
    except Exception as e:
        print(f"❌ Image generator error: {e}")
        return False

def main():
    print("=== Background Generation Debug ===")
    
    # Check basic image generation first
    img_gen_works = check_image_generator()
    
    if img_gen_works:
        print("\n✅ Image generator is working, testing background system...")
        bg_works = debug_background_generation()
        
        if bg_works:
            print("\n🎉 Background generation system is working!")
            print("The issue might be elsewhere in the pipeline.")
        else:
            print("\n❌ Background generation system has issues.")
    else:
        print("\n❌ Basic image generation is not working.")
        print("This is likely the root cause of no background switching.")
    
    print("\n💡 Recommendations:")
    print("1. Check API keys for image generation service")
    print("2. Verify network connectivity") 
    print("3. Check image generation service status")
    print("4. Look at server console for error messages")

if __name__ == "__main__":
    main()