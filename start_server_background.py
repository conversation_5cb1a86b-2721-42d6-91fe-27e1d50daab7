#!/usr/bin/env python3
"""
在后台启动研究系统API服务器
"""

import os
import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 创建必要目录
for dir_name in ['output', 'temp', 'logs']:
    (project_root / dir_name).mkdir(exist_ok=True)

print("🚀 启动深度研究系统API服务器...")
print("🌐 服务地址: http://localhost:5000")
print("📖 API文档和示例: http://localhost:5000/api/research/examples")
print("💡 按 Ctrl+C 停止服务\n")

# 启动服务器
try:
    from research_api import app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True,
        use_reloader=False
    )
except KeyboardInterrupt:
    print("\n👋 服务器已停止")
except Exception as e:
    print(f"\n❌ 服务器启动失败: {str(e)}")
    sys.exit(1)