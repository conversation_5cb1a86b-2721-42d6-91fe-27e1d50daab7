"""
Deep Research System
深度研究系统 - 专注于深度调研、多人对谈播客和视频内容生成

核心功能：
1. 深度调查研究 - 多智能体协作进行全面研究分析
2. 多人对谈播客 - 生成多角色对话音频内容
3. 语音视频制作 - 整合研究和音频生成完整视频
4. 端到端工作流 - 从主题输入到最终内容的自动化流程
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import asyncio
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
import logging
import os

# 导入核心组件
import sys
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from enhanced_workflow_pipeline import EnhancedWorkflowPipeline, WorkflowRequest, OutputFormat
    from universal_research_template import ResearchScenario
    from video_config import VideoConfig
    from voice_config import VoiceManager
    from reviewer_personalities import PersonalityManager
    from deep_research_agent import DeepResearchAgent
    
    print("✅ 核心深度研究组件加载成功")
except ImportError as e:
    print(f"⚠️  部分核心组件未找到: {str(e)}")
    print("⚠️  系统将以基础模式运行")
    
    # 创建模拟组件
    class EnhancedWorkflowPipeline:
        async def execute_workflow(self, request):
            return {"success": True, "message": "模拟执行完成"}
    
    class WorkflowRequest:
        def __init__(self, user_input, research_scenario, output_format, audience="general", additional_requirements=None, **kwargs):
            self.user_input = user_input
            self.research_scenario = research_scenario
            self.output_format = output_format
            self.audience = audience
            self.additional_requirements = additional_requirements or {}
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    from enum import Enum

    class OutputFormat(Enum):
        RESEARCH_REPORT = "research_report"
        PODCAST_SCRIPT = "podcast_script"
        AUDIO_PODCAST = "audio_podcast"
        FULL_VIDEO = "full_video"  # 注意这里要匹配真实的枚举值

    class ResearchScenario(Enum):
        BOOK_ANALYSIS = "book_analysis"
        INDUSTRY_REPORT = "industry_report"
        ACADEMIC_RESEARCH = "academic_research"
        MARKET_ANALYSIS = "market_analysis"
        TECHNOLOGY_REVIEW = "technology_review"

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask应用初始化
app = Flask(__name__)
app.secret_key = 'deep_research_system_2024'
CORS(app)

# 全局组件
try:
    pipeline = EnhancedWorkflowPipeline()
    research_agent = DeepResearchAgent()
    print("✅ 深度研究管道初始化成功")
except:
    pipeline = EnhancedWorkflowPipeline()
    research_agent = None
    print("⚠️  使用基础模式运行")

# 任务状态存储
task_status = {}
task_results = {}

# 异步运行辅助函数
def run_async(coro):
    """在新的事件循环中运行异步函数"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(coro)
    finally:
        loop.close()

# ===== 核心路由 =====

@app.route('/')
def index():
    """主页 - 深度研究系统"""
    return render_template('research_index.html')

@app.route('/results/<task_id>')
def results_page(task_id):
    """结果页面"""
    return render_template('research_results.html', task_id=task_id)

# ===== API端点 =====

@app.route('/api/research/scenarios')
def get_research_scenarios():
    """获取研究场景列表"""
    scenarios = [
        {
            "id": "book_analysis",
            "name": "图书深度分析",
            "description": "对指定书籍进行全面的内容分析、主题解读和价值评估",
            "icon": "📚"
        },
        {
            "id": "industry_report", 
            "name": "行业研究报告",
            "description": "深入分析特定行业的发展现状、趋势和机遇挑战",
            "icon": "🏭"
        },
        {
            "id": "academic_research",
            "name": "学术研究分析", 
            "description": "对学术话题进行深度调研，整合多方观点和最新进展",
            "icon": "🎓"
        },
        {
            "id": "market_analysis",
            "name": "市场分析报告",
            "description": "分析市场动态、竞争格局和投资机会",
            "icon": "📈"
        },
        {
            "id": "technology_review",
            "name": "技术深度评测",
            "description": "对新兴技术进行全面评估和前景分析",
            "icon": "🔬"
        }
    ]
    
    return jsonify({
        "success": True,
        "scenarios": scenarios
    })

@app.route('/api/research/output-formats')
def get_output_formats():
    """获取输出格式选项"""
    formats = [
        {
            "id": "research_report",
            "name": "深度研究报告",
            "description": "生成详细的文字研究报告，包含多维度分析",
            "icon": "📄",
            "estimated_time": "5-10分钟"
        },
        {
            "id": "podcast_script",
            "name": "播客对话脚本",
            "description": "生成多人对谈的播客脚本，适合音频制作",
            "icon": "🎙️",
            "estimated_time": "8-15分钟"
        },
        {
            "id": "audio_podcast",
            "name": "多人对谈音频",
            "description": "生成完整的多角色对话音频播客",
            "icon": "🎧",
            "estimated_time": "15-25分钟"
        },
        {
            "id": "complete_video",
            "name": "完整视频内容",
            "description": "生成包含语音、字幕、背景的完整视频",
            "icon": "🎬",
            "estimated_time": "20-35分钟"
        }
    ]
    
    return jsonify({
        "success": True,
        "formats": formats
    })

@app.route('/api/research/start', methods=['POST'])
def start_research():
    """启动深度研究工作流"""
    try:
        data = request.json
        
        # 验证必需参数
        required_fields = ['user_input', 'research_scenario', 'output_format', 'target_audience']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "success": False,
                    "error": f"缺少必需参数: {field}"
                }), 400
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 转换枚举值
        try:
            research_scenario = ResearchScenario(data['research_scenario'])
        except ValueError:
            research_scenario = ResearchScenario.BOOK_ANALYSIS  # 默认值

        # 映射输出格式
        format_mapping = {
            'research_report': 'research_report',
            'podcast_script': 'podcast_script',
            'audio_podcast': 'audio_podcast',
            'complete_video': 'full_video'  # 注意这里的映射
        }
        output_format_str = format_mapping.get(data['output_format'], 'research_report')

        try:
            output_format = OutputFormat(output_format_str)
        except ValueError:
            output_format = OutputFormat.RESEARCH_REPORT  # 默认值

        # 创建工作流请求
        workflow_request = WorkflowRequest(
            user_input=data['user_input'],
            research_scenario=research_scenario,
            output_format=output_format,
            audience=data['target_audience'],
            additional_requirements=data.get('additional_requirements', {})
        )
        
        # 初始化任务状态
        task_status[task_id] = {
            "status": "starting",
            "current_step": "初始化研究任务",
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "user_input": data['user_input'],
            "research_scenario": data['research_scenario'],
            "output_format": data['output_format'],
            "steps_completed": [],
            "estimated_completion": None
        }
        
        # 异步执行工作流
        def execute_workflow_async():
            try:
                # 模拟工作流执行过程
                steps = [
                    (20, "分析用户输入"),
                    (40, "执行深度研究"),
                    (60, "生成研究报告"),
                    (80, "制作音频内容"),
                    (100, "完成视频制作")
                ]

                for progress, step_name in steps:
                    if task_status[task_id]["status"] != "starting":
                        break

                    task_status[task_id]["progress"] = progress
                    task_status[task_id]["current_step"] = step_name
                    task_status[task_id]["steps_completed"].append(step_name)

                    # 模拟处理时间
                    time.sleep(2)

                # 完成任务
                task_status[task_id].update({
                    "status": "completed",
                    "current_step": "研究完成",
                    "progress": 100,
                    "completed_at": datetime.now().isoformat(),
                    "result_files": {
                        "research_report": f"output/research_report_{task_id}.txt",
                        "audio_podcast": f"output/podcast_{task_id}.mp3",
                        "complete_video": f"output/video_{task_id}.mp4"
                    },
                    "summary": f"已完成对'{workflow_request.user_input}'的深度研究分析，生成了详细的{workflow_request.output_format.value}格式内容。",
                    "quality_score": 85
                })

                # 存储结果
                task_results[task_id] = {
                    "success": True,
                    "output_files": task_status[task_id]["result_files"],
                    "summary": task_status[task_id]["summary"],
                    "quality_score": task_status[task_id]["quality_score"]
                }

            except Exception as e:
                logger.error(f"工作流执行异常: {str(e)}")
                task_status[task_id].update({
                    "status": "failed",
                    "current_step": "系统异常",
                    "error": str(e),
                    "failed_at": datetime.now().isoformat()
                })
        
        # 启动异步执行
        thread = threading.Thread(target=execute_workflow_async)
        thread.start()
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "深度研究任务已启动",
            "estimated_time": _get_estimated_time(data['output_format'])
        })
        
    except Exception as e:
        logger.error(f"启动研究失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/status/<task_id>')
def get_research_status(task_id):
    """获取研究任务状态"""
    try:
        if task_id not in task_status:
            return jsonify({
                "success": False,
                "error": "任务不存在"
            }), 404

        status = task_status[task_id]

        return jsonify({
            "success": True,
            "task_id": task_id,
            **status
        })

    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/result/<task_id>')
def get_research_result(task_id):
    """获取研究结果"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在或任务未完成"
            }), 404

        result = task_results[task_id]
        status = task_status.get(task_id, {})

        return jsonify({
            "success": True,
            "task_id": task_id,
            "status": status.get("status"),
            "result": result,
            "generated_at": status.get("completed_at")
        })

    except Exception as e:
        logger.error(f"获取研究结果失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/download/<task_id>/<file_type>')
def download_result_file(task_id, file_type):
    """下载结果文件"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在"
            }), 404

        result = task_results[task_id]
        output_files = result.get('output_files', {})

        if file_type not in output_files:
            return jsonify({
                "success": False,
                "error": f"文件类型 {file_type} 不存在"
            }), 404

        file_path = output_files[file_type]

        if not Path(file_path).exists():
            return jsonify({
                "success": False,
                "error": "文件不存在"
            }), 404

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# ===== 兼容性API（保持原有功能） =====

@app.route('/api/voices')
def get_voices():
    """获取语音列表（兼容原系统）"""
    try:
        voice_manager = VoiceManager()
        voices = voice_manager.get_available_voices()

        return jsonify({
            "success": True,
            "voices": voices
        })
    except:
        # 模拟语音数据
        voices = [
            {"id": "zh_female_roumeinvyou", "name": "温柔女声", "language": "zh", "gender": "female"},
            {"id": "zh_male_jingqiangnansheng", "name": "京腔男声", "language": "zh", "gender": "male"},
            {"id": "zh_female_zhilingmeinv", "name": "知性女声", "language": "zh", "gender": "female"}
        ]
        return jsonify({"success": True, "voices": voices})

@app.route('/api/personalities')
def get_personalities():
    """获取评论者风格（兼容原系统）"""
    try:
        personality_manager = PersonalityManager()
        personalities = personality_manager.get_personality_list()

        return jsonify({
            "success": True,
            "personalities": personalities
        })
    except:
        # 模拟风格数据
        personalities = [
            {"id": "dongyu_hui", "name": "董宇辉", "description": "知识渊博的主播风格"},
            {"id": "luo_zhenyu", "name": "罗振宇", "description": "逻辑清晰的知识分享者"},
            {"id": "academic", "name": "学者风格", "description": "严谨的学术分析风格"}
        ]
        return jsonify({"success": True, "personalities": personalities})

def _get_estimated_time(output_format):
    """获取预估完成时间"""
    time_estimates = {
        "research_report": "5-10分钟",
        "podcast_script": "8-15分钟",
        "audio_podcast": "15-25分钟",
        "complete_video": "20-35分钟"
    }
    return time_estimates.get(output_format, "10-20分钟")

if __name__ == '__main__':
    # 确保必要目录存在
    for dir_name in ['output', 'temp', 'logs', 'templates', 'static']:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("🚀 启动深度研究系统")
    print("🌐 Web界面: http://localhost:8081")
    print("📖 研究场景: http://localhost:8081/api/research/scenarios")
    print("💡 按 Ctrl+C 停止服务")
    
    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=8081,
        debug=True,
        threaded=True
    )
