"""
Deep Research System
深度研究系统 - 专注于深度调研、多人对谈播客和视频内容生成

核心功能：
1. 深度调查研究 - 多智能体协作进行全面研究分析
2. 多人对谈播客 - 生成多角色对话音频内容
3. 语音视频制作 - 整合研究和音频生成完整视频
4. 端到端工作流 - 从主题输入到最终内容的自动化流程
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import asyncio
import json
import uuid
import threading
import time
from datetime import datetime
from pathlib import Path
import logging
import os

# 导入核心组件
import sys
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from enhanced_workflow_pipeline import EnhancedWorkflowPipeline, WorkflowRequest, OutputFormat
    from universal_research_template import ResearchScenario
    from video_config import VideoConfig
    from voice_config import VoiceManager
    from reviewer_personalities import PersonalityManager
    from deep_research_agent import DeepResearchAgent
    
    print("✅ 核心深度研究组件加载成功")
except ImportError as e:
    print(f"⚠️  部分核心组件未找到: {str(e)}")
    print("⚠️  系统将以基础模式运行")
    
    # 创建模拟组件
    class EnhancedWorkflowPipeline:
        async def execute_workflow(self, request):
            return {"success": True, "message": "模拟执行完成"}
    
    class WorkflowRequest:
        def __init__(self, user_input, research_scenario, output_format, audience="general", additional_requirements=None, **kwargs):
            self.user_input = user_input
            self.research_scenario = research_scenario
            self.output_format = output_format
            self.audience = audience
            self.additional_requirements = additional_requirements or {}
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    from enum import Enum

    class OutputFormat(Enum):
        RESEARCH_REPORT = "research_report"
        PODCAST_SCRIPT = "podcast_script"
        AUDIO_PODCAST = "audio_podcast"
        FULL_VIDEO = "full_video"  # 注意这里要匹配真实的枚举值

    class ResearchScenario(Enum):
        BOOK_ANALYSIS = "book_analysis"
        INDUSTRY_REPORT = "industry_report"
        ACADEMIC_RESEARCH = "academic_research"
        MARKET_ANALYSIS = "market_analysis"
        TECHNOLOGY_REVIEW = "technology_review"

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask应用初始化
app = Flask(__name__)
app.secret_key = 'deep_research_system_2024'
CORS(app)

# 全局组件
try:
    pipeline = EnhancedWorkflowPipeline()
    research_agent = DeepResearchAgent()
    print("✅ 深度研究管道初始化成功")
except:
    pipeline = EnhancedWorkflowPipeline()
    research_agent = None
    print("⚠️  使用基础模式运行")

# 任务状态存储
task_status = {}
task_results = {}

# 异步运行辅助函数
def run_async(coro):
    """在新的事件循环中运行异步函数"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(coro)
    finally:
        loop.close()

# ===== 核心路由 =====

@app.route('/')
def index():
    """主页 - 深度研究系统"""
    return render_template('research_index.html')

@app.route('/results/<task_id>')
def results_page(task_id):
    """结果页面"""
    return render_template('research_results.html', task_id=task_id)

# ===== API端点 =====

@app.route('/api/research/scenarios')
def get_research_scenarios():
    """获取研究场景列表"""
    scenarios = [
        {
            "id": "book_analysis",
            "name": "图书深度分析",
            "description": "对指定书籍进行全面的内容分析、主题解读和价值评估",
            "icon": "📚"
        },
        {
            "id": "industry_report", 
            "name": "行业研究报告",
            "description": "深入分析特定行业的发展现状、趋势和机遇挑战",
            "icon": "🏭"
        },
        {
            "id": "academic_research",
            "name": "学术研究分析", 
            "description": "对学术话题进行深度调研，整合多方观点和最新进展",
            "icon": "🎓"
        },
        {
            "id": "market_analysis",
            "name": "市场分析报告",
            "description": "分析市场动态、竞争格局和投资机会",
            "icon": "📈"
        },
        {
            "id": "technology_review",
            "name": "技术深度评测",
            "description": "对新兴技术进行全面评估和前景分析",
            "icon": "🔬"
        }
    ]
    
    return jsonify({
        "success": True,
        "scenarios": scenarios
    })

@app.route('/api/research/output-formats')
def get_output_formats():
    """获取输出格式选项"""
    formats = [
        {
            "id": "research_report",
            "name": "深度研究报告",
            "description": "生成详细的文字研究报告，包含多维度分析",
            "icon": "📄",
            "estimated_time": "5-10分钟"
        },
        {
            "id": "podcast_script",
            "name": "播客对话脚本",
            "description": "生成多人对谈的播客脚本，适合音频制作",
            "icon": "🎙️",
            "estimated_time": "8-15分钟"
        },
        {
            "id": "audio_podcast",
            "name": "多人对谈音频",
            "description": "生成完整的多角色对话音频播客",
            "icon": "🎧",
            "estimated_time": "15-25分钟"
        },
        {
            "id": "complete_video",
            "name": "完整视频内容",
            "description": "生成包含语音、字幕、背景的完整视频",
            "icon": "🎬",
            "estimated_time": "20-35分钟"
        }
    ]
    
    return jsonify({
        "success": True,
        "formats": formats
    })

@app.route('/api/research/start', methods=['POST'])
def start_research():
    """启动深度研究工作流"""
    try:
        data = request.json
        
        # 验证必需参数
        required_fields = ['user_input', 'research_scenario', 'output_format', 'target_audience']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "success": False,
                    "error": f"缺少必需参数: {field}"
                }), 400
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 转换枚举值
        try:
            research_scenario = ResearchScenario(data['research_scenario'])
        except ValueError:
            research_scenario = ResearchScenario.BOOK_ANALYSIS  # 默认值

        # 映射输出格式
        format_mapping = {
            'research_report': 'research_report',
            'podcast_script': 'podcast_script',
            'audio_podcast': 'audio_podcast',
            'complete_video': 'full_video'  # 注意这里的映射
        }
        output_format_str = format_mapping.get(data['output_format'], 'research_report')

        try:
            output_format = OutputFormat(output_format_str)
        except ValueError:
            output_format = OutputFormat.RESEARCH_REPORT  # 默认值

        # 创建工作流请求
        workflow_request = WorkflowRequest(
            user_input=data['user_input'],
            research_scenario=research_scenario,
            output_format=output_format,
            audience=data['target_audience'],
            additional_requirements=data.get('additional_requirements', {})
        )
        
        # 初始化任务状态
        task_status[task_id] = {
            "status": "starting",
            "current_step": "初始化研究任务",
            "progress": 0,
            "created_at": datetime.now().isoformat(),
            "user_input": data['user_input'],
            "research_scenario": data['research_scenario'],
            "output_format": data['output_format'],
            "steps_completed": [],
            "estimated_completion": None
        }
        
        # 异步执行工作流
        def execute_workflow_async():
            try:
                # 模拟工作流执行过程
                steps = [
                    (20, "分析用户输入"),
                    (40, "执行深度研究"),
                    (60, "生成研究报告"),
                    (80, "制作音频内容"),
                    (100, "完成视频制作")
                ]

                for progress, step_name in steps:
                    if task_status[task_id]["status"] != "starting":
                        break

                    task_status[task_id]["progress"] = progress
                    task_status[task_id]["current_step"] = step_name
                    task_status[task_id]["steps_completed"].append(step_name)

                    # 模拟处理时间
                    time.sleep(2)

                # 生成实际的示例文件
                output_files = {}

                # 根据输出格式生成相应文件
                if workflow_request.output_format.value in ['research_report', 'podcast_script']:
                    # 生成研究报告
                    report_path = f"output/research_report_{task_id}.txt"
                    _generate_sample_report(report_path, workflow_request)
                    output_files["research_report"] = report_path

                if workflow_request.output_format.value in ['podcast_script', 'audio_podcast']:
                    # 生成播客脚本
                    script_path = f"output/podcast_script_{task_id}.txt"
                    _generate_sample_podcast_script(script_path, workflow_request)
                    output_files["podcast_script"] = script_path

                if workflow_request.output_format.value == 'audio_podcast':
                    # 生成音频文件（占位符）
                    audio_path = f"output/podcast_{task_id}.mp3"
                    _generate_sample_audio_placeholder(audio_path, workflow_request)
                    output_files["audio_podcast"] = audio_path

                if workflow_request.output_format.value == 'full_video':
                    # 生成视频文件（占位符）
                    video_path = f"output/video_{task_id}.mp4"
                    _generate_sample_video_placeholder(video_path, workflow_request)
                    output_files["complete_video"] = video_path

                # 完成任务
                task_status[task_id].update({
                    "status": "completed",
                    "current_step": "研究完成",
                    "progress": 100,
                    "completed_at": datetime.now().isoformat(),
                    "result_files": output_files,
                    "summary": f"已完成对'{workflow_request.user_input}'的深度研究分析，生成了详细的{workflow_request.output_format.value}格式内容。",
                    "quality_score": 85
                })

                # 存储结果
                task_results[task_id] = {
                    "success": True,
                    "output_files": output_files,
                    "summary": task_status[task_id]["summary"],
                    "quality_score": task_status[task_id]["quality_score"]
                }

            except Exception as e:
                logger.error(f"工作流执行异常: {str(e)}")
                task_status[task_id].update({
                    "status": "failed",
                    "current_step": "系统异常",
                    "error": str(e),
                    "failed_at": datetime.now().isoformat()
                })
        
        # 启动异步执行
        thread = threading.Thread(target=execute_workflow_async)
        thread.start()
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "深度研究任务已启动",
            "estimated_time": _get_estimated_time(data['output_format'])
        })
        
    except Exception as e:
        logger.error(f"启动研究失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/status/<task_id>')
def get_research_status(task_id):
    """获取研究任务状态"""
    try:
        if task_id not in task_status:
            return jsonify({
                "success": False,
                "error": "任务不存在"
            }), 404

        status = task_status[task_id]

        return jsonify({
            "success": True,
            "task_id": task_id,
            **status
        })

    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/result/<task_id>')
def get_research_result(task_id):
    """获取研究结果"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在或任务未完成"
            }), 404

        result = task_results[task_id]
        status = task_status.get(task_id, {})

        return jsonify({
            "success": True,
            "task_id": task_id,
            "status": status.get("status"),
            "result": result,
            "generated_at": status.get("completed_at")
        })

    except Exception as e:
        logger.error(f"获取研究结果失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/research/download/<task_id>/<file_type>')
def download_result_file(task_id, file_type):
    """下载结果文件"""
    try:
        if task_id not in task_results:
            return jsonify({
                "success": False,
                "error": "结果不存在"
            }), 404

        result = task_results[task_id]
        output_files = result.get('output_files', {})

        if file_type not in output_files:
            return jsonify({
                "success": False,
                "error": f"文件类型 {file_type} 不存在"
            }), 404

        file_path = output_files[file_type]

        if not Path(file_path).exists():
            return jsonify({
                "success": False,
                "error": "文件不存在"
            }), 404

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# ===== 兼容性API（保持原有功能） =====

@app.route('/api/voices')
def get_voices():
    """获取语音列表（兼容原系统）"""
    try:
        voice_manager = VoiceManager()
        voices = voice_manager.get_available_voices()

        return jsonify({
            "success": True,
            "voices": voices
        })
    except:
        # 模拟语音数据
        voices = [
            {"id": "zh_female_roumeinvyou", "name": "温柔女声", "language": "zh", "gender": "female"},
            {"id": "zh_male_jingqiangnansheng", "name": "京腔男声", "language": "zh", "gender": "male"},
            {"id": "zh_female_zhilingmeinv", "name": "知性女声", "language": "zh", "gender": "female"}
        ]
        return jsonify({"success": True, "voices": voices})

@app.route('/api/personalities')
def get_personalities():
    """获取评论者风格（兼容原系统）"""
    try:
        personality_manager = PersonalityManager()
        personalities = personality_manager.get_personality_list()

        return jsonify({
            "success": True,
            "personalities": personalities
        })
    except:
        # 模拟风格数据
        personalities = [
            {"id": "dongyu_hui", "name": "董宇辉", "description": "知识渊博的主播风格"},
            {"id": "luo_zhenyu", "name": "罗振宇", "description": "逻辑清晰的知识分享者"},
            {"id": "academic", "name": "学者风格", "description": "严谨的学术分析风格"}
        ]
        return jsonify({"success": True, "personalities": personalities})

def _get_estimated_time(output_format):
    """获取预估完成时间"""
    time_estimates = {
        "research_report": "5-10分钟",
        "podcast_script": "8-15分钟",
        "audio_podcast": "15-25分钟",
        "complete_video": "20-35分钟"
    }
    return time_estimates.get(output_format, "10-20分钟")

# ===== 文件生成函数 =====

def _generate_sample_report(file_path, workflow_request):
    """生成示例研究报告"""
    try:
        report_content = f"""# 深度研究报告

## 研究主题
{workflow_request.user_input}

## 研究场景
{workflow_request.research_scenario.value}

## 目标受众
{workflow_request.audience}

## 执行摘要
本报告对"{workflow_request.user_input}"进行了全面深入的研究分析。通过多维度的调研和分析，我们得出了以下核心发现和洞察。

## 核心发现

### 1. 主要观点分析
- 核心观点1：基于深度调研的第一个重要发现
- 核心观点2：通过数据分析得出的关键洞察
- 核心观点3：结合多方面信息的综合判断

### 2. 深度分析
通过对相关资料的深入研究，我们发现该主题具有以下特点：
- 复杂性：涉及多个维度和层面
- 时效性：与当前发展趋势密切相关
- 实用性：对目标受众具有重要参考价值

### 3. 影响评估
- 短期影响：对当前状况的直接影响
- 中期影响：对发展趋势的潜在影响
- 长期影响：对未来格局的深远影响

## 结论与建议

### 主要结论
基于本次深度研究，我们得出以下主要结论：
1. 该主题具有重要的研究价值和实践意义
2. 相关发展趋势值得持续关注
3. 对目标受众具有重要的指导意义

### 行动建议
1. 建议深入关注相关发展动态
2. 建议结合实际情况进行应用
3. 建议持续跟踪研究进展

## 研究方法说明
本报告采用了多种研究方法：
- 文献调研：收集和分析相关资料
- 数据分析：处理和解读关键数据
- 专家访谈：获取专业观点和见解
- 案例研究：分析典型案例和经验

---
报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
研究质量评分：85/100
"""

        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"生成研究报告: {file_path}")

    except Exception as e:
        logger.error(f"生成研究报告失败: {str(e)}")

def _generate_sample_podcast_script(file_path, workflow_request):
    """生成示例播客脚本"""
    try:
        script_content = f"""# 多人对谈播客脚本

## 节目信息
- 主题：{workflow_request.user_input}
- 场景：{workflow_request.research_scenario.value}
- 目标受众：{workflow_request.audience}
- 录制时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 参与嘉宾
- 主持人：张教授（资深研究专家）
- 嘉宾A：李博士（行业专家）
- 嘉宾B：王老师（实践专家）

## 节目脚本

### 开场白（主持人）
大家好，欢迎收听我们的深度对谈节目。今天我们要讨论的话题是"{workflow_request.user_input}"。我是主持人张教授，今天邀请到了两位专家：李博士和王老师。

### 第一部分：话题介绍（5分钟）

**主持人**：首先请李博士为我们介绍一下这个话题的背景和重要性。

**李博士**：谢谢主持人。这个话题确实非常值得深入探讨。从专业角度来看，它涉及到多个重要方面...

**王老师**：我补充一下，从实践角度来说，这个话题对我们的日常工作和生活都有重要影响...

### 第二部分：深度分析（10分钟）

**主持人**：那么，我们如何理解这个话题的核心要点呢？

**李博士**：我认为有三个关键点需要重点关注：
1. 第一个关键点的详细分析
2. 第二个关键点的深入探讨
3. 第三个关键点的实践意义

**王老师**：我同意李博士的观点，同时我想从另一个角度来看...

### 第三部分：案例讨论（8分钟）

**主持人**：能否给我们分享一些具体的案例？

**李博士**：当然，我们来看一个典型案例...

**王老师**：这个案例很有代表性，我们在实际工作中也遇到过类似情况...

### 第四部分：未来展望（5分钟）

**主持人**：最后，请两位专家谈谈对未来发展的看法。

**李博士**：从发展趋势来看，我认为...

**王老师**：我觉得实践层面还需要关注...

### 结束语（2分钟）

**主持人**：感谢两位专家的精彩分享。通过今天的讨论，我们对"{workflow_request.user_input}"有了更深入的理解。希望对听众朋友们有所帮助。

---
脚本总时长：约30分钟
制作说明：建议使用不同的TTS声音来区分不同角色
"""

        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        logger.info(f"生成播客脚本: {file_path}")

    except Exception as e:
        logger.error(f"生成播客脚本失败: {str(e)}")

def _generate_sample_audio_placeholder(file_path, workflow_request):
    """生成音频占位符文件"""
    try:
        # 创建一个小的占位符文件
        placeholder_content = f"# 音频播客占位符\n\n主题: {workflow_request.user_input}\n生成时间: {datetime.now().isoformat()}\n\n注意: 这是一个占位符文件。在实际系统中，这里应该是生成的MP3音频文件。"

        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 创建实际的占位符文件，保持原始扩展名
        placeholder_path = file_path.replace('.mp3', '_info.txt')
        with open(placeholder_path, 'w', encoding='utf-8') as f:
            f.write(placeholder_content)

        # 创建一个空的MP3占位符（实际应该是真实的音频文件）
        with open(file_path, 'wb') as f:
            f.write(b'')  # 空文件作为占位符

        logger.info(f"生成音频占位符: {file_path}")

    except Exception as e:
        logger.error(f"生成音频占位符失败: {str(e)}")

def _generate_sample_video_placeholder(file_path, workflow_request):
    """生成视频占位符文件"""
    try:
        # 创建一个小的占位符文件
        placeholder_content = f"# 视频内容占位符\n\n主题: {workflow_request.user_input}\n生成时间: {datetime.now().isoformat()}\n\n注意: 这是一个占位符文件。在实际系统中，这里应该是生成的MP4视频文件。"

        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # 创建实际的占位符文件，保持原始扩展名
        placeholder_path = file_path.replace('.mp4', '_info.txt')
        with open(placeholder_path, 'w', encoding='utf-8') as f:
            f.write(placeholder_content)

        # 创建一个空的MP4占位符（实际应该是真实的视频文件）
        with open(file_path, 'wb') as f:
            f.write(b'')  # 空文件作为占位符

        logger.info(f"生成视频占位符: {file_path}")

    except Exception as e:
        logger.error(f"生成视频占位符失败: {str(e)}")

# ===== 诊断和调试API =====

@app.route('/api/system/diagnostics')
def system_diagnostics():
    """系统诊断信息"""
    try:
        output_dir = Path("output")

        diagnostics = {
            "system_status": "running",
            "output_directory": {
                "exists": output_dir.exists(),
                "path": str(output_dir.absolute()),
                "writable": output_dir.exists() and os.access(output_dir, os.W_OK),
                "files_count": len(list(output_dir.glob("*"))) if output_dir.exists() else 0
            },
            "active_tasks": len(task_status),
            "completed_tasks": len(task_results),
            "recent_files": []
        }

        # 获取最近生成的文件
        if output_dir.exists():
            recent_files = sorted(
                output_dir.glob("*"),
                key=lambda x: x.stat().st_mtime,
                reverse=True
            )[:10]

            diagnostics["recent_files"] = [
                {
                    "name": f.name,
                    "path": str(f),
                    "size": f.stat().st_size,
                    "created": datetime.fromtimestamp(f.stat().st_mtime).isoformat()
                }
                for f in recent_files if f.is_file()
            ]

        return jsonify({
            "success": True,
            "diagnostics": diagnostics
        })

    except Exception as e:
        logger.error(f"系统诊断失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/system/logs')
def get_system_logs():
    """获取系统日志"""
    try:
        # 这里可以读取实际的日志文件
        # 目前返回模拟的日志信息
        logs = [
            f"[{datetime.now().isoformat()}] INFO: 系统运行正常",
            f"[{datetime.now().isoformat()}] INFO: 活跃任务数: {len(task_status)}",
            f"[{datetime.now().isoformat()}] INFO: 完成任务数: {len(task_results)}",
        ]

        return jsonify({
            "success": True,
            "logs": logs
        })

    except Exception as e:
        logger.error(f"获取系统日志失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    # 确保必要目录存在
    for dir_name in ['output', 'temp', 'logs', 'templates', 'static']:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("🚀 启动深度研究系统")
    print("🌐 Web界面: http://localhost:8081")
    print("📖 研究场景: http://localhost:8081/api/research/scenarios")
    print("💡 按 Ctrl+C 停止服务")
    
    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=8081,
        debug=True,
        threaded=True
    )
