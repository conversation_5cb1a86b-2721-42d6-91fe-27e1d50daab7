#!/usr/bin/env python3
"""
启动增强版多智能体协作系统
Enhanced Multi-Agent Collaboration System Launcher
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'flask',
        'flask_cors', 
        'asyncio',
        'aiohttp',
        'moviepy',
        'pillow'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def check_directories():
    """检查并创建必要目录"""
    print("📁 检查目录结构...")
    
    required_dirs = [
        'templates',
        'static',
        'outputs',
        'temp',
        'logs',
        'assets/bgm',
        'assets/fonts'
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构检查完成")

def check_agents():
    """检查智能体文件"""
    print("🤖 检查智能体文件...")
    
    agent_files = [
        'agents/base_agent.py',
        'agents/audio_agent.py',
        'agents/image_context_agent.py',
        'agents/mcp_agent.py',
        'agents/search_agent.py',
        'agents/video_agent.py',
        'agents/workflow_agent.py',
        'agents/hitl_agent.py',
        'agents/scripting_agent.py'
    ]
    
    missing_files = []
    for agent_file in agent_files:
        if not Path(agent_file).exists():
            missing_files.append(agent_file)
    
    if missing_files:
        print(f"❌ 缺少智能体文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 智能体文件检查通过")
    return True

def check_templates():
    """检查模板文件"""
    print("🎨 检查模板文件...")
    
    if not Path('templates/enhanced_index.html').exists():
        print("❌ 缺少增强版模板文件")
        return False
    
    print("✅ 模板文件检查通过")
    return True

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 设置Flask环境
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'True'
    
    # 检查API密钥
    api_keys = [
        'DOUBAO_APP_ID',
        'DOUBAO_ACCESS_TOKEN',
        'OPENROUTER_API_KEY'
    ]
    
    missing_keys = []
    for key in api_keys:
        if not os.getenv(key):
            missing_keys.append(key)
    
    if missing_keys:
        print(f"⚠️  缺少API密钥: {', '.join(missing_keys)}")
        print("系统将以演示模式运行，部分功能可能不可用")
    else:
        print("✅ API密钥配置完成")

def start_system():
    """启动系统"""
    print("\n" + "="*60)
    print("🚀 启动增强版多智能体协作系统")
    print("="*60)
    
    # 系统检查
    if not check_dependencies():
        return False
    
    check_directories()
    
    if not check_agents():
        return False
    
    if not check_templates():
        return False
    
    setup_environment()
    
    print("\n📊 系统信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   端口: 8081")
    
    print("\n🌐 访问地址:")
    print("   主界面: http://localhost:8081")
    print("   基础版: http://localhost:8081/basic")
    print("   API文档: http://localhost:8081/api/agents")
    
    print("\n💡 功能特性:")
    print("   ✅ 多智能体协作管理")
    print("   ✅ 可视化工作流编排")
    print("   ✅ 多AI提供商管理")
    print("   ✅ 实时任务监控")
    print("   ✅ 智能体配置管理")
    
    print("\n" + "="*60)
    print("按 Ctrl+C 停止服务")
    print("="*60 + "\n")
    
    try:
        # 启动Flask应用
        from enhanced_app import app
        app.run(
            host='0.0.0.0',
            port=8081,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\n👋 系统已停止")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        return False

if __name__ == '__main__':
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                 增强版多智能体协作系统                        ║
    ║            Enhanced Multi-Agent Collaboration System         ║
    ║                                                              ║
    ║  🤖 智能体管理  🔄 工作流编排  ☁️  提供商管理  📊 实时监控    ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    success = start_system()
    sys.exit(0 if success else 1)
