#!/usr/bin/env python3
"""
Comprehensive test of the complete video generation system.
Tests all fixes: dynamic backgrounds, subtitle splitting, multi-emotion voices, BGM selection.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api import app
import requests
import json
import time

def test_complete_system():
    """Test the complete video generation pipeline with all fixes."""
    
    # Test configuration with all features
    config = {
        "book_review": {
            "title": "《活着》- 余华",
            "author": "余华",
            "content": """这是一部讲述生命韧性的伟大作品。主人公福贵经历了人生的种种磨难，从地主少爷沦落为贫农，失去了所有亲人，却依然坚强地活着。

故事发生在动荡的年代，福贵年轻时嗜赌如命，输光了家产。当他终于醒悟时，已经家破人亡。战争、饥荒、政治运动接踵而至，他的妻子、儿女一个个离他而去。

最让人心碎的是有庆的死亡。这个善良的孩子，为了给县长夫人献血而死。福贵抱着儿子冰冷的身体，那种撕心裂肺的痛苦让读者也忍不住流泪。

然而，生活还在继续。福贵学会了在苦难中寻找微小的快乐。他和老牛相依为命，在夕阳下耕作，诉说着往事。这不是认命，而是一种超越苦难的智慧。

余华用平实的语言，展现了中国人民的生存哲学。活着本身就是意义，不需要任何理由。这种朴素而深刻的生命观，让无数读者为之动容。"""
        },
        "style": "emotional",  # Test emotional style
        "voice": "zh_female_roumeinvyou_emo_v2_mars_bigtts",  # Test multi-emotion voice
        "bgm_path": "./assets/bgm/peaceful_bgm.mp3",  # Test different BGM
        "volume": 0.3,
        "enable_dynamic_backgrounds": True,  # Enable dynamic backgrounds
        "transition_interval": [10, 15]  # Switch every 10-15 seconds for testing
    }
    
    print("Starting comprehensive system test...")
    print(f"Testing with voice: {config['voice']}")
    print(f"Testing with BGM: {config['bgm_path']}")
    print(f"Dynamic backgrounds: {config['enable_dynamic_backgrounds']}")
    print(f"Transition interval: {config['transition_interval']} seconds")
    
    # Make the API request
    url = "http://localhost:8888/api/generate"
    
    try:
        print("\nSending generation request...")
        response = requests.post(url, json=config)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"Task created successfully: {task_id}")
            
            # Poll for status
            status_url = f"http://localhost:8888/api/status/{task_id}"
            while True:
                time.sleep(3)
                status_response = requests.get(status_url)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    current_status = status_data.get('status')
                    progress = status_data.get('progress', 0)
                    current_step = status_data.get('current_step', '')
                    
                    print(f"\rStatus: {current_status} | Progress: {progress}% | Step: {current_step}", end='', flush=True)
                    
                    if current_status == 'completed':
                        print("\n\n✅ Video generation completed successfully!")
                        print(f"Video URL: {status_data.get('video_url', 'N/A')}")
                        print(f"Output path: {status_data.get('output_path', 'N/A')}")
                        
                        # Check for dynamic backgrounds
                        metadata = status_data.get('metadata', {})
                        backgrounds = metadata.get('dynamic_backgrounds', [])
                        if backgrounds:
                            print(f"\n📸 Generated {len(backgrounds)} dynamic backgrounds:")
                            for i, bg in enumerate(backgrounds):
                                print(f"  {i+1}. {bg.get('prompt', 'N/A')} at {bg.get('start_time', 0):.1f}s")
                        
                        break
                    elif current_status == 'failed':
                        print(f"\n\n❌ Generation failed: {status_data.get('error', 'Unknown error')}")
                        break
                else:
                    print(f"\nError checking status: {status_response.status_code}")
                    break
        else:
            print(f"Error starting generation: {response.status_code}")
            print(f"Response: {response.text}")
    
    except Exception as e:
        print(f"Error during test: {str(e)}")

if __name__ == "__main__":
    print("=== Complete System Test ===")
    print("This test will verify:")
    print("1. Dynamic background switching (10-15 seconds)")
    print("2. Subtitle splitting (max 12 characters)")
    print("3. Multi-emotion voice synthesis")
    print("4. BGM selection")
    print("5. Complete video generation pipeline")
    print("\nMake sure the Flask server is running on port 8888!")
    print("-" * 50)
    
    test_complete_system()