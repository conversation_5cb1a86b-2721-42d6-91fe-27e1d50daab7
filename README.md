# AiriVerse AI Book Review Video Generator

An automated pipeline that generates Chinese book review videos from just a book title and author name.

## Features

- **AI-Powered Script Generation**: Uses OpenRouter Gemini 2.5 Pro to create engaging 40-50 second book reviews
- **Automatic Cover Generation**: Creates book cover illustrations using Bytedance Doubao API
- **Natural Voice Synthesis**: Converts text to speech using Doubao TTS with Chinese voice support
- **Intelligent Subtitle Timing**: Aligns audio and text using WhisperX and aeneas
- **Professional Video Assembly**: Creates 1080x1920 videos with animations, BGM, and subtitles

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/airiVerse_AI_MP4.git
cd airiVerse_AI_MP4
```

2. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install system dependencies:
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt-get install ffmpeg

# Windows
# Download from https://ffmpeg.org/download.html
```

5. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

## Configuration

Edit `.env` file with your API credentials:

- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `DOUBAO_API_KEY`: Your Bytedance Doubao API key
- `DOUBAO_IMAGE_ENDPOINT`: Doubao image generation endpoint
- `DOUBAO_TTS_ENDPOINT`: Doubao TTS endpoint

## Usage

### Web Interface

1. Start the Flask server:
```bash
python app.py
```

2. Open browser to `http://localhost:5000`

3. Enter book title and author, select BGM (optional)

4. Click "Generate" and wait for the video to be created

### Command Line

```python
from src.pipeline import BookReviewPipeline

pipeline = BookReviewPipeline()
result = pipeline.process_book("百年孤独", "加西亚·马尔克斯")

if result["status"] == "completed":
    print(f"Video saved to: {result['output_path']}")
```

## Project Structure

```
airiVerse_AI_MP4/
├── src/
│   ├── __init__.py
│   ├── config.py              # Configuration settings
│   ├── script_generator.py    # LLM script generation
│   ├── image_generator.py     # Cover image generation
│   ├── tts_generator.py       # Text-to-speech synthesis
│   ├── audio_aligner.py       # Audio-text alignment
│   ├── video_assembler.py     # Video assembly with MoviePy
│   └── pipeline.py            # Main orchestration pipeline
├── templates/
│   └── index.html            # Web interface
├── assets/
│   ├── bgm/                  # Background music files
│   └── fonts/                # Font files for subtitles
├── outputs/                  # Generated videos
├── temp/                     # Temporary processing files
├── app.py                    # Flask web application
├── requirements.txt          # Python dependencies
└── .env.example             # Environment variables template
```

## Pipeline Steps

1. **Script Generation**: AI generates book review and illustration prompt
2. **Image Generation**: Creates cover image from prompt
3. **Voice Synthesis**: Converts review text to speech
4. **Audio Alignment**: Synchronizes text with audio timing
5. **Video Assembly**: Combines all elements into final MP4

## Output Format

- Resolution: 1080x1920 (9:16 vertical)
- Frame Rate: 30 FPS
- Duration: ~45-60 seconds
- Features: Opening animation, background music, synchronized subtitles

## Troubleshooting

### WhisperX Installation Issues
If you encounter issues with WhisperX, you can use the fallback alignment method which uses equal time distribution.

### Font Issues
Place Chinese fonts in `assets/fonts/` directory. Recommended: NotoSansSC-Regular.ttf

### API Errors
- Ensure API keys are correctly set in `.env`
- Check API endpoint URLs are accessible
- Verify account has sufficient credits

## License

MIT License - see LICENSE file for details