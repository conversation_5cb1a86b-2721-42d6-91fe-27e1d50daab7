#!/usr/bin/env python3
"""
Quick test to identify which Doubao TTS voices actually work with current credentials
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.doubao_tts import DoubaoTTS

def test_basic_voices():
    """Test basic confirmed working voices"""
    
    tts = DoubaoTTS()
    
    if not tts.is_configured:
        print("❌ Doubao TTS not configured")
        return
    
    # Test the most basic voices that should work
    basic_voices = [
        "zh_female_roumeinvyou_emo_v2_mars_bigtts",  # This was working before
        "zh_male_beijingxiaoye_emo_v2_mars_bigtts",   # This was working before
        "zh_female_standard_mars_bigtts",
        "zh_male_standard_mars_bigtts",
        "zh_female_qingxin_mars_bigtts",
        "zh_male_chenwen_mars_bigtts"
    ]
    
    test_text = "测试语音"
    working_voices = []
    
    print(f"🔍 Testing {len(basic_voices)} basic voices...")
    
    for voice_id in basic_voices:
        print(f"\n🎤 Testing: {voice_id}")
        
        try:
            audio_data = tts.synthesize_speech(test_text, voice_id, "neutral")
            
            if audio_data and len(audio_data) > 0:
                print(f"  ✅ SUCCESS - {len(audio_data)} bytes")
                working_voices.append(voice_id)
            else:
                print(f"  ❌ FAILED - No audio data")
        except Exception as e:
            print(f"  ❌ ERROR - {e}")
    
    print(f"\n📊 RESULTS:")
    print(f"✅ Working voices ({len(working_voices)}):")
    for voice in working_voices:
        print(f"  - {voice}")
    
    print(f"\n❌ Failed voices ({len(basic_voices) - len(working_voices)}):")
    for voice in basic_voices:
        if voice not in working_voices:
            print(f"  - {voice}")
    
    if working_voices:
        print(f"\n💡 Recommended default voice: {working_voices[0]}")
        return working_voices[0]
    else:
        print(f"\n⚠️ No working voices found!")
        return None

if __name__ == "__main__":
    test_basic_voices()