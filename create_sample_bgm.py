#!/usr/bin/env python3
"""
Create sample BGM files by copying the existing one
This is a temporary solution until proper BGM files are added
"""

import os
import shutil
import json

def create_sample_bgm_files():
    """Create sample BGM files based on metadata"""
    
    bgm_dir = "./assets/bgm"
    metadata_file = os.path.join(bgm_dir, "bgm_metadata.json")
    source_file = os.path.join(bgm_dir, "gentle_bgm.mp3")
    
    if not os.path.exists(source_file):
        print(f"❌ Source BGM file not found: {source_file}")
        return
    
    # Load metadata
    with open(metadata_file, 'r', encoding='utf-8') as f:
        metadata = json.load(f)
    
    bgm_files = metadata.get('bgm_files', [])
    
    print(f"🎵 Creating {len(bgm_files)} sample BGM files...")
    
    created_count = 0
    for bgm_info in bgm_files:
        filename = bgm_info['filename']
        target_path = os.path.join(bgm_dir, filename)
        
        if not os.path.exists(target_path):
            try:
                # Copy the source file with new name
                shutil.copy2(source_file, target_path)
                print(f"  ✅ Created: {filename} ({bgm_info['name']})")
                created_count += 1
            except Exception as e:
                print(f"  ❌ Failed to create {filename}: {e}")
        else:
            print(f"  ⏭️ Already exists: {filename}")
    
    print(f"\n🎵 Sample BGM creation complete!")
    print(f"✅ Created {created_count} new BGM files")
    print(f"📁 Total BGM files: {len([f for f in os.listdir(bgm_dir) if f.endswith(('.mp3', '.wav', '.m4a'))])}")
    
    # List all BGM files
    print(f"\n📂 Available BGM files:")
    for file in os.listdir(bgm_dir):
        if file.endswith(('.mp3', '.wav', '.m4a')):
            file_path = os.path.join(bgm_dir, file)
            size = os.path.getsize(file_path)
            print(f"  • {file} ({size:,} bytes)")

if __name__ == "__main__":
    create_sample_bgm_files()