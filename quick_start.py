#!/usr/bin/env python3
"""
快速启动深度研究系统
"""

import os
import sys
import threading
import time
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent
src_path = project_root / 'src'
sys.path.insert(0, str(src_path))

print("🎯 深度研究系统 - 快速启动")
print("=" * 50)

# 创建目录
for dir_name in ['output', 'temp', 'logs']:
    (project_root / dir_name).mkdir(exist_ok=True)

# 测试模板系统
print("🧪 测试核心功能...")
try:
    from universal_research_template import (
        UniversalResearchPromptTemplate, 
        ResearchRequest, 
        ResearchScenario
    )
    
    template_system = UniversalResearchPromptTemplate()
    
    # 测试书籍分析
    book_request = ResearchRequest(
        scenario=ResearchScenario.BOOK_ANALYSIS,
        target="Structures: Or Why Things Don't Fall Down by <PERSON><PERSON><PERSON><PERSON>",
        audience="general"
    )
    
    book_prompt = template_system.generate_research_prompt(book_request)
    print(f"✅ 书籍分析提示词: {len(book_prompt)}字符")
    
    # 测试行业报告
    industry_request = ResearchRequest(
        scenario=ResearchScenario.INDUSTRY_REPORT,
        target="人工智能",
        audience="business"
    )
    
    industry_prompt = template_system.generate_research_prompt(industry_request)
    print(f"✅ 行业报告提示词: {len(industry_prompt)}字符")
    
    print(f"✅ 支持场景数量: {len(template_system.list_available_scenarios())}")
    
except Exception as e:
    print(f"❌ 系统测试失败: {str(e)}")
    sys.exit(1)

print("\n📋 支持的研究场景:")
scenarios = template_system.list_available_scenarios()
for i, scenario in enumerate(scenarios, 1):
    print(f"  {i}. {scenario}")

print("\n🎯 使用示例:")

print("\n1️⃣ 书籍分析示例 (基于您的原始提示词):")
print("目标:", book_request.target)
print("场景:", book_request.scenario.value)
print("提示词预览:")
print(book_prompt[:300] + "...")

print("\n2️⃣ 行业报告示例:")
print("目标:", industry_request.target)
print("场景:", industry_request.scenario.value) 
print("提示词预览:")
print(industry_prompt[:300] + "...")

print("\n3️⃣ 程序化调用示例:")
print("""
# Python代码示例
from universal_research_template import (
    UniversalResearchPromptTemplate, 
    ResearchRequest, 
    ResearchScenario
)

# 创建模板系统
template_system = UniversalResearchPromptTemplate()

# 创建研究请求
request = ResearchRequest(
    scenario=ResearchScenario.BOOK_ANALYSIS,
    target="您要分析的书籍",
    audience="general"
)

# 生成提示词
prompt = template_system.generate_research_prompt(request)
print(prompt)
""")

print("\n🚀 系统特性:")
print("✅ 完全独立运行")
print("✅ 支持10种研究场景") 
print("✅ 基于您的原始DeepResearch设计")
print("✅ 智能提示词生成")
print("✅ 多受众适配")
print("✅ 可扩展架构")

print("\n💡 下一步可以:")
print("1. 直接在Python中使用模板系统")
print("2. 启动API服务器: python3 start_new_server.py")
print("3. 集成到现有的视频生成流程中")
print("4. 扩展新的研究场景")

print(f"\n🎉 深度研究系统已准备就绪！")
print("基于您优秀的提示词设计，现在支持多种研究场景。")