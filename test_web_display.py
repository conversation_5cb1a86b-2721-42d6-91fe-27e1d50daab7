#!/usr/bin/env python3
"""
Test script to verify web interface displays generated content properly
"""

import sys
import os
import requests
import time
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_web_interface():
    """Test the web interface display functionality"""
    
    print("Testing Web Interface Content Display...")
    print("=" * 60)
    
    # Test data
    test_data = {
        "book_title": "红楼梦", 
        "author": "曹雪芹"
    }
    
    base_url = "http://localhost:8080"
    
    try:
        # Test if server is running
        response = requests.get(base_url, timeout=5)
        if response.status_code != 200:
            print("❌ Server not running on localhost:8080")
            return False
        
        print("✅ Server is running")
        
        # Start video generation
        print(f"📚 Testing with book: {test_data['book_title']} by {test_data['author']}")
        
        response = requests.post(
            f"{base_url}/api/generate",
            json=test_data,
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ Generation request failed: {response.status_code}")
            return False
        
        result = response.json()
        job_id = result.get('job_id')
        
        if not job_id:
            print("❌ No job ID returned")
            return False
        
        print(f"✅ Generation started, job ID: {job_id}")
        
        # Poll for status and check script display
        max_attempts = 30
        for attempt in range(max_attempts):
            time.sleep(2)
            
            status_response = requests.get(f"{base_url}/api/status/{job_id}")
            if status_response.status_code != 200:
                continue
                
            status = status_response.json()
            print(f"📊 Attempt {attempt + 1}: Status = {status.get('status', 'unknown')}")
            
            # Check if script is generated
            if status.get('steps', {}).get('script_generation') == 'completed':
                script_content = status.get('script')
                if script_content:
                    print("✅ Script generation completed!")
                    print(f"📝 Script length: {len(script_content)} characters")
                    print("📖 Generated script preview:")
                    print("-" * 50)
                    print(script_content[:200] + "..." if len(script_content) > 200 else script_content)
                    print("-" * 50)
                    return True
                else:
                    print("⚠️ Script completed but no content found")
            
            # Check if process completed or failed
            if status.get('status') in ['completed', 'failed']:
                break
        
        print("❌ Script generation test timed out or failed")
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    success = test_web_interface()
    if success:
        print("\n🎉 Web interface content display test PASSED!")
        print("The generated Chinese book review should now be visible in the web UI.")
    else:
        print("\n❌ Web interface test FAILED!")
        print("Please check the server status and try again.")