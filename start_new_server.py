#!/usr/bin/env python3
"""
启动深度研究系统 - 使用备用端口
"""

import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent
src_path = project_root / 'src'
sys.path.insert(0, str(src_path))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv 未安装")

# 创建必要目录
for dir_name in ['output', 'temp', 'logs']:
    dir_path = project_root / dir_name
    dir_path.mkdir(exist_ok=True)

print("🎯 深度研究系统")
print("=" * 40)

# 测试系统
print("🧪 测试模板系统...")
try:
    from universal_research_template import (
        UniversalResearchPromptTemplate, 
        ResearchRequest, 
        ResearchScenario
    )
    
    template_system = UniversalResearchPromptTemplate()
    
    # 测试生成
    request = ResearchRequest(
        scenario=ResearchScenario.BOOK_ANALYSIS,
        target="测试目标",
        audience="general"
    )
    
    prompt = template_system.generate_research_prompt(request)
    print(f"✅ 提示词生成成功，长度: {len(prompt)}字符")
    print(f"📋 支持的场景: {len(template_system.list_available_scenarios())}个")
    
except Exception as e:
    print(f"❌ 系统测试失败: {str(e)}")
    sys.exit(1)

# 启动API服务器
print("\n🚀 启动API服务器...")

from flask import Flask, jsonify, request
from flask_cors import CORS
import uuid
from datetime import datetime

app = Flask(__name__)
CORS(app)

@app.route('/')
def home():
    return jsonify({
        "message": "🎯 深度研究系统 API",
        "version": "1.0.0",
        "status": "运行中",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "场景列表": "/api/scenarios",
            "生成提示词": "/api/generate",
            "系统测试": "/api/test",
            "使用示例": "/api/examples"
        }
    })

@app.route('/api/scenarios')
def get_scenarios():
    """获取支持的研究场景"""
    try:
        scenarios = template_system.list_available_scenarios()
        scenario_info = {}
        
        for scenario in scenarios:
            scenario_enum = ResearchScenario(scenario)
            config = template_system.get_scenario_info(scenario_enum)
            scenario_info[scenario] = {
                "name": scenario,
                "description": config.get("research_type", ""),
                "analysis_depth": config.get("analysis_depth", ""),
                "task_count": len(config.get("tasks", []))
            }
        
        return jsonify({
            "success": True,
            "scenarios": scenario_info,
            "count": len(scenarios)
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/generate', methods=['POST'])
def generate_prompt():
    """生成研究提示词"""
    try:
        data = request.json
        target = data.get('target', '默认研究目标')
        scenario = data.get('scenario', 'book_analysis')
        audience = data.get('audience', 'general')
        
        # 创建请求
        research_request = ResearchRequest(
            scenario=ResearchScenario(scenario),
            target=target,
            audience=audience,
            additional_requirements=data.get('additional_requirements', {})
        )
        
        # 生成提示词
        prompt = template_system.generate_research_prompt(research_request)
        
        return jsonify({
            "success": True,
            "request_id": str(uuid.uuid4()),
            "target": target,
            "scenario": scenario,
            "audience": audience,
            "prompt_length": len(prompt),
            "prompt_preview": prompt[:500] + "...",
            "full_prompt": prompt,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/test')
def test_api():
    """API健康检查"""
    return jsonify({
        "success": True,
        "message": "API运行正常",
        "system_status": "健康",
        "supported_scenarios": len(template_system.list_available_scenarios()),
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/examples')
def get_examples():
    """获取使用示例"""
    examples = {
        "book_analysis": {
            "description": "基于您的原始提示词设计的书籍深度分析",
            "example": {
                "target": "Structures: Or Why Things Don't Fall Down by J.E. Gordon",
                "scenario": "book_analysis",
                "audience": "general"
            },
            "curl_command": """curl -X POST http://localhost:5002/api/generate \\
  -H "Content-Type: application/json" \\
  -d '{"target": "Structures: Or Why Things Don'\''t Fall Down by J.E. Gordon", "scenario": "book_analysis", "audience": "general"}'"""
        },
        "industry_report": {
            "description": "商业级行业分析报告",
            "example": {
                "target": "人工智能",
                "scenario": "industry_report",
                "audience": "business",
                "additional_requirements": {
                    "focus_area": "中国市场",
                    "time_frame": "2024-2025"
                }
            },
            "curl_command": """curl -X POST http://localhost:5002/api/generate \\
  -H "Content-Type: application/json" \\
  -d '{"target": "人工智能", "scenario": "industry_report", "audience": "business"}'"""
        },
        "research_progress": {
            "description": "学术研究进展分析",
            "example": {
                "target": "量子计算",
                "scenario": "research_progress",
                "audience": "academic"
            },
            "curl_command": """curl -X POST http://localhost:5002/api/generate \\
  -H "Content-Type: application/json" \\
  -d '{"target": "量子计算", "scenario": "research_progress", "audience": "academic"}'"""
        }
    }
    
    return jsonify({
        "success": True,
        "examples": examples,
        "note": "这些示例展示了如何使用深度研究系统的不同场景"
    })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False, 
        "error": "接口不存在",
        "available_endpoints": ["/", "/api/scenarios", "/api/generate", "/api/test", "/api/examples"]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "内部服务器错误"}), 500

if __name__ == "__main__":
    print("🌐 API服务地址: http://localhost:5002")
    print("📖 API文档地址: http://localhost:5002/api/examples")
    print("🧪 健康检查: http://localhost:5002/api/test")
    print("💡 按 Ctrl+C 停止服务\n")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5002,
            debug=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {str(e)}")