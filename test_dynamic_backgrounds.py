#!/usr/bin/env python3
"""
Test the dynamic background generation system
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.dynamic_background_generator import DynamicBackgroundGenerator
from src.image_generator import ImageGenerator

def test_dynamic_backgrounds():
    """Test dynamic background generation"""
    
    print("🎨 Testing Dynamic Background Generation")
    print("=" * 50)
    
    # Test content analyzer
    bg_gen = DynamicBackgroundGenerator()
    
    # Sample alignment data
    sample_alignment = [
        {"text": "这是一个关于冒险的故事", "start": 0, "end": 3000},
        {"text": "主人公在森林中迷路了", "start": 3000, "end": 7000},
        {"text": "夜晚降临，星空美丽", "start": 7000, "end": 10000}
    ]
    
    total_duration = 12.0  # 12 seconds
    
    # Test planning
    print("📋 Testing background sequence planning...")
    segments = bg_gen.plan_background_sequence(sample_alignment, total_duration)
    
    print(f"✅ Planned {len(segments)} segments:")
    for seg in segments:
        print(f"  📍 {seg['start_time']:.1f}s-{seg['end_time']:.1f}s: {seg['content_analysis']['image_type']}")
        print(f"     Prompt: {seg['image_prompt'][:60]}...")
    
    # Test image generator connection
    print("\n🖼️ Testing image generator...")
    image_gen = ImageGenerator()
    
    # Test with a simple landscape prompt
    test_dir = "./temp_test"
    os.makedirs(test_dir, exist_ok=True)
    
    test_image_path = os.path.join(test_dir, "test_bg.jpg")
    success = image_gen.generate_cover_image(
        "beautiful mountain landscape with sunset",
        test_image_path
    )
    
    if success and os.path.exists(test_image_path):
        print(f"✅ Image generation test successful: {test_image_path}")
        file_size = os.path.getsize(test_image_path)
        print(f"   📄 File size: {file_size:,} bytes")
    else:
        print("❌ Image generation test failed")
    
    # Clean up
    try:
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        os.rmdir(test_dir)
    except:
        pass
    
    print("\n🎬 Dynamic background system test complete!")

if __name__ == "__main__":
    test_dynamic_backgrounds()