# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# Environment variables
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Output directories
outputs/
temp/

# Media files
*.mp4
*.mp3
*.wav
*.jpg
*.png
*.gif

# macOS
.DS_Store

# Logs
*.log

# Database
*.db
*.sqlite

# Model cache
.cache/
models/

# Temporary files
temp-audio.m4a
*.tmp