#!/usr/bin/env python3
"""
Test script to verify emotion tag cleanup functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.video_assembler import VideoAssembler

def test_emotion_tag_cleanup():
    """Test the emotion tag cleaning functionality"""
    
    print("🧪 Testing Emotion Tag Cleanup")
    print("=" * 40)
    
    assembler = VideoAssembler()
    
    # Test cases with various emotion tag formats
    test_cases = [
        # Standard format
        '<speak emotion="happy">这是一个快乐的句子。</speak>',
        
        # Multiple emotions in one text
        '<speak emotion="happy">开心的部分</speak> 普通文字 <speak emotion="sad">悲伤的部分</speak>',
        
        # Malformed tags
        'speak emotion"angry"愤怒的文字</speak>',
        
        # Mixed with normal text
        '这是正常文字。<speak emotion="excited">这是兴奋的文字！</speak>更多正常文字。',
        
        # Incomplete tags
        '<speak emotion="neutral">不完整的标签',
        
        # No emotion tags
        '这是没有情感标签的普通文字。',
        
        # Empty emotion
        '<speak emotion="">空情感标签</speak>',
        
        # Nested or complex cases
        '<speak emotion="happy">外层<speak emotion="sad">内层</speak>继续外层</speak>',
        
        # Real example from script
        '<speak emotion="excited">这本书真的让我感到震撼！</speak><speak emotion="neutral">作者的写作技巧非常出色。</speak>'
    ]
    
    print("Testing emotion tag cleanup:")
    print()
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"Test {i}:")
        print(f"  Input:  {test_input}")
        
        cleaned = assembler._clean_emotion_tags_for_subtitles(test_input)
        print(f"  Output: {cleaned}")
        
        # Check if any emotion tags remain
        has_tags = '<speak' in cleaned or '</speak>' in cleaned or 'emotion=' in cleaned
        status = "❌ TAGS REMAIN" if has_tags else "✅ CLEAN"
        print(f"  Status: {status}")
        print()
    
    print("=" * 40)
    print("🎯 Testing subtitle segment processing:")
    
    # Test with sample segments like those from the pipeline
    test_segments = [
        {
            'text': '<speak emotion="happy">欢迎来到今天的书评节目！</speak>',
            'start': 0,
            'end': 3000
        },
        {
            'text': '<speak emotion="neutral">今天我们要讨论的是</speak><speak emotion="excited">一本令人震撼的小说。</speak>',
            'start': 3000,
            'end': 8000
        },
        {
            'text': 'speak emotion"sad"这个故事让人感到悲伤。',
            'start': 8000,
            'end': 12000
        }
    ]
    
    for i, segment in enumerate(test_segments, 1):
        print(f"Segment {i}:")
        print(f"  Original: {segment['text']}")
        
        cleaned = assembler._clean_emotion_tags_for_subtitles(segment['text'])
        print(f"  Cleaned:  {cleaned}")
        
        has_tags = '<speak' in cleaned or '</speak>' in cleaned or 'emotion=' in cleaned
        status = "❌ TAGS REMAIN" if has_tags else "✅ CLEAN"
        print(f"  Status:   {status}")
        print()

if __name__ == "__main__":
    test_emotion_tag_cleanup()