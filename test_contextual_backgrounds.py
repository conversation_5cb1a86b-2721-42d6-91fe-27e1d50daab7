#!/usr/bin/env python3
"""
Test contextual background generation system.
This tests the new intelligent background system that generates images
based on specific content context (e.g., "Little Prince and fox" scene).
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api import app
import requests
import json
import time

def test_little_prince_contextual():
    """Test Little Prince with specific contextual scenes"""
    
    config = {
        "book_review": {
            "title": "《小王子》- 安托万·德·圣埃克苏佩里",
            "author": "安托万·德·圣埃克苏佩里",
            "content": """小王子来自B-612小行星，那里只有他一个人和一朵玫瑰花。玫瑰花很骄傲，总是要求小王子照顾她。

小王子离开了自己的星球，开始了宇宙旅行。他访问了许多小行星，遇到了奇怪的大人们：一个孤独的国王，一个爱慕虚荣的人，还有一个每天数星星的商人。

最重要的相遇发生在地球上。在广阔的沙漠中，小王子遇到了一只聪明的狐狸。狐狸教会了小王子什么是"驯养"。

"请你驯养我吧！"狐狸说。通过驯养，小王子和狐狸建立了独特的关系。狐狸告诉小王子一个秘密："只有用心才能看得清楚，真正重要的东西用眼睛是看不见的。"

当小王子要离开时，狐狸很伤心，但它说："现在你要对你驯养的玫瑰负责。"小王子明白了，他的玫瑰是世界上独一无二的，因为他爱她。

在沙漠中，小王子还遇到了一个飞行员。飞行员的飞机坏了，他们一起寻找水源。小王子告诉飞行员关于他的星球和玫瑰的故事。

最后，小王子想要回到自己的星球B-612。在夜空下，在满天繁星的见证下，小王子踏上了回家的路。"""
        },
        "style": "gentle",
        "voice": "zh_female_roumeinvyou_emo_v2_mars_bigtts",
        "bgm_path": "./assets/bgm/gentle_bgm.mp3",
        "enable_dynamic_backgrounds": True,
        "transition_interval": [12, 18]  # Test contextual timing
    }
    
    print("🌟 Testing Little Prince Contextual Backgrounds")
    print("Expected contextual scenes:")
    print("1. Little Prince with rose on small planet B-612")
    print("2. Little Prince meeting strange adults on asteroids")
    print("3. Little Prince and fox in desert (taming scene)")
    print("4. Little Prince with pilot in desert")
    print("5. Little Prince under starry sky returning home")
    print()
    
    return test_video_generation(config, "Little Prince Contextual")

def test_living_contextual():
    """Test 活着 with specific contextual scenes"""
    
    config = {
        "book_review": {
            "title": "《活着》- 余华",
            "author": "余华", 
            "content": """年轻的福贵是个富家少爷，喜欢赌博。他经常去赌场，把家里的田产都输光了。妻子家珍很生气，带着女儿凤霞回娘家了。

后来福贵醒悟了，开始努力工作。他和家珍重新生活在一起，还有了儿子有庆。一家人在农村过着简单但幸福的生活。

战争来了，福贵被抓去当兵。在战场上，他看到了很多死亡和痛苦。好不容易活着回到家，发现家里的情况更糟了。

最痛苦的是有庆的死亡。善良的有庆为了给县长夫人献血救命，却因为抽血过多而死去。福贵抱着儿子冰冷的身体，在医院里痛哭。

后来，女儿凤霞长大了，嫁给了城里的工人二喜。但是凤霞在生孩子的时候难产死了，留下了小外孙苦根。

妻子家珍也病死了。女婿二喜在工地上被石板压死了。最后连小外孙苦根也因为吃豆子撑死了。

只剩下福贵一个人，和一头老牛相依为命。每天黄昏，他赶着牛在田野里耕作，在夕阳下慢慢走回家。"""
        },
        "style": "emotional",
        "voice": "zh_male_beijingxiaoye_emo_v2_mars_bigtts",
        "bgm_path": "./assets/bgm/peaceful_bgm.mp3",
        "enable_dynamic_backgrounds": True,
        "transition_interval": [15, 20]
    }
    
    print("🌾 Testing 《活着》Contextual Backgrounds")
    print("Expected contextual scenes:")
    print("1. Young Fugui gambling scene")
    print("2. Fugui and Jiazhen happy family life in rural village")
    print("3. War battlefield scene with soldiers")
    print("4. Hospital scene with Youqing's death")
    print("5. Fugui alone with old ox in sunset fields")
    print()
    
    return test_video_generation(config, "活着 Contextual")

def test_video_generation(config, title):
    """Generate test video and analyze contextual backgrounds"""
    
    url = "http://localhost:8888/api/generate"
    
    try:
        print(f"🚀 Sending generation request for {title}...")
        response = requests.post(url, json=config)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ Task created: {task_id}")
            
            # Poll for completion
            status_url = f"http://localhost:8888/api/status/{task_id}"
            while True:
                time.sleep(3)
                status_response = requests.get(status_url)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    current_status = status_data.get('status')
                    progress = status_data.get('progress', 0)
                    current_step = status_data.get('current_step', '')
                    
                    print(f"\\r📊 Status: {current_status} | Progress: {progress}% | Step: {current_step}", end='', flush=True)
                    
                    if current_status == 'completed':
                        print(f"\\n\\n🎉 {title} video completed!")
                        
                        # Analyze contextual results
                        print("\\n🔍 Analyzing Contextual Background Results:")
                        
                        metadata = status_data.get('metadata', {})
                        backgrounds = metadata.get('dynamic_backgrounds', [])
                        
                        if backgrounds:
                            print(f"Generated {len(backgrounds)} contextual backgrounds:")
                            
                            for i, bg in enumerate(backgrounds):
                                print(f"\\n📸 Background {i+1}: {bg.get('start_time', 0):.1f}s - {bg.get('end_time', 0):.1f}s")
                                print(f"   Importance: {bg.get('importance', 0):.2f}")
                                print(f"   Prompt: {bg.get('prompt', 'N/A')}")
                                
                                # Check for specific contextual elements
                                characters = bg.get('characters', [])
                                locations = bg.get('locations', [])
                                
                                if characters:
                                    char_names = [c.get('name', 'Unknown') for c in characters]
                                    print(f"   🎭 Characters: {', '.join(char_names)}")
                                
                                if locations:
                                    loc_names = [l.get('name', 'Unknown') for l in locations]
                                    print(f"   📍 Locations: {', '.join(loc_names)}")
                                
                                print(f"   📝 Context: {bg.get('relevant_text', 'N/A')[:100]}...")
                            
                            # Analyze quality of contextual detection
                            total_importance = sum(bg.get('importance', 0) for bg in backgrounds)
                            avg_importance = total_importance / len(backgrounds) if backgrounds else 0
                            
                            backgrounds_with_chars = [bg for bg in backgrounds if bg.get('characters')]
                            backgrounds_with_locs = [bg for bg in backgrounds if bg.get('locations')]
                            
                            print(f"\\n📊 Contextual Analysis Summary:")
                            print(f"   Average Importance: {avg_importance:.2f}")
                            print(f"   Backgrounds with Characters: {len(backgrounds_with_chars)}/{len(backgrounds)}")
                            print(f"   Backgrounds with Locations: {len(backgrounds_with_locs)}/{len(backgrounds)}")
                            
                            # Check timing intelligence
                            if len(backgrounds) > 1:
                                durations = [bg.get('end_time', 0) - bg.get('start_time', 0) for bg in backgrounds]
                                avg_duration = sum(durations) / len(durations)
                                print(f"   Average Segment Duration: {avg_duration:.1f}s")
                                
                                if avg_duration >= 15:
                                    print("   ✅ Good: Segments are long enough to avoid frequent switching")
                                else:
                                    print("   ⚠️ Warning: Segments might be too short")
                        
                        else:
                            print("❌ No dynamic backgrounds found in metadata")
                        
                        print(f"\\n📁 Video saved: {status_data.get('output_path', 'Unknown')}")
                        return True
                        
                    elif current_status == 'failed':
                        print(f"\\n\\n❌ {title} generation failed: {status_data.get('error', 'Unknown error')}")
                        return False
                else:
                    print(f"\\n❌ Error checking status: {status_response.status_code}")
                    return False
        else:
            print(f"❌ Error starting generation: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

def main():
    print("=== Testing Contextual Background Generation ===")
    print("This system generates backgrounds based on specific content context")
    print("instead of generic themes. For example:")
    print("- 'Little Prince and fox' → Image of Little Prince with fox")
    print("- 'Fugui gambling' → Image of Chinese man gambling")
    print("- 'Starry night' → Image of starry sky scene")
    print()
    print("Key features being tested:")
    print("1. 📝 Content context analysis (characters, locations, scenes)")
    print("2. 🧠 Intelligent timing (minimum 15s between changes)")
    print("3. 🎯 Importance-based switching (only for significant scene changes)")
    print("4. 🎨 Specific scene generation (not generic themes)")
    print()
    print("Make sure the server is running on port 8888!")
    print("-" * 70)
    
    # Test different scenarios
    tests = [
        ("Little Prince Contextual Test", test_little_prince_contextual),
        ("活着 (Living) Contextual Test", test_living_contextual)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results[test_name] = success
        except KeyboardInterrupt:
            print(f"\\n⏹️ Test interrupted by user")
            break
        except Exception as e:
            print(f"\\n❌ Test failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\\n{'='*70}")
    print("📊 CONTEXTUAL BACKGROUND TEST SUMMARY")
    print(f"{'='*70}")
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    print(f"\\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 Contextual background system is working correctly!")
        print("🎯 Backgrounds should now change based on specific content context")
        print("⏱️ Timing should be intelligent to avoid frequent switching")
    else:
        print("⚠️ Some issues need attention.")
        print("💡 Check the console output for detailed analysis")

if __name__ == "__main__":
    main()