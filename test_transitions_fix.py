#!/usr/bin/env python3
"""
Test the fixed transition system
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.background_transitions import BackgroundTransitions, MOVIEPY_AVAILABLE

def test_transitions():
    """Test the transition system fixes"""
    
    print("🎬 Testing Background Transitions")
    print("=" * 40)
    
    print(f"MoviePy Available: {MOVIEPY_AVAILABLE}")
    
    if not MOVIEPY_AVAILABLE:
        print("❌ MoviePy not available, cannot test transitions")
        return
    
    # Test transition creation
    transitions = BackgroundTransitions()
    
    # Create test directory and simple test images
    test_dir = "./temp_test_transitions"
    os.makedirs(test_dir, exist_ok=True)
    
    # Create simple test images
    from PIL import Image
    
    # Image 1 - Red background
    img1 = Image.new('RGB', (1920, 1080), color='red')
    img1_path = os.path.join(test_dir, "test1.jpg")
    img1.save(img1_path)
    
    # Image 2 - Blue background
    img2 = Image.new('RGB', (1920, 1080), color='blue')
    img2_path = os.path.join(test_dir, "test2.jpg")
    img2.save(img2_path)
    
    # Test different transition types
    test_transitions = ["none", "fade", "crossfade", "slide_left", "zoom_in"]
    
    for transition_type in test_transitions:
        print(f"\n🎭 Testing {transition_type} transition...")
        
        try:
            clip = transitions.create_transition_clip(
                img1_path, img2_path, transition_type, 
                duration=2.0, start_time=0.0, video_size=(1920, 1080)
            )
            
            if clip:
                print(f"  ✅ {transition_type} transition created successfully")
                print(f"     Duration: {clip.duration}s")
                clip.close()  # Clean up
            else:
                print(f"  ❌ {transition_type} transition failed")
                
        except Exception as e:
            print(f"  ❌ {transition_type} transition error: {e}")
    
    # Test background sequence creation
    print(f"\n🎬 Testing background sequence creation...")
    
    test_segments = [
        {
            'image_path': img1_path,
            'start_time': 0.0,
            'duration': 2.0,
            'transition_type': 'none'
        },
        {
            'image_path': img2_path,
            'start_time': 2.0,
            'duration': 2.0,
            'transition_type': 'fade'
        }
    ]
    
    try:
        clips = transitions.create_background_sequence(test_segments, (1920, 1080))
        print(f"  ✅ Background sequence created: {len(clips)} clips")
        
        # Clean up clips
        for clip in clips:
            clip.close()
            
    except Exception as e:
        print(f"  ❌ Background sequence error: {e}")
    
    # Clean up test files
    try:
        os.remove(img1_path)
        os.remove(img2_path)
        os.rmdir(test_dir)
    except:
        pass
    
    print(f"\n✅ Transition system test complete!")

if __name__ == "__main__":
    test_transitions()