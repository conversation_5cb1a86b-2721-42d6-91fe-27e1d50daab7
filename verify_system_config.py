#!/usr/bin/env python3
"""
Verify all system components are properly configured after fixes.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_bgm_files():
    """Verify all BGM files exist."""
    print("\n📻 Verifying BGM files...")
    bgm_dir = "assets/bgm"
    expected_files = [
        "gentle_bgm.mp3", "classical_bgm.mp3", "upbeat_bgm.mp3",
        "peaceful_bgm.mp3", "mysterious_bgm.mp3", "romantic_bgm.mp3",
        "adventure_bgm.mp3", "nostalgic_bgm.mp3", "scientific_bgm.mp3",
        "meditation_bgm.mp3", "folk_bgm.mp3", "corporate_bgm.mp3"
    ]
    
    missing = []
    for file in expected_files:
        path = os.path.join(bgm_dir, file)
        if os.path.exists(path):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - MISSING")
            missing.append(file)
    
    return len(missing) == 0

def verify_voices():
    """Verify multi-emotion voices configuration."""
    print("\n🎤 Verifying multi-emotion voices...")
    from src.doubao_tts import DoubaoTTS
    
    tts = DoubaoTTS()
    multi_emotion_voices = [v for v, info in tts.voices.items() if info.get('emotion_support')]
    
    print(f"  Total voices: {len(tts.voices)}")
    print(f"  Multi-emotion voices: {len(multi_emotion_voices)}")
    
    for voice_id in multi_emotion_voices[:5]:  # Show first 5
        info = tts.voices[voice_id]
        print(f"  ✅ {info['name']} - {len(info.get('emotions', []))} emotions")
    
    if len(multi_emotion_voices) > 5:
        print(f"  ... and {len(multi_emotion_voices) - 5} more")
    
    return len(multi_emotion_voices) >= 15

def verify_dynamic_backgrounds():
    """Verify dynamic background system."""
    print("\n🖼️ Verifying dynamic background system...")
    
    modules = [
        ("Background Generator", "src.dynamic_background_generator", "DynamicBackgroundGenerator"),
        ("Background Transitions", "src.background_transitions", "BackgroundTransitions")
    ]
    
    all_ok = True
    for name, module_name, class_name in modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"  ✅ {name} - {class_name} found")
        except Exception as e:
            print(f"  ❌ {name} - Error: {str(e)}")
            all_ok = False
    
    return all_ok

def verify_subtitle_splitting():
    """Verify subtitle splitting with 12-char limit."""
    print("\n📝 Verifying subtitle splitting (12-char limit)...")
    from src.audio_aligner import AudioAligner
    
    aligner = AudioAligner()
    
    # Test case
    test_segment = {
        'text': '这是一个测试句子，用来验证字幕分割功能是否正常工作，每段不超过十二个字符。',
        'start': 0,
        'end': 10000,
        'duration': 10000
    }
    
    # Split the segment
    split_segments = aligner.split_long_sentences([test_segment])
    
    print(f"  Original: {test_segment['text']}")
    print(f"  Split into {len(split_segments)} parts:")
    
    all_ok = True
    for i, seg in enumerate(split_segments):
        char_count = len(seg['text'])
        duration_str = f", {seg.get('duration', 0)/1000:.1f}s" if 'duration' in seg else ""
        if char_count <= 12:
            print(f"    ✅ Part {i+1}: '{seg['text']}' ({char_count} chars{duration_str})")
        else:
            print(f"    ❌ Part {i+1}: '{seg['text']}' ({char_count} chars) - TOO LONG!")
            all_ok = False
    
    return all_ok

def main():
    print("=== System Configuration Verification ===")
    print("Checking all components after fixes...")
    
    results = {
        "BGM Files": verify_bgm_files(),
        "Multi-emotion Voices": verify_voices(),
        "Dynamic Backgrounds": verify_dynamic_backgrounds(),
        "Subtitle Splitting": verify_subtitle_splitting()
    }
    
    print("\n📊 Summary:")
    all_passed = True
    for component, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {component}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All systems verified successfully!")
        print("You can now run test_complete_system.py to generate a test video.")
    else:
        print("\n⚠️ Some components need attention. Please check the errors above.")

if __name__ == "__main__":
    main()