# 测试完整系统 - Test Instructions

本文档说明如何测试所有修复后的功能。

## 所有已修复的功能

1. **✅ 豆包TTS语音权限问题**
   - 修复了 "Resource permissions not granted" 错误
   - 验证了可用的语音并移除了无权限的语音
   
2. **✅ BGM选择问题**
   - 修复了 "BGM大部分都不能选择" 的问题
   - 创建了12个BGM文件供选择

3. **✅ 添加了15个多情感语音**
   - 根据用户提供的列表添加了所有多情感语音
   - 支持happy, sad, angry等多种情感

4. **✅ 动态背景切换功能**
   - 每10-20秒自动切换背景图片
   - 基于脚本内容智能生成相关背景
   - 支持淡入淡出等过渡效果

5. **✅ 字幕长度优化**
   - 限制每段字幕最多12个字符
   - 智能分割长句子
   - 优化时间轴对齐

## 测试步骤

### 1. 验证系统配置
```bash
python3 verify_system_config.py
```
应该看到所有项目都显示 ✅ PASS

### 2. 启动Flask服务器
```bash
python3 start_server.py
```
或者直接运行：
```bash
python3 app.py
```

### 3. 运行完整系统测试
在另一个终端窗口运行：
```bash
python3 test_complete_system.py
```

这个测试会：
- 使用多情感语音 (柔美女友)
- 使用不同的BGM (peaceful_bgm.mp3)
- 启用动态背景 (10-15秒切换)
- 测试字幕分割功能

### 4. 检查生成的视频

生成完成后，检查以下内容：

1. **背景切换**: 视频应该每10-15秒切换一次背景
2. **字幕显示**: 每段字幕不应超过12个字符
3. **语音情感**: 应该能听到情感变化
4. **BGM播放**: 应该有背景音乐

## 可用的配置选项

### 语音选择 (15个多情感语音)
- zh_female_roumeinvyou_emo_v2_mars_bigtts (柔美女友)
- zh_male_beijingxiaoye_emo_v2_mars_bigtts (北京小爷)
- zh_male_yangguangqingnian_emo_v2_mars_bigtts (阳光青年)
- 等等...

### BGM选择 (12个选项)
- gentle_bgm.mp3 (温柔舒缓)
- peaceful_bgm.mp3 (宁静平和)
- mysterious_bgm.mp3 (神秘悬疑)
- romantic_bgm.mp3 (浪漫温馨)
- 等等...

### 动态背景设置
```json
{
  "enable_dynamic_backgrounds": true,
  "transition_interval": [10, 20]  // 秒
}
```

## 故障排除

1. **如果服务器启动失败**
   - 检查端口8888是否被占用
   - 确保安装了所有依赖

2. **如果语音生成失败**
   - 检查豆包API凭证是否正确
   - 确认使用的是验证过的语音ID

3. **如果背景不切换**
   - 确保 enable_dynamic_backgrounds 设置为 true
   - 检查豆包图像生成API是否正常

4. **如果字幕显示过长**
   - 这应该已经修复，如果还有问题请报告

## 自定义测试

您可以修改 `test_complete_system.py` 中的配置来测试不同的场景：

```python
config = {
    "book_review": {
        "title": "您的书名",
        "content": "您的书评内容..."
    },
    "voice": "选择一个语音ID",
    "bgm_path": "./assets/bgm/选择一个BGM.mp3",
    "enable_dynamic_backgrounds": True/False,
    "transition_interval": [最小秒数, 最大秒数]
}
```

祝测试顺利！🎉