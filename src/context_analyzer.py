import re
import json
from typing import List, Dict, Tuple, Optional
from .config import Config

class ContextualThemeAnalyzer:
    """
    Analyze text content to extract specific visual themes and scenes
    for context-aware background generation
    """
    
    def __init__(self):
        # Specific scene patterns for different books
        self.book_scenes = {
            "小王子": {
                "characters": {
                    "小王子": "little prince character",
                    "狐狸": "fox character", 
                    "玫瑰": "rose flower",
                    "飞行员": "pilot aviator",
                    "国王": "king on throne",
                    "商人": "businessman",
                    "地理学家": "geographer with maps",
                    "酒鬼": "sad drunkard"
                },
                "locations": {
                    "小行星": "small planet asteroid in space",
                    "沙漠": "vast desert landscape",
                    "星空": "starry night sky",
                    "B-612": "small planet with baobab trees",
                    "地球": "Earth from space view"
                },
                "scenes": {
                    "驯养": "little prince taming fox scene",
                    "告别": "farewell departure scene",
                    "玫瑰园": "garden full of roses",
                    "日落": "sunset on small planet",
                    "旅行": "journey through space planets"
                }
            },
            "活着": {
                "characters": {
                    "福贵": "old Chinese farmer",
                    "家珍": "Chinese rural woman",
                    "有庆": "young Chinese boy",
                    "凤霞": "young Chinese girl",
                    "苦根": "small Chinese child"
                },
                "locations": {
                    "农村": "Chinese rural village",
                    "田野": "Chinese farmland fields",
                    "老房子": "old Chinese traditional house",
                    "医院": "old Chinese hospital",
                    "学校": "rural Chinese school"
                },
                "scenes": {
                    "耕作": "farming plowing fields scene",
                    "赌博": "gambling scene",
                    "战争": "Chinese civil war scene",
                    "饥荒": "famine hardship scene",
                    "告别": "sad farewell scene"
                }
            },
            "哈利波特": {
                "characters": {
                    "哈利": "Harry Potter wizard boy",
                    "赫敏": "Hermione Granger",
                    "罗恩": "Ron Weasley",
                    "邓布利多": "Dumbledore wizard",
                    "伏地魔": "dark wizard Voldemort"
                },
                "locations": {
                    "霍格沃茨": "Hogwarts castle",
                    "魔法部": "Ministry of Magic",
                    "对角巷": "Diagon Alley",
                    "禁林": "Forbidden Forest",
                    "魁地奇球场": "Quidditch stadium"
                },
                "scenes": {
                    "魔法课": "magic lesson classroom",
                    "魁地奇": "Quidditch match flying",
                    "决斗": "wizard duel magic battle",
                    "魔法仪式": "magical ritual ceremony"
                }
            }
        }
        
        # Emotional context mapping
        self.emotion_scenes = {
            "happy": "bright joyful scene",
            "sad": "melancholic touching scene", 
            "angry": "intense dramatic scene",
            "peaceful": "serene calm landscape",
            "exciting": "dynamic action scene",
            "mysterious": "mysterious atmospheric scene",
            "romantic": "romantic intimate scene"
        }
        
        # Action and relationship keywords
        self.action_keywords = {
            "遇见": "meeting encounter scene",
            "告别": "farewell goodbye scene", 
            "战斗": "battle fighting scene",
            "旅行": "journey travel scene",
            "学习": "learning studying scene",
            "工作": "working labor scene",
            "玩耍": "playing fun scene",
            "思考": "contemplation reflection scene",
            "哭泣": "crying emotional scene",
            "笑容": "smiling happy scene",
            "拥抱": "hugging embrace scene",
            "对话": "conversation talking scene"
        }
        
    def analyze_content_context(self, text: str, book_title: str = "") -> Dict:
        """
        Analyze text content to extract specific visual context
        Returns detailed scene description for image generation
        """
        cleaned_text = self._clean_text(text)
        
        # Detect book type
        book_type = self._detect_book_type(book_title)
        
        # Extract specific elements
        characters = self._extract_characters(cleaned_text, book_type)
        locations = self._extract_locations(cleaned_text, book_type)
        scenes = self._extract_scenes(cleaned_text, book_type)
        actions = self._extract_actions(cleaned_text)
        emotions = self._extract_emotions(cleaned_text)
        
        # Create contextual scene description
        scene_description = self._create_contextual_scene(
            characters, locations, scenes, actions, emotions, book_type
        )
        
        # Determine scene importance (to avoid frequent switching)
        importance = self._calculate_scene_importance(
            characters, locations, scenes, actions
        )
        
        return {
            "scene_description": scene_description,
            "characters": characters,
            "locations": locations, 
            "scenes": scenes,
            "actions": actions,
            "emotions": emotions,
            "book_type": book_type,
            "importance": importance,
            "text_excerpt": cleaned_text[:150] + "..." if len(cleaned_text) > 150 else cleaned_text
        }
    
    def _detect_book_type(self, book_title: str) -> str:
        """Detect which book this content is about"""
        title_lower = book_title.lower()
        
        if "小王子" in book_title or "little prince" in title_lower:
            return "小王子"
        elif "活着" in book_title:
            return "活着"
        elif "哈利波特" in book_title or "harry potter" in title_lower:
            return "哈利波特"
        else:
            return "通用"
    
    def _extract_characters(self, text: str, book_type: str) -> List[Dict]:
        """Extract character mentions from text"""
        characters = []
        
        if book_type in self.book_scenes:
            for char_name, char_desc in self.book_scenes[book_type]["characters"].items():
                if char_name in text:
                    characters.append({
                        "name": char_name,
                        "description": char_desc,
                        "confidence": self._calculate_confidence(text, char_name)
                    })
        
        return sorted(characters, key=lambda x: x["confidence"], reverse=True)
    
    def _extract_locations(self, text: str, book_type: str) -> List[Dict]:
        """Extract location mentions from text"""
        locations = []
        
        if book_type in self.book_scenes:
            for loc_name, loc_desc in self.book_scenes[book_type]["locations"].items():
                if loc_name in text:
                    locations.append({
                        "name": loc_name,
                        "description": loc_desc,
                        "confidence": self._calculate_confidence(text, loc_name)
                    })
        
        return sorted(locations, key=lambda x: x["confidence"], reverse=True)
    
    def _extract_scenes(self, text: str, book_type: str) -> List[Dict]:
        """Extract scene mentions from text"""
        scenes = []
        
        if book_type in self.book_scenes:
            for scene_name, scene_desc in self.book_scenes[book_type]["scenes"].items():
                if scene_name in text:
                    scenes.append({
                        "name": scene_name,
                        "description": scene_desc,
                        "confidence": self._calculate_confidence(text, scene_name)
                    })
        
        return sorted(scenes, key=lambda x: x["confidence"], reverse=True)
    
    def _extract_actions(self, text: str) -> List[Dict]:
        """Extract action words that suggest visual scenes"""
        actions = []
        
        for action, action_desc in self.action_keywords.items():
            if action in text:
                actions.append({
                    "name": action,
                    "description": action_desc,
                    "confidence": self._calculate_confidence(text, action)
                })
        
        return sorted(actions, key=lambda x: x["confidence"], reverse=True)
    
    def _extract_emotions(self, text: str) -> List[str]:
        """Extract emotional context from text"""
        emotions = []
        
        # Detect emotion markers from text
        emotion_patterns = {
            "happy": ["开心", "快乐", "高兴", "愉快", "欢笑", "微笑"],
            "sad": ["悲伤", "伤心", "难过", "哭泣", "眼泪", "痛苦"],
            "angry": ["愤怒", "生气", "愤怒", "怒火", "恼怒"],
            "peaceful": ["平静", "安静", "宁静", "祥和", "安详"],
            "exciting": ["兴奋", "激动", "刺激", "紧张", "激烈"],
            "mysterious": ["神秘", "奇怪", "诡异", "不解", "疑惑"],
            "romantic": ["浪漫", "爱情", "温柔", "甜蜜", "亲吻"]
        }
        
        for emotion, keywords in emotion_patterns.items():
            for keyword in keywords:
                if keyword in text:
                    emotions.append(emotion)
                    break
        
        return list(set(emotions))  # Remove duplicates
    
    def _create_contextual_scene(self, characters: List[Dict], locations: List[Dict], 
                                scenes: List[Dict], actions: List[Dict], 
                                emotions: List[str], book_type: str) -> str:
        """Create a detailed scene description for image generation"""
        
        scene_parts = []
        
        # Add book-specific context
        if book_type == "小王子":
            scene_parts.append("Little Prince story scene")
        elif book_type == "活着":
            scene_parts.append("Chinese historical rural scene")
        elif book_type == "哈利波特":
            scene_parts.append("Harry Potter magical world scene")
        
        # Add main characters (max 2 to avoid overcrowding)
        if characters:
            main_chars = [char["description"] for char in characters[:2]]
            scene_parts.append(f"featuring {', '.join(main_chars)}")
        
        # Add primary location
        if locations:
            scene_parts.append(f"in {locations[0]['description']}")
        
        # Add main scene type
        if scenes:
            scene_parts.append(f"showing {scenes[0]['description']}")
        elif actions:
            scene_parts.append(f"depicting {actions[0]['description']}")
        
        # Add emotional context
        if emotions:
            emotion_desc = self.emotion_scenes.get(emotions[0], "atmospheric scene")
            scene_parts.append(f"with {emotion_desc} mood")
        
        # Combine parts
        if scene_parts:
            base_description = ", ".join(scene_parts)
        else:
            base_description = "beautiful atmospheric scene"
        
        # Add style modifiers
        style_parts = [
            "cinematic composition",
            "beautiful lighting", 
            "high quality",
            "detailed artwork"
        ]
        
        full_description = f"{base_description}, {', '.join(style_parts)}"
        
        return full_description
    
    def _calculate_scene_importance(self, characters: List[Dict], locations: List[Dict],
                                  scenes: List[Dict], actions: List[Dict]) -> float:
        """
        Calculate importance score to determine if scene change is warranted
        Higher score = more important scene change
        """
        importance = 0.0
        
        # Character importance
        if characters:
            importance += sum(char["confidence"] for char in characters) * 0.4
        
        # Location importance  
        if locations:
            importance += sum(loc["confidence"] for loc in locations) * 0.3
        
        # Scene importance
        if scenes:
            importance += sum(scene["confidence"] for scene in scenes) * 0.2
            
        # Action importance
        if actions:
            importance += sum(action["confidence"] for action in actions) * 0.1
        
        return min(importance, 1.0)  # Cap at 1.0
    
    def _calculate_confidence(self, text: str, keyword: str) -> float:
        """Calculate confidence score for keyword detection"""
        count = text.count(keyword)
        text_length = len(text)
        
        # Base confidence from occurrence frequency
        frequency_score = min(count / 3.0, 1.0)  # Max 1.0 for 3+ occurrences
        
        # Bonus for keyword density
        density_score = min((count * len(keyword)) / text_length * 10, 0.5)
        
        return frequency_score + density_score
    
    def _clean_text(self, text: str) -> str:
        """Clean text by removing formatting and emotion tags"""
        # Remove emotion tags
        cleaned = re.sub(r'<speak\s+emotion=["\'][^"\']*["\']>(.*?)</speak>', r'\1', text)
        cleaned = re.sub(r'<[^>]+>', '', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned

class IntelligentBackgroundPlanner:
    """
    Plan background changes based on content analysis with intelligent timing
    to avoid frequent switching
    """
    
    def __init__(self, min_duration: float = 15.0, importance_threshold: float = 0.6):
        self.min_duration = min_duration  # Minimum seconds between changes
        self.importance_threshold = importance_threshold  # Minimum importance to trigger change
        self.theme_analyzer = ContextualThemeAnalyzer()
        
    def plan_contextual_backgrounds(self, alignment_data: List[Dict], 
                                  book_metadata: Dict) -> List[Dict]:
        """
        Plan background changes based on content context with intelligent timing
        """
        segments = []
        current_time = 0.0
        current_theme = None
        last_change_time = 0.0
        
        book_title = book_metadata.get('title', '')
        
        print(f"🧠 Planning contextual backgrounds for {book_title}")
        print(f"📏 Minimum duration between changes: {self.min_duration}s")
        print(f"🎯 Importance threshold: {self.importance_threshold}")
        
        # Group alignment data into potential segments
        text_segments = self._group_text_segments(alignment_data)
        
        for i, text_segment in enumerate(text_segments):
            segment_text = text_segment['text']
            segment_start = text_segment['start']
            segment_end = text_segment['end']
            
            # Analyze content context
            context_analysis = self.theme_analyzer.analyze_content_context(
                segment_text, book_title
            )
            
            # Determine if theme change is warranted
            time_since_last_change = segment_start - last_change_time
            should_change = self._should_change_background(
                context_analysis, current_theme, time_since_last_change
            )
            
            if should_change:
                # Create new background segment
                segment = {
                    "index": len(segments),
                    "start_time": segment_start,
                    "end_time": segment_end,
                    "duration": segment_end - segment_start,
                    "context_analysis": context_analysis,
                    "scene_description": context_analysis["scene_description"],
                    "importance": context_analysis["importance"],
                    "characters": context_analysis.get("characters", []),
                    "locations": context_analysis.get("locations", []),
                    "relevant_text": segment_text[:200] + "..." if len(segment_text) > 200 else segment_text
                }
                
                segments.append(segment)
                current_theme = context_analysis
                last_change_time = segment_start
                
                print(f"  🎬 Segment {len(segments)}: {segment_start:.1f}s-{segment_end:.1f}s")
                print(f"     Importance: {context_analysis['importance']:.2f}")
                print(f"     Scene: {context_analysis['scene_description'][:80]}...")
                
                if context_analysis['characters']:
                    chars = [c['name'] for c in context_analysis['characters'][:2]]
                    print(f"     Characters: {', '.join(chars)}")
                
                if context_analysis['locations']:
                    locs = [l['name'] for l in context_analysis['locations'][:2]]
                    print(f"     Locations: {', '.join(locs)}")
            
            elif segments:
                # Extend current segment
                segments[-1]["end_time"] = segment_end
                segments[-1]["duration"] = segment_end - segments[-1]["start_time"]
        
        print(f"✅ Planned {len(segments)} contextual background segments")
        return segments
    
    def _group_text_segments(self, alignment_data: List[Dict]) -> List[Dict]:
        """Group alignment data into meaningful text segments"""
        if not alignment_data:
            return []
        
        segments = []
        current_segment = {
            "text": "",
            "start": alignment_data[0]['start'] / 1000.0,
            "end": alignment_data[0]['end'] / 1000.0
        }
        
        for item in alignment_data:
            start_time = item['start'] / 1000.0
            end_time = item['end'] / 1000.0
            text = item['text'].strip()
            
            # Add to current segment
            if current_segment["text"]:
                current_segment["text"] += " " + text
            else:
                current_segment["text"] = text
            current_segment["end"] = end_time
            
            # Check if we should start a new segment
            # (based on sentence endings or length)
            if (text.endswith(('。', '！', '？', '.', '!', '?')) and 
                len(current_segment["text"]) > 50):
                
                segments.append(current_segment.copy())
                current_segment = {
                    "text": "",
                    "start": end_time,
                    "end": end_time
                }
        
        # Add final segment if it has content
        if current_segment["text"].strip():
            segments.append(current_segment)
        
        return segments
    
    def _should_change_background(self, new_context: Dict, current_theme: Dict, 
                                time_since_last: float) -> bool:
        """Determine if background should change based on context and timing"""
        
        # Always change for first segment
        if current_theme is None:
            return True
        
        # Don't change if not enough time has passed (unless very important)
        if time_since_last < self.min_duration:
            if new_context["importance"] < 0.9:  # Very high importance can override timing
                return False
        
        # Don't change if importance is too low
        if new_context["importance"] < self.importance_threshold:
            return False
        
        # Check if context is significantly different
        if self._is_context_different(new_context, current_theme):
            return True
        
        return False
    
    def _is_context_different(self, new_context: Dict, current_context: Dict) -> bool:
        """Check if two contexts are significantly different"""
        
        # Compare characters
        new_chars = set(char["name"] for char in new_context.get("characters", []))
        current_chars = set(char["name"] for char in current_context.get("characters", []))
        
        if new_chars and current_chars:
            char_overlap = len(new_chars & current_chars) / len(new_chars | current_chars)
            if char_overlap < 0.5:  # Less than 50% overlap
                return True
        
        # Compare locations
        new_locs = set(loc["name"] for loc in new_context.get("locations", []))
        current_locs = set(loc["name"] for loc in current_context.get("locations", []))
        
        if new_locs and current_locs:
            loc_overlap = len(new_locs & current_locs) / len(new_locs | current_locs)
            if loc_overlap < 0.5:  # Less than 50% overlap
                return True
        
        # Compare emotions
        new_emotions = set(new_context.get("emotions", []))
        current_emotions = set(current_context.get("emotions", []))
        
        if new_emotions and current_emotions:
            emotion_overlap = len(new_emotions & current_emotions) / len(new_emotions | current_emotions)
            if emotion_overlap < 0.3:  # Less than 30% overlap
                return True
        
        # If we have new characters or locations that weren't present before
        if (new_chars and not current_chars) or (new_locs and not current_locs):
            return True
        
        return False