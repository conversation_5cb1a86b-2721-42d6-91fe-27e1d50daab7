"""
Deep Research Agent
深度研究智能体

基于通用研究模板系统的核心研究Agent，负责执行深度研究任务
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging
from pathlib import Path

# 导入基础组件
try:
    from universal_research_template import (
        UniversalResearchPromptTemplate, 
        ResearchRequest, 
        ResearchScenario
    )
    # 导入现有组件
    from concept_expander import ConceptExpander, SemanticContext
    from doubao_search_enhancer import DoubaoSearchEnhancer
    from enhanced_script_generator import EnhancedScriptGenerator
    from config import Config
except ImportError:
    # 模拟组件，用于测试
    print("⚠️  部分组件未找到，使用模拟组件")
    
    class ConceptExpander:
        async def expand_user_input(self, text):
            from dataclasses import dataclass
            @dataclass
            class MockSemanticContext:
                domain = "general"
                content_type = "analysis"
                intent = "research"
                expanded_concepts = []
            return MockSemanticContext()
    
    class DoubaoSearchEnhancer:
        async def enhance_prompt_with_search(self, prompt, context=None):
            return prompt, {"search_results": []}
    
    class EnhancedScriptGenerator:
        async def generate_from_prompt(self, prompt, **kwargs):
            return f"模拟生成的内容基于提示词: {prompt[:100]}..."
        
        async def _call_doubao_api(self, prompt):
            return f"模拟API响应: {prompt[:100]}..."
    
    class Config:
        pass

@dataclass
class ResearchResult:
    """研究结果数据结构"""
    scenario: ResearchScenario
    target: str
    content: str
    quality_score: float
    metadata: Dict[str, Any]
    sources: List[str]
    timestamp: datetime
    processing_time: float
    
@dataclass
class QualityMetrics:
    """质量指标"""
    completeness: float  # 完整性
    accuracy: float      # 准确性
    depth: float         # 深度
    coherence: float     # 连贯性
    originality: float   # 原创性
    overall_score: float # 总体评分
    
@dataclass
class ResearchContext:
    """研究上下文"""
    expanded_concepts: List[str]
    semantic_context: Any  # SemanticContext
    search_results: List[Dict]
    quality_requirements: Dict[str, float]
    
class ResearchQualityAssessor:
    """研究质量评估器"""
    
    def __init__(self):
        self.quality_thresholds = {
            "minimum_acceptable": 0.7,
            "good_quality": 0.8,
            "excellent_quality": 0.9
        }
    
    def assess_research_quality(self, content: str, metadata: Dict) -> QualityMetrics:
        """评估研究质量"""
        
        # 完整性评估
        completeness = self._assess_completeness(content, metadata)
        
        # 准确性评估
        accuracy = self._assess_accuracy(content, metadata)
        
        # 深度评估
        depth = self._assess_depth(content, metadata)
        
        # 连贯性评估
        coherence = self._assess_coherence(content)
        
        # 原创性评估
        originality = self._assess_originality(content, metadata)
        
        # 计算总体评分
        overall_score = self._calculate_overall_score(
            completeness, accuracy, depth, coherence, originality
        )
        
        return QualityMetrics(
            completeness=completeness,
            accuracy=accuracy,
            depth=depth,
            coherence=coherence,
            originality=originality,
            overall_score=overall_score
        )
    
    def _assess_completeness(self, content: str, metadata: Dict) -> float:
        """评估完整性"""
        # 检查是否包含所有必需的部分
        required_sections = metadata.get("required_sections", [])
        if not required_sections:
            return 0.8  # 默认分数
        
        found_sections = 0
        for section in required_sections:
            if section in content:
                found_sections += 1
        
        return found_sections / len(required_sections)
    
    def _assess_accuracy(self, content: str, metadata: Dict) -> float:
        """评估准确性"""
        # 检查引用数量和质量
        web_citations = content.count("[web:")
        post_citations = content.count("[post:")
        
        # 基本引用分数
        citation_score = min(1.0, (web_citations + post_citations) / 10)
        
        # 内容长度与质量关系
        content_length = len(content)
        if content_length < 500:
            length_score = 0.5
        elif content_length < 2000:
            length_score = 0.7
        elif content_length < 5000:
            length_score = 0.9
        else:
            length_score = 1.0
        
        return (citation_score + length_score) / 2
    
    def _assess_depth(self, content: str, metadata: Dict) -> float:
        """评估深度"""
        # 检查分析深度指标
        depth_indicators = [
            "分析", "深入", "详细", "探讨", "研究", "调查",
            "原因", "影响", "趋势", "预测", "建议", "策略"
        ]
        
        depth_score = 0
        for indicator in depth_indicators:
            depth_score += content.count(indicator) * 0.1
        
        return min(1.0, depth_score / 10)
    
    def _assess_coherence(self, content: str) -> float:
        """评估连贯性"""
        # 检查逻辑连接词
        coherence_markers = [
            "因此", "所以", "由于", "然而", "但是", "同时",
            "另外", "此外", "首先", "其次", "最后", "总之"
        ]
        
        coherence_score = 0
        for marker in coherence_markers:
            coherence_score += content.count(marker) * 0.1
        
        # 检查段落结构
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 3:
            coherence_score += 0.2
        
        return min(1.0, coherence_score / 5)
    
    def _assess_originality(self, content: str, metadata: Dict) -> float:
        """评估原创性"""
        # 检查模板化内容
        template_phrases = [
            "请根据", "以下是", "总结如下", "根据以上分析",
            "综上所述", "基于上述", "通过以上"
        ]
        
        template_score = 0
        for phrase in template_phrases:
            template_score += content.count(phrase) * 0.1
        
        # 原创性 = 1 - 模板化程度
        originality = max(0.3, 1.0 - template_score / 3)
        
        return originality
    
    def _calculate_overall_score(self, completeness: float, accuracy: float, 
                               depth: float, coherence: float, originality: float) -> float:
        """计算总体评分"""
        weights = {
            "completeness": 0.3,
            "accuracy": 0.25,
            "depth": 0.2,
            "coherence": 0.15,
            "originality": 0.1
        }
        
        overall_score = (
            completeness * weights["completeness"] +
            accuracy * weights["accuracy"] +
            depth * weights["depth"] +
            coherence * weights["coherence"] +
            originality * weights["originality"]
        )
        
        return overall_score

class DeepResearchAgent:
    """深度研究智能体"""
    
    def __init__(self):
        self.agent_id = "deep_research_agent"
        self.agent_type = "deep_research"
        
        # 核心组件
        self.template_system = UniversalResearchPromptTemplate()
        self.concept_expander = ConceptExpander()
        self.search_enhancer = DoubaoSearchEnhancer()
        self.script_generator = EnhancedScriptGenerator()
        self.quality_assessor = ResearchQualityAssessor()
        
        # 配置
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        
        # 性能指标
        self.performance_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "average_quality_score": 0.0,
            "average_processing_time": 0.0
        }
    
    async def conduct_deep_research(self, request: ResearchRequest) -> ResearchResult:
        """执行深度研究"""
        start_time = datetime.now()
        
        try:
            # 1. 验证请求
            if not self.template_system.validate_request(request):
                raise ValueError("Invalid research request")
            
            # 2. 生成专用提示词
            research_prompt = self.template_system.generate_research_prompt(request)
            
            # 3. 概念扩展和语义分析
            research_context = await self._build_research_context(request)
            
            # 4. 执行深度研究
            research_content = await self._execute_research(
                research_prompt, research_context, request
            )
            
            # 5. 质量评估
            quality_metrics = self.quality_assessor.assess_research_quality(
                research_content, {"required_sections": self._get_required_sections(request)}
            )
            
            # 6. 质量优化
            if quality_metrics.overall_score < 0.8:
                research_content = await self._enhance_research_quality(
                    research_content, quality_metrics, research_context
                )
                
                # 重新评估
                quality_metrics = self.quality_assessor.assess_research_quality(
                    research_content, {"required_sections": self._get_required_sections(request)}
                )
            
            # 7. 构建结果
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ResearchResult(
                scenario=request.scenario,
                target=request.target,
                content=research_content,
                quality_score=quality_metrics.overall_score,
                metadata={
                    "quality_metrics": asdict(quality_metrics),
                    "prompt_length": len(research_prompt),
                    "concept_count": len(research_context.expanded_concepts),
                    "source_count": len(research_context.search_results)
                },
                sources=self._extract_sources(research_content),
                timestamp=datetime.now(),
                processing_time=processing_time
            )
            
            # 8. 更新性能指标
            self._update_performance_metrics(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Research failed: {str(e)}")
            raise
    
    async def _build_research_context(self, request: ResearchRequest) -> ResearchContext:
        """构建研究上下文"""
        
        # 概念扩展
        semantic_context = await self.concept_expander.expand_user_input(request.target)
        
        # 提取扩展概念
        expanded_concepts = []
        for concept in semantic_context.expanded_concepts:
            expanded_concepts.append(concept.original_concept)
            expanded_concepts.extend(concept.synonyms)
            expanded_concepts.extend(concept.related_concepts)
        
        # 搜索增强
        search_results = []
        try:
            enhanced_prompt, search_data = await self.search_enhancer.enhance_prompt_with_search(
                request.target, semantic_context
            )
            search_results = search_data.get("search_results", [])
        except Exception as e:
            self.logger.warning(f"Search enhancement failed: {str(e)}")
        
        # 质量要求
        quality_requirements = {
            "minimum_length": 2000,
            "citation_count": 8,
            "section_count": 6,
            "depth_score": 0.8
        }
        
        return ResearchContext(
            expanded_concepts=expanded_concepts,
            semantic_context=semantic_context,
            search_results=search_results,
            quality_requirements=quality_requirements
        )
    
    async def _execute_research(self, prompt: str, context: ResearchContext, 
                              request: ResearchRequest) -> str:
        """执行研究"""
        
        # 增强提示词
        enhanced_prompt = self._enhance_prompt_with_context(prompt, context)
        
        # 调用内容生成
        try:
            # 使用增强脚本生成器
            research_content = await self.script_generator.generate_from_prompt(
                enhanced_prompt,
                use_web_data=True,
                semantic_context=context.semantic_context
            )
            
            return research_content
            
        except Exception as e:
            self.logger.error(f"Content generation failed: {str(e)}")
            # 回退到基本生成
            return await self._fallback_research_generation(prompt, context)
    
    def _enhance_prompt_with_context(self, prompt: str, context: ResearchContext) -> str:
        """使用上下文增强提示词"""
        
        # 添加概念扩展信息
        concept_enhancement = f"""
## 概念扩展信息
基于语义分析，以下概念与研究主题相关：
- 核心概念: {', '.join(context.expanded_concepts[:5])}
- 相关领域: {context.semantic_context.domain}
- 内容类型: {context.semantic_context.content_type}
- 用户意图: {context.semantic_context.intent}

请在研究中充分利用这些概念扩展，深入挖掘相关联的信息和观点。
"""
        
        # 添加搜索结果信息
        search_enhancement = ""
        if context.search_results:
            search_enhancement = f"""
## 搜索增强信息
基于web搜索，发现以下相关信息：
{self._format_search_results(context.search_results[:3])}

请将这些信息整合到研究分析中，但要进行事实验证和批判性思考。
"""
        
        # 组合增强后的提示词
        enhanced_prompt = f"{prompt}\n{concept_enhancement}\n{search_enhancement}"
        
        return enhanced_prompt
    
    def _format_search_results(self, search_results: List[Dict]) -> str:
        """格式化搜索结果"""
        formatted = ""
        for i, result in enumerate(search_results, 1):
            formatted += f"{i}. {result.get('title', '未知标题')}\n"
            formatted += f"   来源: {result.get('source', '未知来源')}\n"
            formatted += f"   摘要: {result.get('summary', '无摘要')[:200]}...\n\n"
        
        return formatted
    
    async def _fallback_research_generation(self, prompt: str, context: ResearchContext) -> str:
        """回退研究生成"""
        
        # 使用基本的文本生成
        fallback_prompt = f"""
请根据以下研究要求生成详细的分析报告：

{prompt}

请确保：
1. 内容结构清晰，逻辑连贯
2. 分析深入，观点明确
3. 引用可靠的信息来源
4. 语言准确，表达流畅
5. 篇幅充实，不少于2000字

开始生成报告：
"""
        
        try:
            # 调用基本生成器
            return await self.script_generator._call_doubao_api(fallback_prompt)
        except Exception as e:
            self.logger.error(f"Fallback generation failed: {str(e)}")
            return self._generate_minimal_fallback(prompt)
    
    def _generate_minimal_fallback(self, prompt: str) -> str:
        """生成最小回退内容"""
        return f"""
# 研究报告生成失败

由于技术问题，无法生成完整的研究报告。

## 原始研究要求
{prompt[:500]}...

## 建议
1. 请检查网络连接
2. 稍后重试研究请求
3. 联系技术支持

## 错误信息
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
状态: 生成失败，已启用最小回退机制
"""
    
    async def _enhance_research_quality(self, content: str, quality_metrics: QualityMetrics, 
                                      context: ResearchContext) -> str:
        """增强研究质量"""
        
        # 识别需要改进的方面
        improvements = []
        
        if quality_metrics.completeness < 0.7:
            improvements.append("增加内容完整性")
        
        if quality_metrics.depth < 0.7:
            improvements.append("深化分析深度")
        
        if quality_metrics.coherence < 0.7:
            improvements.append("改善逻辑连贯性")
        
        if quality_metrics.originality < 0.5:
            improvements.append("提高原创性")
        
        # 生成改进提示词
        improvement_prompt = f"""
请对以下研究内容进行质量改进：

原始内容：
{content}

改进要求：
{', '.join(improvements)}

请特别注意：
1. 增加分析深度，提供更多洞察
2. 改善逻辑结构，增强连贯性
3. 添加更多原创性观点
4. 确保内容完整性

改进后的内容：
"""
        
        try:
            enhanced_content = await self.script_generator._call_doubao_api(improvement_prompt)
            return enhanced_content
        except Exception as e:
            self.logger.error(f"Quality enhancement failed: {str(e)}")
            return content  # 返回原始内容
    
    def _get_required_sections(self, request: ResearchRequest) -> List[str]:
        """获取必需的章节"""
        scenario_config = self.template_system.get_scenario_info(request.scenario)
        if not scenario_config:
            return []
        
        return [task.name for task in scenario_config.get("tasks", [])]
    
    def _extract_sources(self, content: str) -> List[str]:
        """提取引用来源"""
        sources = []
        
        # 提取web引用
        import re
        web_citations = re.findall(r'\[web:(\d+)\]', content)
        sources.extend([f"web:{cite}" for cite in web_citations])
        
        # 提取社交媒体引用
        post_citations = re.findall(r'\[post:(\d+)\]', content)
        sources.extend([f"post:{cite}" for cite in post_citations])
        
        return sources
    
    def _update_performance_metrics(self, result: ResearchResult):
        """更新性能指标"""
        self.performance_metrics["total_requests"] += 1
        self.performance_metrics["successful_requests"] += 1
        
        # 更新平均质量分数
        total_quality = (
            self.performance_metrics["average_quality_score"] * 
            (self.performance_metrics["successful_requests"] - 1) +
            result.quality_score
        )
        self.performance_metrics["average_quality_score"] = (
            total_quality / self.performance_metrics["successful_requests"]
        )
        
        # 更新平均处理时间
        total_time = (
            self.performance_metrics["average_processing_time"] * 
            (self.performance_metrics["successful_requests"] - 1) +
            result.processing_time
        )
        self.performance_metrics["average_processing_time"] = (
            total_time / self.performance_metrics["successful_requests"]
        )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    def get_supported_scenarios(self) -> List[str]:
        """获取支持的研究场景"""
        return self.template_system.list_available_scenarios()

# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def test_deep_research():
        """测试深度研究功能"""
        
        # 创建深度研究Agent
        research_agent = DeepResearchAgent()
        
        # 创建研究请求
        request = ResearchRequest(
            scenario=ResearchScenario.BOOK_ANALYSIS,
            target="Structures: Or Why Things Don't Fall Down by J.E. Gordon",
            audience="general",
            output_format="report"
        )
        
        print("🔍 开始深度研究...")
        print(f"研究对象: {request.target}")
        print(f"研究场景: {request.scenario.value}")
        
        # 执行研究
        result = await research_agent.conduct_deep_research(request)
        
        print(f"\n✅ 研究完成!")
        print(f"质量评分: {result.quality_score:.2f}")
        print(f"处理时间: {result.processing_time:.2f}秒")
        print(f"内容长度: {len(result.content)}字符")
        print(f"引用来源: {len(result.sources)}个")
        
        # 显示部分内容
        print(f"\n📄 研究内容预览:")
        print(result.content[:500] + "...")
        
        # 显示性能指标
        print(f"\n📊 性能指标:")
        metrics = research_agent.get_performance_metrics()
        for key, value in metrics.items():
            print(f"  {key}: {value}")
    
    # 运行测试
    asyncio.run(test_deep_research())