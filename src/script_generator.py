import requests
import json
import time
from typing import Dict, Tu<PERSON>
from .config import Config

class ScriptGenerator:
    def __init__(self):
        self.api_key = Config.OPENROUTER_API_KEY
        self.base_url = Config.OPENROUTER_BASE_URL
        
    def generate_book_review(self, book_title: str, author: str) -> Tuple[str, str]:
        """
        Generate a Chinese book review script and illustration prompt using OpenRouter Gemini 2.5 Pro
        Returns: (review_script, illustration_prompt)
        """
        
        prompt = f"""
        专业书籍推荐解读
        你是一位专业的书评人，请根据用户提供的书籍名称《{book_title}》（作者：{author}），创作一份结构完整、逻辑清晰的书籍推荐内容。
        
        ## 内容结构要求：
        1. **书籍介绍**（50-60字）：
           - 首先介绍这本书的基本信息和主要内容
           - 说明作品的文学类型、创作背景或时代意义
           - 简要概括故事主线或核心主题
        
        2. **深度评析**（120-140字）：
           - 深入分析作品的核心价值和精神内核
           - 阐述书中的重要观点、哲理或人生智慧
           - 分析人物性格、情节发展或写作手法的精妙之处
           - 将书中智慧与现代生活相结合
        
        3. **推荐总结**（30-40字）：
           - 用温暖有力的语言总结推荐理由
           - 说明这本书能给读者带来什么启发或收获

        ## 语言风格要求：
        - 语言温暖有力，富有感染力和亲和力
        - 善用生活化的比喻和贴近人心的表达
        - 将深刻的哲理用朴素的话语表达出来
        - 语调真诚，带有人文关怀和生活智慧
        - 语言节奏感强，适合朗读传播
        
        ## 质量要求：
        - 确保内容逻辑清晰，从介绍到评析到推荐层层递进
        - 避免泛泛而谈，要有具体的内容分析
        - 体现专业的文学素养和深度思考
        - 总字数控制在200-220字，确保朗读时长45-55秒

        ## 封面插画要求：
        请用中文为这本书设计一个富有艺术性和象征意义的封面插画描述，要求：
        - 体现作品的核心主题和精神内涵
        - 融入中国传统美学元素
        - 具有深层的象征意义和文化内涵
        - 适合转化为视觉艺术作品

        请严格按照上述结构要求，以JSON格式返回：
        {{
            "review": "按照'书籍介绍-深度评析-推荐总结'三段结构的完整书评内容",
            "illustration_prompt": "中文插画设计描述"
        }}
        """
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/yourusername/airiverse",
            "X-Title": "AiriVerse Book Review Generator"
        }
        
        data = {
            "model": "google/gemini-2.0-flash-exp:free",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一位资深的中文书评人，擅长用优美的语言总结书籍精华。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1000,
            "response_format": {"type": "json_object"}
        }
        
        max_retries = 3
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=30
                )
                
                if response.status_code == 429:
                    # Rate limited - wait and retry
                    wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                    print(f"Rate limited. Waiting {wait_time} seconds before retry {attempt + 1}/{max_retries}...")
                    time.sleep(wait_time)
                    continue
                
                response.raise_for_status()
                
                result = response.json()
                content = json.loads(result['choices'][0]['message']['content'])
                
                return content['review'], content['illustration_prompt']
                
            except json.JSONDecodeError:
                # Sometimes the response isn't valid JSON, try to extract manually
                try:
                    result = response.json()
                    raw_content = result['choices'][0]['message']['content']
                    # Try to parse it as text and create fallback
                    print(f"JSON decode error, using raw content for fallback")
                    break
                except:
                    pass
            except Exception as e:
                print(f"Error generating script (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                
        # Fallback response with 董宇辉 style
        print("Using fallback script generation with 董宇辉 style")
        fallback_review = f"""朋友们，今天和大家聊聊{author}的《{book_title}》。读完这本书，我想说，真正的好书，就像一位智慧的老友，在你迷茫时给你指路，在你疲惫时给你力量。

这本书不是高高在上的学术论文，而是贴近我们生活的人生智慧。{author}用最朴素的文字，讲述最深刻的道理。就像我们小时候听奶奶讲故事一样，每一个字都带着温度，每一句话都能触动心灵。

读书，不是为了炫耀你认识多少字，而是为了在人生的路上不那么孤单。《{book_title}》告诉我们，生活中的困顿和迷茫，都是成长路上必经的风景。

愿我们都能像书中所说的那样，保持内心的纯真与善良，在这个快速变化的世界里，做一个温暖的人。因为真正的财富，不在于你拥有多少，而在于你能给这个世界带来多少光明。"""
        
        fallback_prompt = f"水墨画风格的书籍封面设计，以《{book_title}》为主题，融入传统中国文化元素，体现{author}作品的深邃哲理。画面采用素雅的色调，运用留白的艺术手法，营造出宁静致远的意境。中央可设计象征性的图案，如古典书卷、梅兰竹菊、或抽象的文字符号，体现作品的文学性和思想深度。整体设计既要有现代美感，又要承载深厚的文化内涵"
        
        return fallback_review, fallback_prompt

    def generate_from_prompt_basic(self, prompt_text: str) -> Tuple[str, str]:
        """
        Generate script and illustration prompt from user's content prompt
        Returns: (script_content, illustration_prompt)
        """
        
        system_prompt = f"""
        你是一位专业的内容创作者，请根据用户的提示词创作高质量的视频内容。

        ## 内容结构要求：
        1. **开场介绍**（50-60字）：
           - 根据用户需求，适当引入主题
           - 营造合适的氛围和语调
        
        2. **核心内容**（120-140字）：
           - 深入展开用户要求的主题内容
           - 提供有价值的信息、观点或分析
           - 保持逻辑清晰、内容丰富
        
        3. **总结收尾**（30-40字）：
           - 简洁有力地总结要点
           - 给观众留下深刻印象

        ## 语言风格要求：
        - 语言自然流畅，适合视频旁白
        - 根据内容类型调整语调（正式/轻松/专业）
        - 语言富有感染力和表现力
        - 总字数控制在200-220字

        ## 响应格式：
        请以JSON格式返回：
        {{
            "script": "生成的脚本内容",
            "illustration_prompt": "对应的视觉描述提示词"
        }}
        """
        
        user_prompt = f"用户需求：{prompt_text}"
        
        try:
            # Call OpenRouter API
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': 'google/gemini-2.0-flash-exp:free',
                'messages': [
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': user_prompt}
                ],
                'temperature': 0.7,
                'max_tokens': 1500
            }
            
            response = requests.post(f'{self.base_url}/chat/completions', headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                content = json.loads(result['choices'][0]['message']['content'])
                return content['script'], content['illustration_prompt']
            else:
                print(f"API request failed: {response.status_code}")
                
        except Exception as e:
            print(f"Error generating script from prompt: {e}")
        
        # Fallback response
        print("Using fallback script generation from prompt")
        fallback_script = f"""朋友们，今天我们来聊聊一个有趣的话题。{prompt_text[:50]}{'...' if len(prompt_text) > 50 else ''}

在这个快速变化的时代，我们每个人都需要不断学习和思考。正如这个话题所展现的，生活中的智慧往往隐藏在最平凡的事物中，等待着我们去发现和理解。

让我们用开放的心态，去感受生活的美好，去思考那些看似简单却蕴含深意的道理。因为真正的成长，就在这样的思考和体验中悄然发生。"""
        
        fallback_prompt = "现代简约风格的设计，温暖的色调，体现内容的核心主题，适合作为视频背景"
        
        return fallback_script, fallback_prompt