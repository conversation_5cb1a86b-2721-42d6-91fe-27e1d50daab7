"""
概念扩展器 - 对用户输入进行同义词、概念深度和广度扩展
实现智能的语义扩展和相关概念挖掘
"""

import requests
import json
import re
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from .config import Config

@dataclass
class ConceptExpansion:
    """概念扩展结果"""
    original_concept: str
    synonyms: List[str]           # 同义词
    related_concepts: List[str]   # 相关概念  
    broader_concepts: List[str]   # 更广泛的概念
    narrower_concepts: List[str]  # 更具体的概念
    associated_topics: List[str]  # 关联主题
    expansion_score: float        # 扩展质量评分

@dataclass
class SemanticContext:
    """语义上下文"""
    domain: str                   # 领域类型
    intent: str                   # 用户意图
    content_type: str            # 内容类型
    key_entities: List[str]      # 关键实体
    expanded_concepts: List[ConceptExpansion]
    search_queries: List[str]    # 生成的搜索查询

class ConceptExpander:
    """概念扩展器 - 智能语义扩展和概念挖掘"""
    
    def __init__(self):
        self.api_key = Config.OPENROUTER_API_KEY
        self.base_url = Config.OPENROUTER_BASE_URL
        
        # 预定义的概念关系映射
        self.concept_mappings = self._initialize_concept_mappings()
        
        # 领域特定的扩展规则
        self.domain_rules = self._initialize_domain_rules()
    
    def expand_user_input(self, user_input: str) -> SemanticContext:
        """
        对用户输入进行全面的概念扩展和语义分析
        """
        print(f"🔍 开始对用户输入进行概念扩展: {user_input[:100]}...")
        
        # 1. 基础分析
        domain = self._detect_domain(user_input)
        intent = self._detect_intent(user_input)
        content_type = self._detect_content_type(user_input)
        
        # 2. 提取关键实体
        key_entities = self._extract_key_entities(user_input)
        
        # 3. 对每个关键实体进行概念扩展
        expanded_concepts = []
        for entity in key_entities:
            expansion = self._expand_single_concept(entity, domain, user_input)
            if expansion:
                expanded_concepts.append(expansion)
        
        # 4. 生成智能搜索查询
        search_queries = self._generate_search_queries(user_input, expanded_concepts, domain)
        
        context = SemanticContext(
            domain=domain,
            intent=intent,
            content_type=content_type,
            key_entities=key_entities,
            expanded_concepts=expanded_concepts,
            search_queries=search_queries
        )
        
        print(f"✅ 概念扩展完成 - 领域: {domain}, 意图: {intent}, 实体数: {len(key_entities)}")
        return context
    
    def _detect_domain(self, user_input: str) -> str:
        """检测内容领域"""
        content_lower = user_input.lower()
        
        # 文学领域
        if any(keyword in content_lower for keyword in 
               ['书评', '文学', '小说', '诗歌', '散文', '作家', '作品', '阅读', '《', '》']):
            return "文学"
        
        # 科技领域
        elif any(keyword in content_lower for keyword in 
                ['科技', 'ai', '人工智能', '技术', '软件', '算法', '编程', '数字化', '互联网']):
            return "科技"
        
        # 商业领域
        elif any(keyword in content_lower for keyword in 
                ['商业', '经济', '管理', '市场', '营销', '品牌', '企业', '投资', '财务']):
            return "商业"
        
        # 教育领域
        elif any(keyword in content_lower for keyword in 
                ['教育', '学习', '培训', '课程', '知识', '技能', '教学', '学校']):
            return "教育"
        
        # 生活领域
        elif any(keyword in content_lower for keyword in 
                ['生活', '健康', '美食', '旅行', '运动', '娱乐', '时尚', '文化']):
            return "生活"
        
        # 产品领域
        elif any(keyword in content_lower for keyword in 
                ['产品', '功能', '特点', '使用', '体验', '评测', '推荐', '介绍']):
            return "产品"
        
        else:
            return "通用"
    
    def _detect_intent(self, user_input: str) -> str:
        """检测用户意图"""
        content_lower = user_input.lower()
        
        if any(keyword in content_lower for keyword in ['评价', '评论', '分析', '解读']):
            return "评论分析"
        elif any(keyword in content_lower for keyword in ['介绍', '说明', '讲解', '解释']):
            return "知识介绍"
        elif any(keyword in content_lower for keyword in ['推荐', '建议', '选择']):
            return "推荐建议"
        elif any(keyword in content_lower for keyword in ['教学', '教程', '学习', '培训']):
            return "教学指导"
        elif any(keyword in content_lower for keyword in ['比较', '对比', '区别']):
            return "对比分析"
        else:
            return "信息分享"
    
    def _detect_content_type(self, user_input: str) -> str:
        """检测内容类型"""
        content_lower = user_input.lower()
        
        if any(keyword in content_lower for keyword in ['深度', '详细', '全面', '深入']):
            return "深度内容"
        elif any(keyword in content_lower for keyword in ['简单', '快速', '简介', '概述']):
            return "简要内容"
        elif any(keyword in content_lower for keyword in ['专业', '技术', '学术']):
            return "专业内容"
        elif any(keyword in content_lower for keyword in ['通俗', '易懂', '大众']):
            return "通俗内容"
        else:
            return "标准内容"
    
    def _extract_key_entities(self, user_input: str) -> List[str]:
        """提取关键实体"""
        entities = []
        
        # 提取书名/作品名（中文书名号）
        book_titles = re.findall(r'《([^》]+)》', user_input)
        entities.extend(book_titles)
        
        # 提取引号内容
        quoted_content = re.findall(r'"([^"]+)"|\'([^\']+)\'|"([^"]+)"', user_input)
        for match in quoted_content:
            for item in match:
                if item:
                    entities.append(item)
        
        # 提取专有名词（英文大写词汇）
        proper_nouns = re.findall(r'\b[A-Z][a-zA-Z0-9]+\b', user_input)
        entities.extend(proper_nouns)
        
        # 提取中文关键词（使用简单的词汇识别）
        chinese_keywords = self._extract_chinese_keywords(user_input)
        entities.extend(chinese_keywords)
        
        # 去重并返回
        return list(set(entities))
    
    def _extract_chinese_keywords(self, text: str) -> List[str]:
        """提取中文关键词"""
        # 简化的中文关键词提取
        keywords = []
        
        # 预定义的重要词汇模式
        important_patterns = [
            r'[人工智能|机器学习|深度学习|神经网络]',
            r'[管理学|经济学|心理学|哲学|文学]', 
            r'[iPhone|iPad|Mac|Windows|Android]',
            r'[健康|营养|运动|饮食|睡眠]',
            r'[投资|理财|股票|基金|保险]'
        ]
        
        for pattern in important_patterns:
            matches = re.findall(pattern, text)
            keywords.extend(matches)
        
        # 提取长度为2-4的中文词汇
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,4}', text)
        
        # 过滤常见停用词
        stop_words = {'的', '了', '是', '在', '有', '和', '与', '或', '但', '而', '也', '都', '很', '更', '最'}
        chinese_words = [word for word in chinese_words if word not in stop_words]
        
        keywords.extend(chinese_words[:10])  # 限制数量
        
        return list(set(keywords))
    
    def _expand_single_concept(self, concept: str, domain: str, context: str) -> ConceptExpansion:
        """对单个概念进行扩展"""
        print(f"  🔬 扩展概念: {concept} (领域: {domain})")
        
        # 尝试使用AI进行智能扩展
        ai_expansion = self._ai_concept_expansion(concept, domain, context)
        if ai_expansion:
            return ai_expansion
        
        # 使用规则基础的扩展作为后备
        return self._rule_based_expansion(concept, domain)
    
    def _ai_concept_expansion(self, concept: str, domain: str, context: str) -> ConceptExpansion:
        """使用AI进行智能概念扩展"""
        
        system_prompt = f"""
        你是一个专业的概念扩展专家。请对给定的概念进行全面的语义扩展分析。

        ## 任务要求：
        1. 分析概念的同义词和近义词
        2. 找出相关的概念和主题
        3. 识别更广泛的上级概念
        4. 发现更具体的下级概念
        5. 挖掘关联的话题和应用场景

        ## 领域背景：
        当前领域：{domain}
        上下文：{context}

        ## 输出格式：
        请以JSON格式返回：
        {{
            "synonyms": ["同义词1", "同义词2", ...],
            "related_concepts": ["相关概念1", "相关概念2", ...],
            "broader_concepts": ["上级概念1", "上级概念2", ...],
            "narrower_concepts": ["下级概念1", "下级概念2", ...],
            "associated_topics": ["关联话题1", "关联话题2", ...]
        }}
        """
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': 'google/gemini-2.0-flash-exp:free',
                'messages': [
                    {'role': 'system', 'content': system_prompt},
                    {'role': 'user', 'content': f"请扩展这个概念：{concept}"}
                ],
                'temperature': 0.7,
                'max_tokens': 1000
            }
            
            response = requests.post(f'{self.base_url}/chat/completions', 
                                   headers=headers, json=data, timeout=20)
            
            if response.status_code == 200:
                result = response.json()
                content = json.loads(result['choices'][0]['message']['content'])
                
                return ConceptExpansion(
                    original_concept=concept,
                    synonyms=content.get('synonyms', []),
                    related_concepts=content.get('related_concepts', []),
                    broader_concepts=content.get('broader_concepts', []),
                    narrower_concepts=content.get('narrower_concepts', []),
                    associated_topics=content.get('associated_topics', []),
                    expansion_score=0.9  # 高质量AI扩展
                )
        
        except Exception as e:
            print(f"    ❌ AI概念扩展失败: {e}")
        
        return None
    
    def _rule_based_expansion(self, concept: str, domain: str) -> ConceptExpansion:
        """基于规则的概念扩展（后备方案）"""
        
        synonyms = []
        related_concepts = []
        broader_concepts = []
        narrower_concepts = []
        associated_topics = []
        
        # 使用预定义的映射
        if concept in self.concept_mappings:
            mapping = self.concept_mappings[concept]
            synonyms = mapping.get('synonyms', [])
            related_concepts = mapping.get('related', [])
            broader_concepts = mapping.get('broader', [])
            narrower_concepts = mapping.get('narrower', [])
        
        # 根据领域添加相关概念
        if domain in self.domain_rules:
            domain_concepts = self.domain_rules[domain]
            related_concepts.extend(domain_concepts.get('common_concepts', []))
            associated_topics.extend(domain_concepts.get('topics', []))
        
        return ConceptExpansion(
            original_concept=concept,
            synonyms=synonyms[:5],
            related_concepts=related_concepts[:8],
            broader_concepts=broader_concepts[:3],
            narrower_concepts=narrower_concepts[:5],
            associated_topics=associated_topics[:6],
            expansion_score=0.6  # 中等质量规则扩展
        )
    
    def _generate_search_queries(self, original_input: str, 
                               expanded_concepts: List[ConceptExpansion], 
                               domain: str) -> List[str]:
        """生成智能搜索查询"""
        
        queries = []
        
        # 1. 基于原始输入的查询
        queries.append(original_input)
        
        # 2. 基于扩展概念的查询
        for expansion in expanded_concepts[:3]:  # 取前3个最重要的概念
            # 同义词查询
            if expansion.synonyms:
                synonym_query = f"{expansion.original_concept} {' '.join(expansion.synonyms[:2])}"
                queries.append(synonym_query)
            
            # 相关概念查询
            if expansion.related_concepts:
                related_query = f"{expansion.original_concept} {' '.join(expansion.related_concepts[:2])}"
                queries.append(related_query)
            
            # 应用场景查询
            if expansion.associated_topics:
                topic_query = f"{expansion.original_concept} {expansion.associated_topics[0]}"
                queries.append(topic_query)
        
        # 3. 领域特定查询
        domain_queries = self._generate_domain_queries(original_input, domain)
        queries.extend(domain_queries)
        
        # 去重并限制数量
        unique_queries = list(dict.fromkeys(queries))  # 保持顺序的去重
        return unique_queries[:10]
    
    def _generate_domain_queries(self, original_input: str, domain: str) -> List[str]:
        """生成领域特定的搜索查询"""
        
        domain_query_templates = {
            "文学": [
                f"{original_input} 文学评论",
                f"{original_input} 作品分析", 
                f"{original_input} 阅读心得"
            ],
            "科技": [
                f"{original_input} 技术原理",
                f"{original_input} 应用场景",
                f"{original_input} 发展趋势"
            ],
            "商业": [
                f"{original_input} 商业模式",
                f"{original_input} 市场分析",
                f"{original_input} 案例研究"
            ],
            "教育": [
                f"{original_input} 学习方法",
                f"{original_input} 知识体系",
                f"{original_input} 实践应用"
            ],
            "生活": [
                f"{original_input} 生活指南",
                f"{original_input} 实用技巧",
                f"{original_input} 经验分享"
            ],
            "产品": [
                f"{original_input} 产品评测",
                f"{original_input} 使用体验",
                f"{original_input} 功能介绍"
            ]
        }
        
        return domain_query_templates.get(domain, [f"{original_input} 详细介绍"])
    
    def _initialize_concept_mappings(self) -> Dict:
        """初始化概念映射"""
        return {
            "人工智能": {
                "synonyms": ["AI", "机器智能", "智能系统"],
                "related": ["机器学习", "深度学习", "神经网络", "自然语言处理"],
                "broader": ["计算机科学", "信息技术"],
                "narrower": ["图像识别", "语音识别", "推荐系统"]
            },
            "机器学习": {
                "synonyms": ["ML", "机器学习算法"],
                "related": ["数据挖掘", "统计学习", "模式识别"],
                "broader": ["人工智能", "数据科学"],
                "narrower": ["监督学习", "无监督学习", "强化学习"]
            },
            "健康": {
                "synonyms": ["健康状态", "身体健康"],
                "related": ["营养", "运动", "睡眠", "心理健康"],
                "broader": ["生活质量", "福祉"],
                "narrower": ["身体健康", "心理健康", "社会健康"]
            }
            # 可以继续添加更多概念映射...
        }
    
    def _initialize_domain_rules(self) -> Dict:
        """初始化领域规则"""
        return {
            "文学": {
                "common_concepts": ["文学作品", "作家", "文学流派", "文学批评", "阅读"],
                "topics": ["文学欣赏", "写作技巧", "文化传承", "人文精神"]
            },
            "科技": {
                "common_concepts": ["技术创新", "数字化", "智能化", "科技发展"],
                "topics": ["技术趋势", "科技应用", "创新创业", "数字生活"]
            },
            "商业": {
                "common_concepts": ["商业模式", "市场营销", "企业管理", "经济发展"],
                "topics": ["商业策略", "市场分析", "企业文化", "投资理财"]
            },
            "教育": {
                "common_concepts": ["教学方法", "学习技巧", "知识传授", "能力培养"],
                "topics": ["教育理念", "学习方式", "技能发展", "终身学习"]
            },
            "生活": {
                "common_concepts": ["生活方式", "生活品质", "日常习惯", "生活智慧"],
                "topics": ["健康生活", "生活技巧", "人际关系", "个人成长"]
            },
            "产品": {
                "common_concepts": ["产品设计", "用户体验", "功能特性", "性能表现"],
                "topics": ["产品评测", "使用指南", "技术规格", "市场比较"]
            }
        }