import os
import json
import subprocess
from typing import List, Dict, Tuple

# Optional imports with fallback
try:
    import whisperx
    import torch
    WHISPERX_AVAILABLE = True
except ImportError:
    WHISPERX_AVAILABLE = False

try:
    from aeneas.executetask import ExecuteTask
    from aeneas.task import Task
    from aeneas.language import Language
    from aeneas.textfile import TextFileFormat
    from aeneas.syncmap import SyncMapFormat
    AENEAS_AVAILABLE = True
except ImportError:
    AENEAS_AVAILABLE = False

class AudioAligner:
    def __init__(self):
        if WHISPERX_AVAILABLE:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            self.compute_type = "float16" if self.device == "cuda" else "int8"
        else:
            self.device = "cpu"
            self.compute_type = "int8"
        
    def align_with_whisperx(self, audio_path: str, text: str, language: str = "zh") -> List[Dict]:
        """
        Use WhisperX for audio-text alignment
        Returns: List of segments with start/end timestamps
        """
        if not WHISPERX_AVAILABLE:
            return self._fallback_alignment(audio_path, text)
            
        try:
            # Load model
            model = whisperx.load_model("base", self.device, compute_type=self.compute_type)
            
            # Load audio
            audio = whisperx.load_audio(audio_path)
            
            # Transcribe
            result = model.transcribe(audio, batch_size=16)
            
            # Load alignment model
            model_a, metadata = whisperx.load_align_model(language_code=language, device=self.device)
            
            # Align
            result = whisperx.align(result["segments"], model_a, metadata, audio, self.device, return_char_alignments=False)
            
            # Format segments
            segments = []
            for seg in result["segments"]:
                segments.append({
                    "text": seg["text"],
                    "start": seg["start"] * 1000,  # Convert to milliseconds
                    "end": seg["end"] * 1000
                })
            
            return segments
            
        except Exception as e:
            print(f"WhisperX alignment error: {e}")
            return self._fallback_alignment(audio_path, text)
    
    def align_with_aeneas(self, audio_path: str, text_segments: List[str], output_path: str) -> List[Dict]:
        """
        Use aeneas for forced alignment
        Returns: List of segments with timestamps
        """
        if not AENEAS_AVAILABLE:
            return self._fallback_alignment(audio_path, ' '.join(text_segments))
            
        try:
            # Create temporary text file
            text_file = output_path.replace('.json', '.txt')
            with open(text_file, 'w', encoding='utf-8') as f:
                for segment in text_segments:
                    f.write(segment.strip() + '\n')
            
            # Create Task
            config_string = u"task_language=zho|is_text_type=plain|os_task_file_format=json"
            task = Task(config_string=config_string)
            task.audio_file_path_absolute = audio_path
            task.text_file_path_absolute = text_file
            task.sync_map_file_path_absolute = output_path
            
            # Execute
            ExecuteTask(task).execute()
            
            # Parse results
            with open(output_path, 'r', encoding='utf-8') as f:
                sync_map = json.load(f)
            
            segments = []
            for fragment in sync_map.get('fragments', []):
                segments.append({
                    "text": fragment['lines'][0] if fragment.get('lines') else '',
                    "start": float(fragment['begin']) * 1000,
                    "end": float(fragment['end']) * 1000
                })
            
            # Clean up
            os.remove(text_file)
            
            return segments
            
        except Exception as e:
            print(f"Aeneas alignment error: {e}")
            return self._fallback_alignment(audio_path, ' '.join(text_segments))
    
    def _fallback_alignment(self, audio_path: str, text: str) -> List[Dict]:
        """Simple fallback alignment based on duration estimation"""
        try:
            from pydub import AudioSegment
            
            audio = AudioSegment.from_file(audio_path)
            total_duration = len(audio)
            
            # Split text into segments
            import re
            sentences = re.split(r'[。！？；]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if not sentences:
                return [{
                    "text": text,
                    "start": 0,
                    "end": total_duration
                }]
            
            # Distribute time equally
            segment_duration = total_duration / len(sentences)
            segments = []
            
            for i, sentence in enumerate(sentences):
                segments.append({
                    "text": sentence,
                    "start": i * segment_duration,
                    "end": (i + 1) * segment_duration
                })
            
            return segments
            
        except Exception as e:
            print(f"Fallback alignment error: {e}")
            return [{
                "text": text,
                "start": 0,
                "end": 5000  # Default 5 seconds
            }]
    
    def align_sentences(self, audio_segments: List[Tuple[str, str, float]]) -> List[Dict]:
        """
        Align multiple audio segments with improved timing
        Returns: Combined alignment data
        """
        all_segments = []
        current_time = 0
        
        for text, audio_path, duration in audio_segments:
            # Get actual audio duration for more accurate timing
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(audio_path)
                actual_duration = len(audio)  # in milliseconds
            except:
                actual_duration = duration * 1000  # fallback to estimated duration
            
            # No gap at the beginning, align precisely with audio
            segment = {
                "text": text.strip(),
                "audio_path": audio_path,
                "start": current_time,
                "end": current_time + actual_duration,
                "duration": actual_duration
            }
            all_segments.append(segment)
            current_time = segment["end"]
        
        return all_segments
    
    def split_long_sentences(self, segments: List[Dict], max_duration: float = 3500, max_chars: int = 12) -> List[Dict]:
        """
        智能分割长字幕，确保字幕长度和显示时间都合适
        max_duration: 最大显示时间（毫秒）
        max_chars: 最大字符数
        """
        split_segments = []
        
        for segment in segments:
            duration = segment["end"] - segment["start"]
            text = self._clean_emotion_tags_for_subtitles(segment["text"])
            
            # 检查是否需要分割
            if duration <= max_duration and len(text) <= max_chars:
                # 长度合适，保持原样
                segment_copy = segment.copy()
                segment_copy["text"] = text
                split_segments.append(segment_copy)
                continue
            
            # 需要分割
            text_parts = self._smart_split_text(text, max_chars)
            
            if len(text_parts) == 1:
                # 无法分割，保持原样
                segment_copy = segment.copy()
                segment_copy["text"] = text
                split_segments.append(segment_copy)
                continue
            
            # 平均分配时间，确保每个片段都有合理的显示时间
            num_parts = len(text_parts)
            avg_duration = duration / num_parts
            min_duration = 1200  # 最小1.2秒
            
            # 如果平均时间太短，调整为最小时间
            if avg_duration < min_duration:
                segment_duration = min_duration
            else:
                segment_duration = avg_duration
                
            current_time = segment["start"]
            
            for i, part in enumerate(text_parts):
                if not part.strip():
                    continue
                
                # 最后一段使用剩余时间，确保不超过原始结束时间
                if i == len(text_parts) - 1:
                    end_time = segment["end"]
                else:
                    end_time = min(current_time + segment_duration, segment["end"] - min_duration)
                
                # 确保结束时间不早于开始时间
                if end_time <= current_time:
                    end_time = current_time + min_duration
                
                split_segments.append({
                    "text": part.strip(),
                    "audio_path": segment.get("audio_path", ""),
                    "start": current_time,
                    "end": end_time
                })
                
                current_time = end_time
        
        return split_segments
    
    def _clean_emotion_tags_for_subtitles(self, text: str) -> str:
        """清理字幕文本中的情感标签"""
        import re
        
        # 移除情感标签
        cleaned = re.sub(r'<speak\s+emotion=["\'][^"\']*["\']>(.*?)</speak>', r'\1', text)
        cleaned = re.sub(r'<speak[^>]*>', '', cleaned)
        cleaned = re.sub(r'</speak>', '', cleaned)
        cleaned = re.sub(r'<[^>]+>', '', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        return cleaned
    
    def _smart_split_text(self, text: str, max_chars: int) -> List[str]:
        """智能分割文本，优先在标点符号处分割"""
        if len(text) <= max_chars:
            return [text]
        
        parts = []
        
        # 首先按标点符号分割
        import re
        sentences = re.split(r'([。！？；，])', text)
        
        current_part = ""
        for i in range(0, len(sentences)-1, 2):
            sentence = sentences[i]
            punctuation = sentences[i+1] if i+1 < len(sentences) else ""
            full_sentence = sentence + punctuation
            
            # 检查加入这个句子是否会超过长度限制
            if len(current_part + full_sentence) <= max_chars:
                current_part += full_sentence
            else:
                # 超过限制
                if current_part:
                    parts.append(current_part)
                    current_part = full_sentence
                else:
                    # 单个句子就超过限制，需要强制分割
                    if len(full_sentence) > max_chars:
                        # 按字符强制分割
                        for j in range(0, len(full_sentence), max_chars):
                            part = full_sentence[j:j+max_chars]
                            if part.strip():
                                parts.append(part)
                    else:
                        current_part = full_sentence
        
        # 添加最后一部分
        if current_part:
            parts.append(current_part)
        
        # 如果还是没有分割成功，按字符强制分割
        if not parts or (len(parts) == 1 and len(parts[0]) > max_chars):
            parts = []
            for i in range(0, len(text), max_chars):
                part = text[i:i+max_chars]
                if part.strip():
                    parts.append(part)
        
        # 二次检查：确保每个部分都不超过最大字符数
        final_parts = []
        for part in parts:
            if len(part) <= max_chars:
                final_parts.append(part)
            else:
                # 再次分割超长部分
                for i in range(0, len(part), max_chars):
                    sub_part = part[i:i+max_chars]
                    if sub_part.strip():
                        final_parts.append(sub_part)
        
        return final_parts if final_parts else [text[:max_chars]]