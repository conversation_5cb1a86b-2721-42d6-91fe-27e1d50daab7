import os
import numpy as np
try:
    from moviepy import ImageClip, VideoClip, TextClip, AudioFileClip, CompositeVideoClip, CompositeAudioClip, concatenate_audioclips, vfx, afx
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Tuple
from .config import Config
from .video_config import VideoConfig, VideoConfigManager

class VideoAssembler:
    def __init__(self, video_config: VideoConfig = None):
        self.config = video_config or VideoConfig()
        self.width, self.height = VideoConfigManager.get_dimensions(self.config)
        self.fps = self.config.fps
        self.bgm_volume = self.config.bgm_volume
        self.opening_duration = self.config.opening_duration
        
    def create_page_flip_animation(self, image_path: str, duration: float) -> VideoClip:
        """Create page flip animation for opening"""
        img = ImageClip(image_path, duration=duration)
        
        # Resize to fit video dimensions
        img = img.resized((self.width, self.height))
        
        # Add fade in effect
        img = img.with_effects([vfx.FadeIn(0.5)])
        
        # Simple page flip simulation 
        img_flip = img
        
        return img_flip
    
    def _apply_flip_effect(self, image, progress):
        """Apply simple flip effect to image"""
        if progress < 0.5:
            # First half - original image fading out
            return image
        else:
            # Second half - "flipped" image fading in
            # Simple brightness adjustment to simulate flip
            arr = np.array(image)
            brightness_factor = 0.7 + 0.3 * (progress - 0.5) * 2
            return np.clip(arr * brightness_factor, 0, 255).astype(np.uint8)
    
    def _clean_emotion_tags_for_subtitles(self, text: str) -> str:
        """Remove emotion tags from text for clean subtitle display"""
        import re
        
        # Remove emotion tags like <speak emotion="happy">text</speak>
        # This handles multiple formats of emotion tags
        cleaned = re.sub(r'<speak\s+emotion=["\'][^"\']*["\']>(.*?)</speak>', r'\1', text)
        
        # Handle malformed tags like speak emotion"angry"text (missing < and >)
        cleaned = re.sub(r'speak\s+emotion["\'][^"\']*["\']([^<]*?)(?=speak\s+emotion|</speak>|$)', r'\1', cleaned)
        
        # Remove any remaining incomplete or malformed tags
        cleaned = re.sub(r'<speak[^>]*>', '', cleaned)
        cleaned = re.sub(r'</speak>', '', cleaned)
        
        # Remove any remaining "speak emotion" patterns
        cleaned = re.sub(r'speak\s+emotion["\'][^"\']*["\']', '', cleaned)
        
        # Remove other potential XML-like tags
        cleaned = re.sub(r'<[^>]+>', '', cleaned)
        
        # Clean up whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        return cleaned
    
    def _smart_wrap_text(self, text: str, font, draw, max_width: int) -> List[str]:
        """智能文本换行，考虑不同宽高比"""
        # 根据宽高比调整策略
        aspect_ratio = self.width / self.height
        
        # 竖屏 (9:16) - 更严格的限制
        if aspect_ratio < 0.6:
            # 对于12字符或更短的文本，不换行
            if len(text) <= 12:
                return [text]
        # 横屏 (16:9) - 可以显示更长的文本
        elif aspect_ratio > 1.5:
            if len(text) <= 20:
                return [text]
        # 方形 (1:1)
        else:
            if len(text) <= 15:
                return [text]
        
        # 尝试按标点符号分割
        import re
        
        # 按标点符号分割
        parts = re.split(r'([，。！？；：])', text)
        lines = []
        current_line = ""
        
        for i in range(0, len(parts)):
            part = parts[i]
            test_line = current_line + part
            
            # 检查宽度
            test_bbox = draw.textbbox((0, 0), test_line, font=font)
            test_width = test_bbox[2] - test_bbox[0]
            
            if test_width <= max_width:
                current_line = test_line
            else:
                # 超过宽度
                if current_line:
                    lines.append(current_line.strip())
                    current_line = part
                else:
                    # 单个部分就超过宽度，强制分割
                    if len(part) > 1:
                        # 按字符分割
                        for j in range(0, len(part), 6):  # 每行最多6个字符
                            char_part = part[j:j+6]
                            if char_part.strip():
                                lines.append(char_part)
                    else:
                        lines.append(part)
                    current_line = ""
        
        if current_line:
            lines.append(current_line.strip())
        
        # 清理空行
        lines = [line for line in lines if line.strip()]
        
        # 如果还是只有一行但很长，按字符强制分割
        if len(lines) == 1 and len(lines[0]) > 10:
            text = lines[0]
            lines = []
            for i in range(0, len(text), 8):  # 每行8个字符
                part = text[i:i+8]
                if part.strip():
                    lines.append(part)
        
        return lines if lines else [text]
    
    def create_subtitle_clips(self, segments: List[Dict], video_duration: float) -> List[VideoClip]:
        """Create subtitle clips from alignment data using PIL-generated images"""
        if not self.config.enable_subtitles:
            return []
            
        subtitle_clips = []
        
        for segment in segments:
            raw_text = segment['text'].strip()
            if not raw_text:
                continue
            
            # Clean emotion tags from subtitle text
            text = self._clean_emotion_tags_for_subtitles(raw_text)
            if not text:
                continue
                
            start = segment['start'] / 1000.0  # Convert to seconds
            end = segment['end'] / 1000.0
            duration = end - start
            
            # Add opening duration offset to synchronize with audio
            start += self.opening_duration
            end += self.opening_duration
            
            if duration <= 0:
                continue
            
            try:
                # Create text image using PIL for better compatibility
                txt_clip = self.create_text_image_clip(text, duration)
                if txt_clip is None:
                    continue
                
                # Position based on configuration
                position = VideoConfigManager.get_subtitle_position(
                    self.config, self.width, self.height
                )
                txt_clip = txt_clip.with_position(position)
                txt_clip = txt_clip.with_start(start).with_duration(duration)
                
                # Add fade effects if enabled
                if duration > 0.5 and self.config.enable_fade_effects:
                    txt_clip = txt_clip.with_effects([vfx.FadeIn(0.1), vfx.FadeOut(0.1)])
                
                subtitle_clips.append(txt_clip)
                
            except Exception as e:
                print(f"Error creating subtitle for '{text}': {e}")
                continue
        
        return subtitle_clips
    
    def create_text_image_clip(self, text: str, duration: float) -> VideoClip:
        """Create a text clip using PIL to generate text image"""
        try:
            # Create image with text - adjust based on aspect ratio
            aspect_ratio = self.width / self.height
            
            # 竖屏需要更多边距
            if aspect_ratio < 0.6:  # 9:16
                img_width = self.width - 160
                img_height = 150
            # 横屏可以使用更宽的区域
            elif aspect_ratio > 1.5:  # 16:9
                img_width = self.width - 200
                img_height = 120
            else:  # 1:1
                img_width = self.width - 140
                img_height = 140
            
            # Create image
            img = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Try to use a better font for Chinese
            try:
                # Try common Chinese fonts
                font_names = [
                    "/System/Library/Fonts/PingFang.ttc",  # macOS
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux
                    "simsun.ttc",  # Windows
                    "arial.ttf"  # Fallback
                ]
                font = None
                for font_name in font_names:
                    try:
                        font = ImageFont.truetype(font_name, self.config.subtitle_font_size)
                        break
                    except:
                        continue
                
                if font is None:
                    font = ImageFont.load_default()
            except:
                font = ImageFont.load_default()
            
            # 智能换行处理 - 为短字幕优化
            max_width = img_width - 60  # 增加边距
            lines = self._smart_wrap_text(text, font, draw, max_width)
            
            # Draw each line
            line_height = 45
            total_height = len(lines) * line_height
            start_y = (img_height - total_height) // 2
            
            for i, line in enumerate(lines):
                # Get line dimensions for centering
                line_bbox = draw.textbbox((0, 0), line, font=font)
                line_width = line_bbox[2] - line_bbox[0]
                x = (img_width - line_width) // 2
                y = start_y + i * line_height
                
                # Draw stroke (outline)
                stroke_width = 3
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            draw.text((x + dx, y + dy), line, font=font, fill=(0, 0, 0, 255))
                
                # Draw main text
                draw.text((x, y), line, font=font, fill=(255, 255, 255, 255))
            
            # Convert PIL image to numpy array for MoviePy
            img_array = np.array(img)
            
            # Create ImageClip from the array
            clip = ImageClip(img_array, duration=duration)
            
            return clip
            
        except Exception as e:
            print(f"Error creating text image: {e}")
            return None
    
    def create_ass_subtitles(self, segments: List[Dict], output_path: str):
        """Create ASS subtitle file for better styling"""
        ass_header = """[Script Info]
Title: Book Review Subtitles
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: None
PlayResX: {width}
PlayResY: {height}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Noto Sans SC,48,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,3,2,2,20,20,150,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
""".format(width=self.width, height=self.height)
        
        events = []
        for segment in segments:
            start = self._format_ass_time(segment['start'] / 1000.0)
            end = self._format_ass_time(segment['end'] / 1000.0)
            # Clean emotion tags from subtitle text
            clean_text = self._clean_emotion_tags_for_subtitles(segment['text'])
            text = clean_text.replace('\n', '\\N')
            events.append(f"Dialogue: 0,{start},{end},Default,,0,0,0,,{text}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(ass_header + '\n'.join(events))
    
    def _format_ass_time(self, seconds: float) -> str:
        """Format time for ASS subtitle format"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}:{minutes:02d}:{secs:05.2f}"
    
    def assemble_video_with_dynamic_backgrounds(self,
                                              cover_image_path: str,
                                              audio_segments: List[Tuple[str, str, float]],
                                              alignment_data: List[Dict],
                                              bgm_path: str,
                                              output_path: str,
                                              temp_dir: str,
                                              book_metadata: Dict = None) -> bool:
        """Assemble video with dynamic background transitions"""
        if not MOVIEPY_AVAILABLE:
            print("MoviePy not available, falling back to simple video...")
            return self.assemble_video(cover_image_path, audio_segments, alignment_data, bgm_path, output_path)
        
        try:
            from .dynamic_background_generator import DynamicBackgroundGenerator
            from .background_transitions import BackgroundTransitions
            
            print("🎬 Assembling video with dynamic backgrounds...")
            
            # Calculate total duration
            narration_duration = sum(seg[2] for seg in audio_segments)
            total_duration = self.opening_duration + narration_duration
            
            # Plan dynamic background sequence
            bg_generator = DynamicBackgroundGenerator(book_metadata=book_metadata)
            background_segments = bg_generator.plan_background_sequence(alignment_data, total_duration)
            
            # Generate background images
            background_segments = bg_generator.generate_background_images(background_segments, temp_dir)
            
            # Create transition system
            transitions = BackgroundTransitions()
            
            # Create opening with book cover
            opening_clip = self.create_page_flip_animation(cover_image_path, self.opening_duration)
            
            # Create dynamic background clips with transitions
            background_clips = transitions.create_background_sequence(
                background_segments, 
                (self.width, self.height)
            )
            
            # Shift background clips to start after opening
            shifted_background_clips = []
            for clip in background_clips:
                # Add opening duration to the clip's start time
                original_start = clip.start if hasattr(clip, 'start') and clip.start else 0
                new_start_time = original_start + self.opening_duration
                shifted_clip = clip.with_start(new_start_time)
                shifted_background_clips.append(shifted_clip)
                print(f"📍 Background clip: {original_start:.1f}s → {new_start_time:.1f}s (duration: {clip.duration:.1f}s)")
            
            # Combine all audio segments
            audio_clips = []
            for _, audio_path, _ in audio_segments:
                if os.path.exists(audio_path):
                    audio_clips.append(AudioFileClip(audio_path))
            
            if audio_clips:
                narration = concatenate_audioclips(audio_clips)
            else:
                print("No audio clips found!")
                return False
            
            # Create subtitle clips
            subtitle_clips = self.create_subtitle_clips(alignment_data, total_duration)
            
            # Load and adjust BGM
            if os.path.exists(bgm_path):
                bgm = AudioFileClip(bgm_path)
                bgm = bgm.with_volume_scaled(self.bgm_volume)
                bgm = bgm.with_duration(total_duration)
                bgm = bgm.with_effects([afx.AudioFadeIn(1.0), afx.AudioFadeOut(1.0)])
            else:
                bgm = None
            
            # Combine all video clips
            all_video_clips = [opening_clip] + shifted_background_clips + subtitle_clips
            video = CompositeVideoClip(all_video_clips, size=(self.width, self.height))
            
            # Set narration to start after opening
            narration = narration.with_start(self.opening_duration)
            
            # Combine audio
            if bgm:
                final_audio = CompositeAudioClip([bgm, narration])
            else:
                final_audio = narration
            
            # Set audio to video
            video = video.with_audio(final_audio)
            
            # Write video file
            print(f"🎬 Rendering final video: {output_path}")
            video.write_videofile(
                output_path,
                fps=self.fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )
            
            # Clean up
            video.close()
            
            print("✅ Dynamic background video assembly complete!")
            return True
            
        except Exception as e:
            print(f"❌ Dynamic background assembly failed: {e}")
            print("🔄 Falling back to standard video assembly...")
            return self.assemble_video(cover_image_path, audio_segments, alignment_data, bgm_path, output_path)

    def assemble_video(self, 
                      cover_image_path: str,
                      audio_segments: List[Tuple[str, str, float]],
                      alignment_data: List[Dict],
                      bgm_path: str,
                      output_path: str) -> bool:
        """Assemble the final video"""
        if not MOVIEPY_AVAILABLE:
            print("MoviePy not available, trying simple video creation...")
            # Concatenate all audio
            all_audio_path = output_path.replace('.mp4', '_audio.mp3')
            from .tts_generator import TTSGenerator
            tts = TTSGenerator()
            audio_files = [seg[1] for seg in audio_segments]
            success = tts.concatenate_audio_files(audio_files, all_audio_path)
            if success:
                return self.create_simple_video(cover_image_path, all_audio_path, output_path)
            return False
            
        try:
            # Create opening with page flip
            opening_clip = self.create_page_flip_animation(cover_image_path, self.opening_duration)
            
            # Create main video background
            # Use the cover image as background throughout
            background = ImageClip(cover_image_path, duration=None)
            background = background.resized((self.width, self.height))
            
            # Combine all audio segments
            audio_clips = []
            for _, audio_path, _ in audio_segments:
                if os.path.exists(audio_path):
                    audio_clips.append(AudioFileClip(audio_path))
            
            if audio_clips:
                narration = concatenate_audioclips(audio_clips)
            else:
                print("No audio clips found!")
                return False
            
            # Calculate total duration
            total_duration = self.opening_duration + narration.duration
            
            # Set background duration
            background = background.with_duration(total_duration - self.opening_duration)
            background = background.with_start(self.opening_duration)
            
            # Create subtitle clips
            subtitle_clips = self.create_subtitle_clips(alignment_data, total_duration)
            
            # Load and adjust BGM
            if os.path.exists(bgm_path):
                bgm = AudioFileClip(bgm_path)
                bgm = bgm.with_volume_scaled(self.bgm_volume)
                bgm = bgm.with_duration(total_duration)
                bgm = bgm.with_effects([afx.AudioFadeIn(1.0), afx.AudioFadeOut(1.0)])
            else:
                bgm = None
            
            # Combine video clips
            video_clips = [opening_clip, background] + subtitle_clips
            video = CompositeVideoClip(video_clips, size=(self.width, self.height))
            
            # Set narration to start after opening
            narration = narration.with_start(self.opening_duration)
            
            # Combine audio
            if bgm:
                final_audio = CompositeAudioClip([bgm, narration])
            else:
                final_audio = narration
            
            # Set audio to video
            video = video.with_audio(final_audio)
            
            # Write video file
            video.write_videofile(
                output_path,
                fps=self.fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )
            
            # Clean up
            video.close()
            
            return True
            
        except Exception as e:
            print(f"Error assembling video: {e}")
            return False
    
    def create_simple_video(self, image_path: str, audio_path: str, output_path: str) -> bool:
        """Create a simple video without subtitles as fallback"""
        try:
            # Load image and audio
            img = ImageClip(image_path)
            img = img.resized((self.width, self.height))
            
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            
            # Set image duration to match audio
            img = img.with_duration(duration)
            
            # Add audio to video
            video = img.with_audio(audio)
            
            # Write video
            video.write_videofile(
                output_path,
                fps=self.fps,
                codec='libx264',
                audio_codec='aac'
            )
            
            return True
            
        except Exception as e:
            print(f"Error creating simple video: {e}")
            return False