import requests
import json
import time
from typing import Dict, <PERSON><PERSON>, Optional
from .config import Config
from .book_info_crawler import BookInfo, BookInfoCrawler
from .book_data_verifier import BookDataVerifier, VerificationResult
from .reviewer_personalities import ReviewerPersonalities, ReviewerPersonality
from .book_content_analyzer import <PERSON><PERSON>ontent<PERSON>naly<PERSON>, BookContent
from .concept_expander import ConceptExpander, SemanticContext
from .doubao_search_enhancer import DoubaoSearchEnhancer

class EnhancedScriptGenerator:
    """Enhanced script generator using comprehensive book data and reviewer personalities"""
    
    def __init__(self):
        # Doubao LLM Configuration
        self.ark_api_key = Config.ARK_API_KEY
        self.ark_base_url = Config.ARK_BASE_URL
        self.ark_model = Config.ARK_MODEL
        
        # Fallback to OpenRouter if <PERSON><PERSON>o not configured
        self.openrouter_api_key = Config.OPENROUTER_API_KEY
        self.openrouter_base_url = Config.OPENROUTER_BASE_URL
        
        self.book_crawler = BookInfoCrawler()
        self.data_verifier = BookDataVerifier()
        self.content_analyzer = BookContentAnalyzer()
        self.concept_expander = ConceptExpander()
        self.search_enhancer = DoubaoSearchEnhancer()
        
    def generate_enhanced_review(self, 
                               book_title: str, 
                               author: str, 
                               personality_id: str = None,
                               use_web_data: bool = True) -> Tuple[str, str, Dict]:
        """
        Generate enhanced book review using comprehensive book data and personality
        Returns: (review_script, illustration_prompt, book_data_dict)
        """
        
        print(f"🔄 Starting enhanced review generation for '{book_title}' by {author}")
        
        # Step 1: Gather comprehensive book information
        book_info = None
        verification_result = None
        book_content = None
        
        if use_web_data:
            print("🌐 Gathering comprehensive book information...")
            book_info = self.book_crawler.gather_comprehensive_info(book_title, author)
            
            # Verify and enhance the data
            verification_result = self.data_verifier.verify_book_data(book_info)
            book_info = self.data_verifier.enhance_book_data(book_info)
            
            print(f"📊 Data verification: {'✅ Verified' if verification_result.is_verified else '⚠️ Partial'} "
                  f"(Confidence: {verification_result.confidence_score:.2%})")
            
            # Step 1.5: Analyze book content for plot, characters, and themes
            print("📖 Analyzing book content for detailed review...")
            book_content = self.content_analyzer.analyze_book_content(book_info)
            
        else:
            # Create basic book info
            book_info = BookInfo(title=book_title, author=author)
        
        # Step 2: Select reviewer personality
        if not personality_id:
            personality_id = ReviewerPersonalities.recommend_personality_for_book(book_info)
        
        if not ReviewerPersonalities.validate_personality_id(personality_id):
            personality_id = ReviewerPersonalities.get_default_personality()
        
        personality = ReviewerPersonalities.get_personality(personality_id)
        print(f"🎭 Using reviewer personality: {personality.name}")
        
        # Step 3: Generate enhanced review script
        review_script = self._generate_review_with_personality(book_info, personality, verification_result, book_content)
        
        # Step 4: Generate illustration prompt
        illustration_prompt = self._generate_illustration_prompt(book_info, personality)
        
        # Step 5: Prepare return data
        book_data_dict = {
            'title': book_info.title,
            'author': book_info.author,
            'description': book_info.description,
            'genres': book_info.genres,
            'themes': book_info.themes,
            'average_rating': book_info.average_rating,
            'publication_year': book_info.publication_year,
            'reviews_count': len(book_info.reviews) if book_info.reviews else 0,
            'personality_used': personality.name,
            'data_verified': verification_result.is_verified if verification_result else False,
            'confidence_score': verification_result.confidence_score if verification_result else 0.5,
            'content_analyzed': book_content is not None,
            'plot_available': bool(book_content.plot_summary) if book_content else False,
            'characters_found': len(book_content.main_characters) if book_content else 0,
            'analysis_depth': len(book_content.critical_analysis) if book_content else 0
        }
        
        print("✅ Enhanced review generation completed")
        return review_script, illustration_prompt, book_data_dict
    
    def generate_from_prompt(self, prompt_text: str, personality_id: str = None, use_web_data: bool = True) -> Tuple[str, str, Dict]:
        """
        Generate content from prompt using comprehensive concept expansion and personality
        Returns: (content_script, illustration_prompt, content_data_dict)
        """
        
        print(f"🔄 Starting content generation from prompt: {prompt_text[:100]}...")
        
        # Step 1: Enhance prompt with web search if enabled
        enhanced_prompt = prompt_text
        search_metadata = {}
        if use_web_data:
            print("🌐 Enhancing prompt with intelligent web search...")
            enhanced_prompt, search_metadata = self.search_enhancer.enhance_prompt_with_search(prompt_text)
        
        # Step 2: Expand concepts and analyze semantics (using enhanced prompt)
        print("🔍 Expanding concepts and analyzing semantic context...")
        semantic_context = self.concept_expander.expand_user_input(enhanced_prompt)
        
        # Step 3: Gather additional web information using expanded concepts
        expanded_info = search_metadata  # Use search results as expanded info
        if use_web_data and not search_metadata.get('search_successful', False):
            print("🌐 Gathering additional information using expanded concepts...")
            fallback_info = self._gather_expanded_info(semantic_context)
            expanded_info.update(fallback_info)
        
        # Step 4: Select reviewer personality
        if not personality_id:
            personality_id = self._recommend_personality_for_content(semantic_context)
        
        if not ReviewerPersonalities.validate_personality_id(personality_id):
            personality_id = ReviewerPersonalities.get_default_personality()
        
        personality = ReviewerPersonalities.get_personality(personality_id)
        print(f"🎭 Using reviewer personality: {personality.name}")
        
        # Step 5: Generate enhanced content script using search-enhanced prompt
        content_script = self._generate_content_with_expanded_concepts(
            enhanced_prompt, semantic_context, personality, expanded_info
        )
        
        # Step 6: Add emotion markers for enhanced TTS if enabled
        if Config.ENABLE_EMOTION_MARKERS:
            print("🎭 Adding emotion markers to generated content...")
            content_script = self._add_emotion_markers_for_content(content_script, personality, semantic_context)
        
        # Step 7: Generate illustration prompt based on expanded concepts
        illustration_prompt = self._generate_illustration_from_concepts(semantic_context, content_script)
        
        # Step 8: Prepare return data
        content_data_dict = {
            'original_prompt': prompt_text,
            'enhanced_prompt': enhanced_prompt,
            'search_enhancement': search_metadata,
            'domain': semantic_context.domain,
            'intent': semantic_context.intent,
            'content_type': semantic_context.content_type,
            'key_entities': semantic_context.key_entities,
            'expanded_concepts_count': len(semantic_context.expanded_concepts),
            'search_queries_generated': len(semantic_context.search_queries),
            'personality_used': personality.name,
            'data_enhanced': use_web_data and (expanded_info is not None or search_metadata.get('search_successful', False)),
            'concept_expansion_successful': len(semantic_context.expanded_concepts) > 0,
            'semantic_depth': sum([len(exp.related_concepts) + len(exp.synonyms) for exp in semantic_context.expanded_concepts]),
            'search_enhanced': search_metadata.get('search_successful', False),
            'prompt_enhancement_ratio': len(enhanced_prompt) / len(prompt_text) if prompt_text else 1
        }
        
        print("✅ Enhanced content generation from prompt completed")
        return content_script, illustration_prompt, content_data_dict
    
    def generate_from_prompt_basic(self, prompt_text: str) -> Tuple[str, str]:
        """
        Generate basic content from prompt without advanced features
        Returns: (content_script, illustration_prompt)
        """
        
        print(f"📝 Generating basic content from prompt: {prompt_text[:100]}...")
        
        # Use default personality
        personality = ReviewerPersonalities.get_personality(ReviewerPersonalities.get_default_personality())
        
        # Generate basic content script
        content_script = self._generate_basic_content_script(prompt_text, personality)
        
        # Add emotion markers if enabled
        if Config.ENABLE_EMOTION_MARKERS:
            print("🎭 Adding emotion markers to basic content...")
            # Create a minimal semantic context for basic mode
            basic_semantic_context = type('obj', (object,), {
                'domain': '通用',
                'key_entities': self._extract_basic_entities(prompt_text)
            })
            content_script = self._add_emotion_markers_for_content(content_script, personality, basic_semantic_context)
        
        # Generate basic illustration prompt
        illustration_prompt = self._generate_basic_illustration_prompt(prompt_text)
        
        print("✅ Basic content generation completed")
        return content_script, illustration_prompt
    
    def _gather_expanded_info(self, semantic_context: SemanticContext) -> Dict:
        """
        Gather information from the web using expanded search queries
        """
        # This would integrate with web search APIs using the expanded queries
        # For now, return structured data based on semantic analysis
        return {
            'domain_insights': f"领域相关洞察：{semantic_context.domain}",
            'related_topics': [exp.associated_topics for exp in semantic_context.expanded_concepts],
            'concept_depth': len(semantic_context.expanded_concepts),
            'search_coverage': len(semantic_context.search_queries)
        }
    
    def _recommend_personality_for_content(self, semantic_context: SemanticContext) -> str:
        """
        Recommend personality based on semantic context
        """
        domain = semantic_context.domain
        intent = semantic_context.intent
        content_type = semantic_context.content_type
        
        # Domain-based recommendations
        if domain == "文学":
            return "dongyu_hui" if content_type == "通俗内容" else "academic_scholar"
        elif domain == "科技":
            return "practical_reviewer" if intent == "知识介绍" else "academic_scholar"
        elif domain == "商业":
            return "practical_reviewer"
        elif domain == "教育":
            return "life_coach"
        elif domain == "生活":
            return "emotional_reader" if content_type == "情感内容" else "dongyu_hui"
        
        # Intent-based fallback
        if intent == "教学指导":
            return "life_coach"
        elif intent == "推荐建议":
            return "practical_reviewer"
        elif intent == "评论分析":
            return "academic_scholar"
        
        return "dongyu_hui"  # Default
    
    def _generate_content_with_expanded_concepts(self, 
                                               original_prompt: str, 
                                               semantic_context: SemanticContext,
                                               personality: ReviewerPersonality,
                                               expanded_info: Dict = None) -> str:
        """
        Generate content script using expanded concepts and semantic analysis
        """
        
        # Build comprehensive context
        semantic_context_str = self._build_semantic_context(semantic_context)
        expanded_context_str = self._build_expanded_context(expanded_info) if expanded_info else ""
        personality_context_str = self._build_personality_context(personality)
        
        prompt = f"""
        请根据以下语义分析和扩展概念，生成一篇高质量的内容脚本：

        ## 原始用户输入
        {original_prompt}

        ## 语义上下文分析
        {semantic_context_str}

        ## 扩展信息
        {expanded_context_str}

        ## 内容创作者人格设定
        {personality_context_str}

        ## 生成要求：
        1. 严格按照指定创作者的风格和语调撰写
        2. 充分利用扩展的概念和相关信息
        3. 体现创作者的专业水平和关注重点
        4. 字数控制在200-300字左右，朗读时长约50-70秒
        5. 语言流畅自然，适合口语表达
        6. 必须体现创作者的典型用词和表达习惯
        7. 深度利用扩展概念，展现内容的广度和深度

        ## 结构建议：
        - 开场：使用创作者典型的开场方式
        - 主体：结合扩展概念进行深度阐述
        - 结尾：符合创作者风格的总结或启发

        请直接返回内容脚本，不要包含其他格式或标记。
        """
        
        try:
            # Try Doubao LLM first, fallback to OpenRouter
            if self.ark_api_key:
                content_script = self._call_doubao_llm_api(prompt, personality)
            else:
                content_script = self._call_openrouter_api(prompt, personality)
            
            # Enhance script with personality
            enhanced_script = self._enhance_script_with_personality(content_script, personality)
            
            return enhanced_script
            
        except Exception as e:
            print(f"❌ API generation failed: {e}")
            return self._generate_fallback_content_script(original_prompt, semantic_context, personality)
    
    def _build_semantic_context(self, semantic_context: SemanticContext) -> str:
        """Build semantic context description for prompt"""
        context_parts = []
        
        context_parts.append(f"内容领域：{semantic_context.domain}")
        context_parts.append(f"用户意图：{semantic_context.intent}")
        context_parts.append(f"内容类型：{semantic_context.content_type}")
        
        if semantic_context.key_entities:
            context_parts.append(f"关键实体：{', '.join(semantic_context.key_entities[:8])}")
        
        if semantic_context.expanded_concepts:
            context_parts.append("扩展概念分析：")
            for i, concept in enumerate(semantic_context.expanded_concepts[:3]):
                concept_info = []
                if concept.synonyms:
                    concept_info.append(f"同义词: {', '.join(concept.synonyms[:3])}")
                if concept.related_concepts:
                    concept_info.append(f"相关概念: {', '.join(concept.related_concepts[:3])}")
                if concept.broader_concepts:
                    concept_info.append(f"上级概念: {', '.join(concept.broader_concepts[:2])}")
                if concept.associated_topics:
                    concept_info.append(f"关联话题: {', '.join(concept.associated_topics[:3])}")
                
                context_parts.append(f"  {i+1}. {concept.original_concept}: {'; '.join(concept_info)}")
        
        return "\n".join(context_parts)
    
    def _build_expanded_context(self, expanded_info: Dict) -> str:
        """Build expanded information context"""
        if not expanded_info:
            return "暂无扩展信息"
        
        context_parts = []
        
        if 'domain_insights' in expanded_info:
            context_parts.append(expanded_info['domain_insights'])
        
        if 'related_topics' in expanded_info:
            all_topics = []
            for topic_list in expanded_info['related_topics']:
                all_topics.extend(topic_list[:2])  # Take first 2 from each list
            if all_topics:
                context_parts.append(f"相关话题: {', '.join(all_topics[:6])}")
        
        return "\n".join(context_parts)
    
    def _generate_basic_content_script(self, prompt_text: str, personality: ReviewerPersonality) -> str:
        """Generate basic content script without advanced features"""
        
        basic_prompt = f"""
        请根据以下用户输入，生成一篇简洁的内容脚本：

        用户输入：{prompt_text}

        要求：
        1. 内容简洁明了，字数控制在150-200字
        2. 语言自然流畅，适合朗读
        3. 针对用户输入的核心内容进行阐述
        4. 风格：{personality.writing_style}
        5. 语调：{personality.tone}

        请直接返回内容脚本。
        """
        
        try:
            if self.ark_api_key:
                return self._call_doubao_llm_api(basic_prompt, personality)
            else:
                return self._call_openrouter_api(basic_prompt, personality)
        except Exception as e:
            print(f"❌ Basic generation failed: {e}")
            return f"今天我们来聊聊{prompt_text}。这是一个很有意思的话题，值得我们深入思考。让我们一起来探讨一下其中的奥秘和价值。"
    
    def _generate_basic_illustration_prompt(self, prompt_text: str) -> str:
        """Generate basic illustration prompt from content"""
        return f"根据以下内容创建视觉插图：{prompt_text[:100]}。要求：简洁美观，色彩和谐，适合作为视频背景。"
    
    def _generate_illustration_from_concepts(self, semantic_context: SemanticContext, content_script: str) -> str:
        """Generate illustration prompt based on expanded concepts and generated content"""
        
        # Extract visual elements from expanded concepts
        visual_elements = []
        
        # Add domain-specific visual style
        domain_styles = {
            "文学": "书香气息，温暖色调，古典文化元素",
            "科技": "现代简约，蓝色调，科技感线条",
            "商业": "专业商务风格，深色调配金色点缀",
            "教育": "清新学术风格，明亮色调",
            "生活": "温暖人文色调，柔和渐变",
            "产品": "现代设计感，简洁线条"
        }
        
        base_style = domain_styles.get(semantic_context.domain, "现代简约，和谐色彩")
        visual_elements.append(base_style)
        
        # Add concept-based visual elements
        for concept in semantic_context.expanded_concepts[:2]:
            if concept.associated_topics:
                visual_elements.extend(concept.associated_topics[:2])
        
        # Extract visual cues from generated content
        content_keywords = content_script[:200].lower()
        if "深度" in content_keywords or "思考" in content_keywords:
            visual_elements.append("深度思考的氛围")
        if "创新" in content_keywords or "未来" in content_keywords:
            visual_elements.append("创新未来感")
        
        illustration_prompt = f"""
        创建一个视觉插图，体现以下元素：
        
        主题领域：{semantic_context.domain}
        视觉风格：{base_style}
        关键概念：{', '.join([c.original_concept for c in semantic_context.expanded_concepts[:3]])}
        视觉元素：{', '.join(visual_elements[:5])}
        
        要求：
        - 画面简洁美观，色彩和谐
        - 体现内容的深度和广度
        - 适合作为视频背景使用
        - 具有很强的视觉吸引力
        """
        
        return illustration_prompt
    
    def _generate_fallback_content_script(self, prompt_text: str, semantic_context: SemanticContext, personality: ReviewerPersonality) -> str:
        """Generate fallback content script when API fails"""
        
        # Use personality phrases and semantic context
        opening = personality.sample_opening or "朋友们，今天我们来聊聊一个很有意思的话题。"
        closing = personality.sample_closing or "希望这些分享能给大家带来一些思考和启发。"
        
        # Build content based on expanded concepts
        main_concepts = [c.original_concept for c in semantic_context.expanded_concepts[:3]]
        related_topics = []
        for concept in semantic_context.expanded_concepts[:2]:
            related_topics.extend(concept.related_concepts[:2])
        
        middle_content = f"关于{prompt_text}，我们可以从{semantic_context.domain}的角度来理解。"
        if main_concepts:
            middle_content += f"其中涉及到{', '.join(main_concepts)}等核心概念。"
        if related_topics:
            middle_content += f"这也让我们联想到{', '.join(related_topics[:3])}等相关话题。"
        
        return f"{opening} {middle_content} {closing}"
    
    def _generate_review_with_personality(self, 
                                        book_info: BookInfo, 
                                        personality: ReviewerPersonality,
                                        verification_result: Optional[VerificationResult],
                                        book_content: Optional[BookContent] = None) -> str:
        """Generate review script using personality and comprehensive book data"""
        
        # Build comprehensive prompt with book data and content
        book_context = self._build_book_context(book_info, verification_result)
        content_context = self._build_content_context(book_content) if book_content else ""
        personality_context = self._build_personality_context(personality)
        
        prompt = f"""
        请根据以下书籍信息和评论者人格，生成一篇高质量的中文书评：

        ## 书籍基本信息
        {book_context}

        ## 书籍内容分析
        {content_context}

        ## 评论者人格设定
        {personality_context}

        ## 生成要求：
        1. 严格按照指定评论者的风格和语调撰写
        2. 充分利用提供的书籍详细信息
        3. 体现评论者的专业水平和关注重点
        4. 字数控制在200-250字左右，朗读时长约45-60秒
        5. 语言流畅自然，适合口语表达
        6. 必须体现评论者的典型用词和表达习惯

        ## 结构建议：
        - 开场：使用评论者典型的开场方式
        - 主体：结合书籍信息进行深度解读
        - 结尾：符合评论者风格的总结或感悟

        请直接返回书评内容，不要包含其他格式或标记。
        """
        
        try:
            # Try Doubao LLM first, fallback to OpenRouter
            if self.ark_api_key:
                review_script = self._call_doubao_llm_api(prompt, personality)
            else:
                review_script = self._call_openrouter_api(prompt, personality)
            
            # Validate and enhance the generated script
            enhanced_script = self._enhance_script_with_personality(review_script, personality)
            
            # Add emotion markers for Doubao TTS if enabled
            if Config.ENABLE_EMOTION_MARKERS:
                marked_script = self._add_emotion_markers(enhanced_script, personality, book_info)
                return marked_script
            else:
                return enhanced_script
            
        except Exception as e:
            print(f"❌ API generation failed: {e}")
            fallback_script = self._generate_fallback_script(book_info, personality, book_content)
            # Add emotion markers to fallback script as well if enabled
            if Config.ENABLE_EMOTION_MARKERS:
                marked_fallback = self._add_emotion_markers(fallback_script, personality, book_info)
                return marked_fallback
            else:
                return fallback_script
    
    def _build_book_context(self, book_info: BookInfo, verification_result: Optional[VerificationResult]) -> str:
        """Build comprehensive book context for the prompt"""
        context_parts = []
        
        # Basic info
        context_parts.append(f"书名：《{book_info.title}》")
        context_parts.append(f"作者：{book_info.author}")
        
        if book_info.publication_year:
            context_parts.append(f"出版年份：{book_info.publication_year}")
        
        if book_info.publisher:
            context_parts.append(f"出版社：{book_info.publisher}")
        
        # Rating and reviews
        if book_info.average_rating:
            context_parts.append(f"平均评分：{book_info.average_rating:.1f}")
        
        if book_info.ratings_count:
            context_parts.append(f"评价数量：{book_info.ratings_count}")
        
        # Description and summary
        if book_info.description:
            context_parts.append(f"内容简介：{book_info.description[:300]}...")
        
        # Genres and themes
        if book_info.genres:
            context_parts.append(f"类型标签：{', '.join(book_info.genres[:5])}")
        
        if book_info.themes:
            context_parts.append(f"主要主题：{', '.join(book_info.themes[:5])}")
        
        # Sample reviews
        if book_info.reviews:
            context_parts.append("读者评价摘要：")
            for i, review in enumerate(book_info.reviews[:3]):
                context_parts.append(f"- {review['text'][:100]}...")
        
        # Data quality info
        if verification_result:
            context_parts.append(f"数据可信度：{verification_result.confidence_score:.2%}")
        
        return "\n".join(context_parts)
    
    def _build_personality_context(self, personality: ReviewerPersonality) -> str:
        """Build personality context for the prompt"""
        context_parts = []
        
        context_parts.append(f"角色：{personality.name}")
        context_parts.append(f"风格描述：{personality.description}")
        context_parts.append(f"写作风格：{personality.writing_style}")
        context_parts.append(f"语调特点：{personality.tone}")
        context_parts.append(f"专业水平：{personality.expertise_level}")
        context_parts.append(f"关注视角：{personality.perspective}")
        
        context_parts.append(f"关注领域：{', '.join(personality.focus_areas)}")
        context_parts.append(f"常用表达：{', '.join(personality.typical_phrases[:8])}")
        
        context_parts.append(f"开场示例：{personality.sample_opening}")
        context_parts.append(f"结尾示例：{personality.sample_closing}")
        
        return "\n".join(context_parts)
    
    def _build_content_context(self, book_content: BookContent) -> str:
        """Build detailed content context for the prompt"""
        context_parts = []
        
        if book_content.plot_summary:
            context_parts.append(f"故事情节：{book_content.plot_summary[:400]}...")
        
        if book_content.main_characters:
            context_parts.append(f"主要人物：{', '.join(book_content.main_characters[:5])}")
        
        if book_content.key_themes:
            context_parts.append(f"核心主题：{', '.join(book_content.key_themes[:6])}")
        
        if book_content.notable_quotes:
            context_parts.append("重要段落/金句：")
            for i, quote in enumerate(book_content.notable_quotes[:3]):
                context_parts.append(f"  {i+1}. {quote[:150]}...")
        
        if book_content.critical_analysis:
            context_parts.append("文学分析：")
            for i, analysis in enumerate(book_content.critical_analysis[:2]):
                context_parts.append(f"  {i+1}. {analysis[:200]}...")
        
        if book_content.cultural_context:
            context_parts.append(f"文化背景：{book_content.cultural_context}")
        
        if book_content.literary_significance:
            context_parts.append(f"文学意义：{book_content.literary_significance[:300]}...")
        
        if book_content.reader_insights:
            context_parts.append("读者见解：")
            for insight in book_content.reader_insights[:2]:
                context_parts.append(f"  - {insight[:150]}...")
        
        return "\n".join(context_parts) if context_parts else "暂无详细内容分析"
    
    def _call_doubao_llm_api(self, prompt: str, personality: ReviewerPersonality) -> str:
        """Call Doubao LLM API to generate review using OpenAI-compatible interface"""
        try:
            from openai import OpenAI
            
            # Initialize Doubao LLM client
            client = OpenAI(
                base_url=self.ark_base_url,
                api_key=self.ark_api_key,
            )
            
            # Adjust temperature based on personality
            temperature = 0.8 if personality.id in ["entertainment_critic", "young_reader"] else 0.7
            
            print(f"🤖 Using Doubao LLM: {self.ark_model}")
            
            completion = client.chat.completions.create(
                model=self.ark_model,
                messages=[
                    {
                        "role": "system",
                        "content": f"你是{personality.name}，{personality.description}。请严格按照这个人格特征来撰写书评。"
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_tokens=800
            )
            
            result = completion.choices[0].message.content.strip()
            print(f"✅ Doubao LLM response received: {len(result)} characters")
            return result
            
        except ImportError:
            print("❌ OpenAI library not available, falling back to OpenRouter")
            return self._call_openrouter_api(prompt, personality)
        except Exception as e:
            print(f"❌ Doubao LLM API error: {e}")
            if self.openrouter_api_key:
                print("🔄 Falling back to OpenRouter API")
                return self._call_openrouter_api(prompt, personality)
            else:
                raise
    
    def _call_openrouter_api(self, prompt: str, personality: ReviewerPersonality) -> str:
        """Call OpenRouter API to generate review"""
        headers = {
            "Authorization": f"Bearer {self.openrouter_api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/yourusername/airiverse",
            "X-Title": "AiriVerse Enhanced Book Review Generator"
        }
        
        # Adjust temperature based on personality
        temperature = 0.8 if personality.id in ["entertainment_critic", "young_reader"] else 0.7
        
        data = {
            "model": "google/gemini-2.0-flash-exp:free",
            "messages": [
                {
                    "role": "system",
                    "content": f"你是{personality.name}，{personality.description}。请严格按照这个人格特征来撰写书评。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": temperature,
            "max_tokens": 800
        }
        
        max_retries = 3
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{self.openrouter_base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=30
                )
                
                if response.status_code == 429:
                    wait_time = retry_delay * (2 ** attempt)
                    print(f"⏳ Rate limited. Waiting {wait_time}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue
                
                response.raise_for_status()
                result = response.json()
                
                return result['choices'][0]['message']['content'].strip()
                
            except Exception as e:
                print(f"❌ API call error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                raise
    
    def _enhance_script_with_personality(self, script: str, personality: ReviewerPersonality) -> str:
        """Enhance script to better match personality style"""
        # Handle None or empty script
        if not script:
            print("⚠️ Empty script received, generating basic fallback")
            return f"今天为大家推荐一本值得阅读的好书。"
        
        enhanced_script = script
        
        # Add personality-specific phrases if missing
        script_lower = script.lower()
        used_phrases = sum(1 for phrase in personality.typical_phrases 
                          if phrase.lower() in script_lower)
        
        # If too few personality phrases are used, add some naturally
        if used_phrases < 2:
            # Insert a typical phrase at the beginning if needed
            if not any(phrase.lower() in script[:50].lower() for phrase in personality.typical_phrases):
                opening_phrases = [p for p in personality.typical_phrases if len(p) < 10]
                if opening_phrases and not script.startswith(opening_phrases[0]):
                    enhanced_script = f"{opening_phrases[0]}，{script}"
        
        return enhanced_script
    
    def _generate_fallback_script(self, book_info: BookInfo, personality: ReviewerPersonality, book_content: Optional[BookContent] = None) -> str:
        """Generate fallback script when API fails"""
        print(f"🔄 Generating fallback script with {personality.name} style")
        
        # Use personality template to create fallback
        if personality.id == "dongyu_hui":
            return self._dongyu_hui_fallback(book_info, book_content)
        elif personality.id == "academic_scholar":
            return self._academic_fallback(book_info, book_content)
        elif personality.id == "emotional_reader":
            return self._emotional_fallback(book_info, book_content)
        elif personality.id == "practical_reviewer":
            return self._practical_fallback(book_info, book_content)
        elif personality.id == "entertainment_critic":
            return self._entertainment_fallback(book_info, book_content)
        elif personality.id == "philosophical_thinker":
            return self._philosophical_fallback(book_info, book_content)
        elif personality.id == "young_reader":
            return self._young_reader_fallback(book_info, book_content)
        elif personality.id == "life_coach":
            return self._life_coach_fallback(book_info, book_content)
        else:
            return self._dongyu_hui_fallback(book_info, book_content)  # Default fallback
    
    def _dongyu_hui_fallback(self, book_info: BookInfo, book_content: Optional[BookContent] = None) -> str:
        """董宇辉风格的回退脚本"""
        # Use actual book content if available
        themes_text = "人生智慧"
        if book_content and book_content.key_themes:
            themes_text = "、".join(book_content.key_themes[:2])
        elif book_info.themes:
            themes_text = "、".join(book_info.themes[:2])
        
        # Add plot details if available
        plot_intro = ""
        if book_content and book_content.plot_summary:
            plot_intro = f"这本书讲述了{book_content.plot_summary[:100]}的故事。"
        
        # Add character mentions if available  
        character_mention = ""
        if book_content and book_content.main_characters:
            main_char = book_content.main_characters[0]
            character_mention = f"书中的{main_char}让我印象深刻，"
        
        return f"""朋友们，今天咱们聊聊{book_info.author}的《{book_info.title}》。说实话，读完这本书我想了很久。

{plot_intro}真正的好书，就像一位智慧的老朋友，在你迷茫时给你指路，在你疲惫时给你温暖。{character_mention}这本书探讨的是{themes_text}，但更重要的是，它告诉我们如何在平凡的生活中找到不平凡的意义。

你想想看，我们每个人的生命都是有限的，但通过阅读，我们可以体验无限种人生。{book_info.author}用最朴素的文字，讲述最深刻的道理，就像我们小时候听长辈讲故事一样。

希望这本书能给你带来一些思考。愿我们都能在这个快速变化的世界里，做一个温暖的人，因为真正的财富，不在于你拥有多少，而在于你能给这个世界带来多少光明。"""
    
    def _academic_fallback(self, book_info: BookInfo, book_content: Optional[BookContent] = None) -> str:
        """学者型回退脚本"""
        return f"""从学术角度分析，{book_info.author}的《{book_info.title}》在当代文学中具有重要地位。

本书的叙事结构和思想内涵值得深入研究。作者通过独特的文学技巧，展现了深刻的思想体系。从文本分析来看，该作品不仅具有重要的文学价值，更体现了深层次的理论意义。

值得注意的是，这部作品在思想史的语境中展现了独特的批判性思维。其文学技巧的运用和理论框架的构建，为相关领域的研究提供了新的视角。

综上所述，这是一部具有重要学术价值的作品，无论是其文学成就还是思想贡献，都值得我们深入研究和思考。"""
    
    def _emotional_fallback(self, book_info: BookInfo, book_content: Optional[BookContent] = None) -> str:
        """感性读者回退脚本"""
        return f"""拿到《{book_info.title}》这本书的时候，我就被深深吸引了。读着读着，内心深处涌起了一阵阵的感动。

{book_info.author}的文字就像有魔力一样，每一个字都能触动我的心弦。特别是书中的那些情感描述，让我情不自禁地想起了自己的经历，真的是感同身受。

说真心话，很久没有一本书能让我如此动容了。那种心灵震撼的感觉，就像在黑暗中突然看到了光明。作者把最真挚的情感都写了出来，说到了我的心坎里。

合上书本的那一刻，我的心还在颤抖。这本书给了我太多的感动和思考，让我更加珍惜生活中的每一份美好。"""
    
    def _practical_fallback(self, book_info: BookInfo) -> str:
        """实用主义者回退脚本"""
        return f"""这本《{book_info.title}》真的是干货满满！我来给大家总结几个最实用的要点。

首先，{book_info.author}在书中提出的核心观点非常有可操作性。建议大家在阅读时重点关注这些方法，立马就能运用到实际生活中。

从实用价值来看，这本书适合各个年龄段的读者。无论你是学生还是职场人士，都能从中找到对自己有用的内容。划重点的是，书中的这些建议都经过了实践验证。

总的来说，这本书值得收藏。建议大家读完就开始行动，把这些实用的方法真正运用起来。记住这点：知识只有应用才有价值。"""
    
    def _entertainment_fallback(self, book_info: BookInfo) -> str:
        """娱乐评论家回退脚本"""
        return f"""哈哈哈，这本《{book_info.title}》简直就是快乐源泉！{book_info.author}真的是个隐藏的段子手。

不得不说，这本书太有意思了！读的时候我好几次都笑出声来。作者的文笔真的很有梗，那些神转折简直让人意想不到，惊喜满满。

最让我佩服的是，作者能把严肃的话题写得这么有趣。真香定律在这里得到了完美体现——本来以为会很枯燥，没想到如此精彩。

总之，如果你想要一本既有营养又有趣的书，这本绝对不会让你失望！简直了，太真实了！"""
    
    def _philosophical_fallback(self, book_info: BookInfo) -> str:
        """哲思者回退脚本"""
        return f"""这本《{book_info.title}》让我思考一个根本性问题：我们存在的意义究竟是什么？

从哲学角度来看，{book_info.author}在作品中探讨的不仅仅是表面的故事，更是深层次的存在问题。本质上说，这是一次关于人生意义的深度思辨。

作品的哲学意义在于，它触及了我们精神层面的终极问题。在思维深度上，作者展现了对存在主义和形而上学问题的深刻理解。这种思辨性的探索，值得我们认真对待。

读完这本书，我们需要问自己：在有限的生命中，什么才是真正重要的？这样的哲学思考，正是我们在快节奏生活中最需要的精神营养。"""
    
    def _young_reader_fallback(self, book_info: BookInfo) -> str:
        """年轻读者回退脚本"""
        return f"""姐妹们！这本《{book_info.title}》真的绝绝子，看完直接破防了！

{book_info.author}的文笔真的yyds，那种代入感超级强。作为一个社畜，看到书中的一些情节真的DNA动了，太有共鸣了！

说真的，现在的年轻人压力这么大，内卷这么严重，能找到这样一本能让人暂时躺平的好书真的不容易。这本书就像精神小伙一样，给人满满的正能量。

总之这本书真香，强烈安利给所有小伙伴们！绝对不会让你们失望的，爱了爱了！"""
    
    def _life_coach_fallback(self, book_info: BookInfo) -> str:
        """生活导师回退脚本"""
        return f"""在人生的成长路上，我们都需要导师。{book_info.author}的《{book_info.title}》就是这样一位智慧的引路人。

这本书关注的是我们每个人都会面临的成长课题。通过作者的生活智慧分享，我们可以学会如何激发内在力量，如何在困境中突破自己。

从心理健康的角度来说，书中提供的方法具有很强的实践性。这些都是经过验证的成长方法论，能够帮助我们在自我提升的道路上少走弯路。

愿每个人都能在阅读中找到成长的力量，在人生的修炼路上不断蜕变，成为更好的自己。这就是我们阅读的真正意义。"""
    
    def _generate_illustration_prompt(self, book_info: BookInfo, personality: ReviewerPersonality) -> str:
        """Generate illustration prompt based on book data and personality"""
        
        # Build base prompt with book themes
        themes_text = "经典文学"
        if book_info.themes:
            themes_text = "、".join(book_info.themes[:3])
        
        # Adjust style based on personality
        if personality.id == "academic_scholar":
            style = "学术严谨的古典"
        elif personality.id == "young_reader":
            style = "现代时尚的插画"
        elif personality.id == "entertainment_critic":
            style = "活泼有趣的卡通"
        elif personality.id == "philosophical_thinker":
            style = "深沉哲理的抽象"
        else:
            style = "温暖人文的水墨"
        
        prompt = f"""
        {style}风格的书籍封面设计，以《{book_info.title}》为主题，体现{themes_text}的深刻内涵。
        
        设计要求：
        - 融入中国传统美学元素
        - 体现作品的核心主题和精神内涵
        - 具有{personality.tone}的视觉表达
        - 适合{personality.expertise_level}读者的审美
        - 运用象征性的图案和色彩搭配
        - 整体风格既有现代感又有文化底蕴
        
        画面构成：中央主体图案 + 优雅的文字排版 + 恰当的留白处理
        色调：素雅且富有层次，能够传达{book_info.author}作品的独特气质
        """
        
        return prompt.strip()
    
    def _add_emotion_markers(self, script: str, personality: ReviewerPersonality, book_info: BookInfo) -> str:
        """Add emotion markers to script based on content and personality for Doubao TTS"""
        
        # Define emotion patterns based on content analysis
        emotion_patterns = {
            # Positive emotions
            'happy': ['好书', '精彩', '智慧', '温暖', '美好', '希望', '欢喜', '喜欢', '赞', '棒', '优秀', '杰出'],
            'excited': ['惊喜', '震撼', '太', '真的', '简直', '绝对', '相当', '非常', '特别', '超级'],
            
            # Contemplative emotions  
            'neutral': ['分析', '认为', '思考', '探讨', '研究', '观察', '发现', '理解', '明白', '知道'],
            'cold': ['客观', '理性', '严肃', '深刻', '哲理', '学术', '专业', '理论', '逻辑'],
            
            # Emotional depth
            'sad': ['痛苦', '悲伤', '失落', '孤独', '忧伤', '沉重', '苦难', '眼泪', '哭'],
            'angry': ['愤怒', '不满', '批判', '抗议', '反对', '斗争', '愤慨'],
            'fear': ['恐惧', '害怕', '担心', '忧虑', '紧张', '不安', '焦虑'],
            'surprised': ['惊讶', '意外', '没想到', '竟然', '居然', '原来', '突然'],
            'disgust': ['厌恶', '讨厌', '反感', '恶心', '不齿']
        }
        
        # Get personality-specific emotion preferences
        emotion_mapping = self._get_personality_emotion_mapping(personality)
        
        # Split script into sentences
        sentences = self._split_into_sentences(script)
        marked_sentences = []
        
        for sentence in sentences:
            # Determine appropriate emotion for this sentence
            emotion = self._analyze_sentence_emotion(sentence, emotion_patterns, emotion_mapping, book_info)
            
            # Add emotion marker using Volcengine SSML format
            if emotion and emotion != 'neutral':
                marked_sentence = f'<speak emotion="{emotion}">{sentence}</speak>'
            else:
                marked_sentence = sentence
                
            marked_sentences.append(marked_sentence)
        
        # Join back with proper spacing
        marked_script = ' '.join(marked_sentences)
        
        print(f"🎭 Added emotion markers: {len([s for s in marked_sentences if '<speak' in s])}/{len(sentences)} sentences")
        
        return marked_script
    
    def _get_personality_emotion_mapping(self, personality: ReviewerPersonality) -> dict:
        """Get emotion preferences based on reviewer personality"""
        
        mappings = {
            'dongyu_hui': {
                'primary': ['happy', 'neutral', 'excited'],
                'secondary': ['sad', 'surprised'], 
                'avoid': ['angry', 'disgust']
            },
            'academic_scholar': {
                'primary': ['neutral', 'cold'],
                'secondary': ['surprised', 'happy'],
                'avoid': ['excited', 'angry']
            },
            'emotional_reader': {
                'primary': ['happy', 'sad', 'excited'],
                'secondary': ['surprised', 'fear'],
                'avoid': ['cold', 'angry']
            },
            'practical_reviewer': {
                'primary': ['neutral', 'happy'],
                'secondary': ['excited', 'surprised'],
                'avoid': ['sad', 'fear']
            },
            'entertainment_critic': {
                'primary': ['excited', 'happy', 'surprised'],
                'secondary': ['neutral', 'angry'],
                'avoid': ['sad', 'cold']
            },
            'philosophical_thinker': {
                'primary': ['neutral', 'cold', 'sad'],
                'secondary': ['surprised', 'happy'],
                'avoid': ['excited', 'angry']
            },
            'young_reader': {
                'primary': ['excited', 'happy', 'surprised'],
                'secondary': ['sad', 'angry'],
                'avoid': ['cold', 'fear']
            },
            'life_coach': {
                'primary': ['happy', 'neutral', 'excited'],
                'secondary': ['sad', 'surprised'],
                'avoid': ['angry', 'disgust']
            }
        }
        
        return mappings.get(personality.id, mappings['dongyu_hui'])
    
    def _analyze_sentence_emotion(self, sentence: str, emotion_patterns: dict, 
                                emotion_mapping: dict, book_info: BookInfo) -> str:
        """Analyze sentence to determine appropriate emotion"""
        
        sentence_lower = sentence.lower()
        
        # Count emotion indicators
        emotion_scores = {}
        for emotion, keywords in emotion_patterns.items():
            score = sum(1 for keyword in keywords if keyword in sentence_lower)
            if score > 0:
                emotion_scores[emotion] = score
        
        # If no specific emotion detected, use neutral
        if not emotion_scores:
            return 'neutral'
        
        # Get the emotion with highest score
        top_emotion = max(emotion_scores.items(), key=lambda x: x[1])[0]
        
        # Filter through personality preferences
        if top_emotion in emotion_mapping['avoid']:
            # Try to find an alternative from primary emotions
            for alt_emotion in emotion_mapping['primary']:
                if alt_emotion in emotion_scores:
                    return alt_emotion
            return 'neutral'
        
        # Boost score if it's a preferred emotion
        if top_emotion in emotion_mapping['primary']:
            return top_emotion
        elif top_emotion in emotion_mapping['secondary']:
            return top_emotion
        
        return 'neutral'
    
    def _split_into_sentences(self, text: str) -> list:
        """Split text into sentences for emotion marking"""
        import re
        
        # First, clean up any malformed emotion tags to prevent splitting issues
        # Remove orphaned closing tags
        text = re.sub(r'</speak>\s*(?=[^<])', '', text)  # Remove </speak> not followed by <
        text = re.sub(r'</speak>\s*$', '', text)  # Remove trailing </speak>
        text = re.sub(r'^\s*</speak>', '', text)  # Remove leading </speak>
        
        # Clean up standalone closing tags
        text = re.sub(r'</speak>([。！？；])', r'\1', text)  # Remove </speak> before punctuation
        
        # Split by Chinese punctuation marks
        sentence_pattern = r'([^。！？；]*[。！？；])'
        sentences = re.findall(sentence_pattern, text)
        
        # Handle remaining text without punctuation
        remaining = re.sub(r'[^。！？；]*[。！？；]', '', text).strip()
        if remaining:
            sentences.append(remaining + '。')
        
        # Clean and filter sentences
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            
            # Skip sentences that are just emotion tags or very short
            if (not sentence or 
                len(sentence) < 3 or 
                sentence in ['</speak>', '<speak>', '</speak>。', '<speak>。'] or
                re.match(r'^</?\w+>$', sentence)):
                continue
                
            # Remove any remaining orphaned tags
            sentence = re.sub(r'</speak>', '', sentence)
            sentence = re.sub(r'<speak[^>]*>$', '', sentence)  # Remove incomplete opening tags
            
            sentence = sentence.strip()
            if sentence:
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def _add_emotion_markers_for_content(self, script: str, personality: ReviewerPersonality, semantic_context) -> str:
        """Add emotion markers to content-generated script for enhanced TTS"""
        
        # Enhanced emotion patterns for general content (not just books)
        emotion_patterns = {
            # Positive emotions
            'happy': ['很好', '棒', '优秀', '杰出', '精彩', '智慧', '温暖', '美好', '希望', '欢喜', '喜欢', '赞', '成功', '有趣', '开心', '满意'],
            'excited': ['惊喜', '震撼', '太', '真的', '简直', '绝对', '相当', '非常', '特别', '超级', '厉害', '牛', '酷', '哇', '令人兴奋', '激动'],
            
            # Contemplative emotions  
            'neutral': ['分析', '认为', '思考', '探讨', '研究', '观察', '发现', '理解', '明白', '知道', '看到', '注意到', '发现', '表明', '显示'],
            'cold': ['客观', '理性', '严肃', '深刻', '哲理', '学术', '专业', '理论', '逻辑', '科学', '技术', '数据', '事实'],
            
            # Emotional depth
            'sad': ['痛苦', '悲伤', '失落', '孤独', '忧伤', '沉重', '苦难', '眼泪', '哭', '难过', '遗憾', '可惜', '不幸'],
            'angry': ['愤怒', '不满', '批判', '抗议', '反对', '斗争', '愤慨', '生气', '恼火', '讨厌'],
            'fear': ['恐惧', '害怕', '担心', '忧虑', '紧张', '不安', '焦虑', '危险', '风险', '威胁'],
            'surprised': ['惊讶', '意外', '没想到', '竟然', '居然', '原来', '突然', '出乎意料', '想不到'],
            'disgust': ['厌恶', '讨厌', '反感', '恶心', '不齿', '鄙视']
        }
        
        # Get personality-specific emotion preferences
        emotion_mapping = self._get_personality_emotion_mapping(personality)
        
        # Add domain-specific emotion adjustments based on semantic context
        domain_emotion_boost = self._get_domain_emotion_boost(semantic_context.domain)
        
        # Split script into sentences
        sentences = self._split_into_sentences(script)
        marked_sentences = []
        
        for i, sentence in enumerate(sentences):
            # Determine appropriate emotion for this sentence
            emotion = self._analyze_content_sentence_emotion(
                sentence, emotion_patterns, emotion_mapping, semantic_context, domain_emotion_boost
            )
            
            # Add emotion marker using Volcengine SSML format
            if emotion and emotion != 'neutral':
                marked_sentence = f'<speak emotion="{emotion}">{sentence}</speak>'
                print(f"🎭 Added {emotion} emotion to: {sentence[:30]}...")
            else:
                marked_sentence = sentence
                
            marked_sentences.append(marked_sentence)
        
        # Join back with proper spacing
        marked_script = ' '.join(marked_sentences)
        
        emotion_count = len([s for s in marked_sentences if '<speak' in s])
        print(f"🎭 Added emotion markers: {emotion_count}/{len(sentences)} sentences with emotions")
        
        return marked_script
    
    def _get_domain_emotion_boost(self, domain: str) -> dict:
        """Get emotion preferences based on content domain"""
        domain_boosts = {
            '科技': {
                'excited': 1.5,  # Tech content often exciting
                'neutral': 1.2,  # Professional tone
                'surprised': 1.3  # Innovation surprises
            },
            '商业': {
                'neutral': 1.5,  # Professional
                'excited': 1.2,  # Success stories
                'cold': 1.3      # Analytical
            },
            '教育': {
                'happy': 1.3,    # Encouraging
                'neutral': 1.4,  # Educational tone
                'excited': 1.2   # Learning excitement
            },
            '生活': {
                'happy': 1.5,    # Life content is positive
                'neutral': 1.1,
                'sad': 0.8       # Less sadness in lifestyle
            },
            '文学': {
                'sad': 1.3,      # Literature can be emotional
                'happy': 1.2,
                'neutral': 1.4,
                'cold': 1.2      # Analytical literature
            },
            '通用': {
                'neutral': 1.2,
                'happy': 1.1
            }
        }
        
        return domain_boosts.get(domain, domain_boosts['通用'])
    
    def _analyze_content_sentence_emotion(self, sentence: str, emotion_patterns: dict, 
                                        emotion_mapping: dict, semantic_context, domain_boost: dict) -> str:
        """Analyze sentence emotion for general content with domain awareness"""
        
        sentence_lower = sentence.lower()
        
        # Count emotion indicators
        emotion_scores = {}
        for emotion, keywords in emotion_patterns.items():
            score = sum(1 for keyword in keywords if keyword in sentence_lower)
            if score > 0:
                # Apply domain boost
                boost = domain_boost.get(emotion, 1.0)
                emotion_scores[emotion] = score * boost
        
        # Add context-based emotion scoring
        # If the sentence mentions key concepts from the semantic context, boost excitement
        if semantic_context.key_entities:
            for entity in semantic_context.key_entities[:3]:
                if entity in sentence_lower:
                    emotion_scores['excited'] = emotion_scores.get('excited', 0) + 0.5
        
        # If no specific emotion detected, use neutral
        if not emotion_scores:
            return 'neutral'
        
        # Get the emotion with highest score
        top_emotion = max(emotion_scores.items(), key=lambda x: x[1])[0]
        
        # Filter through personality preferences
        if top_emotion in emotion_mapping['avoid']:
            # Try to find an alternative from primary emotions
            for alt_emotion in emotion_mapping['primary']:
                if alt_emotion in emotion_scores:
                    return alt_emotion
            return 'neutral'
        
        # Boost score if it's a preferred emotion
        if top_emotion in emotion_mapping['primary']:
            return top_emotion
        elif top_emotion in emotion_mapping['secondary']:
            return top_emotion
        
        return 'neutral'
    
    def _extract_basic_entities(self, text: str) -> list:
        """Extract basic entities from text for basic emotion analysis"""
        import re
        
        entities = []
        
        # Extract quoted content
        quoted = re.findall(r'"([^"]+)"|\'([^\']+)\'|《([^》]+)》', text)
        for match in quoted:
            for item in match:
                if item:
                    entities.append(item)
        
        # Extract key technical terms and concepts
        tech_terms = ['AI', '人工智能', '技术', '科技', '算法', '数据', '系统', '平台', '应用']
        business_terms = ['企业', '公司', '市场', '管理', '商业', '经济', '投资', '创业']
        
        text_lower = text.lower()
        for term in tech_terms + business_terms:
            if term in text_lower:
                entities.append(term)
        
        return entities[:5]  # Limit to top 5


