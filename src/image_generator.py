import os
import requests
import textwrap
from typing import Optional
from .config import Config
from .video_config import VideoConfig, AspectRatio

# Import official Volcano Engine SDK
try:
    from volcenginesdkarkruntime import Ark
    VOLCENGINE_SDK_AVAILABLE = True
except ImportError:
    VOLCENGINE_SDK_AVAILABLE = False
    print("Warning: Volcano Engine SDK not available. Install with: pip install 'volcengine-python-sdk[ark]'")

class ImageGenerator:
    def __init__(self):
        self.api_key = Config.DOUBAO_API_KEY
        self.base_url = Config.DOUBAO_IMAGE_ENDPOINT
        self.model_id = Config.DOUBAO_MODEL_ID
        
        # Initialize official SDK client
        if VOLCENGINE_SDK_AVAILABLE:
            self.ark_client = Ark(
                base_url=self.base_url,
                api_key=self.api_key
            )
        else:
            self.ark_client = None
        
    def enhance_prompt_for_aspect_ratio(self, prompt: str, video_config: VideoConfig) -> str:
        """Enhance image prompt based on video aspect ratio"""
        aspect_ratio_prompts = {
            AspectRatio.VERTICAL_9_16: """
        ## 技术规格：
        - 垂直构图比例 9:16，适配移动端书评视频
        - 高分辨率精细制作，支持细节放大观看
        - 现代印刷工艺要求，色彩层次丰富
        - 文字区域预留，便于后期标题添加
        """,
            AspectRatio.HORIZONTAL_16_9: """
        ## 技术规格：
        - 横向构图比例 16:9，适配桌面端播放
        - 宽屏视觉布局，突出主体元素
        - 左右留白平衡，中央聚焦设计
        - 适合大屏幕观看的视觉层次
        """,
            AspectRatio.SQUARE_1_1: """
        ## 技术规格：
        - 正方形构图 1:1，适配社交媒体平台
        - 中心对称设计，四周平衡布局
        - 紧凑构图，充分利用画面空间
        - 适合Instagram等方形显示区域
        """
        }
        
        aspect_spec = aspect_ratio_prompts.get(video_config.aspect_ratio, aspect_ratio_prompts[AspectRatio.VERTICAL_9_16])
        
        # Replace the existing technical specs with aspect-ratio specific ones
        if "## 技术规格：" in prompt:
            parts = prompt.split("## 技术规格：")
            # Find the end of the technical specs section
            after_tech = parts[1].split("## 文化深度：") if "## 文化深度：" in parts[1] else [parts[1], ""]
            enhanced_prompt = parts[0] + aspect_spec + "\n        ## 文化深度：" + after_tech[1] if len(after_tech) > 1 else parts[0] + aspect_spec
        else:
            enhanced_prompt = prompt + aspect_spec
            
        return enhanced_prompt

    def generate_cover_image(self, prompt: str, output_path: str, video_config: VideoConfig = None) -> bool:
        """
        Generate book cover image using official Volcano Engine Doubao Seedream 3.0 SDK
        Returns: True if successful, False otherwise
        """
        
        # Optimize prompt for book cover generation and aspect ratio
        if video_config:
            enhanced_prompt = self.enhance_prompt_for_aspect_ratio(prompt, video_config)
        else:
            enhanced_prompt = prompt
        enhanced_prompt = self._enhance_content_background_prompt(enhanced_prompt, video_config)
        
        # Try official SDK first
        if self.ark_client and VOLCENGINE_SDK_AVAILABLE:
            success = self._generate_with_official_sdk(enhanced_prompt, output_path)
            if success:
                return True
        
        # Fallback to placeholder if SDK fails
        print("Official SDK failed or unavailable, falling back to placeholder image generation...")
        return self._create_placeholder_image(prompt, output_path)
    
    def _generate_with_official_sdk(self, prompt: str, output_path: str) -> bool:
        """Generate image using official Volcano Engine SDK"""
        try:
            print(f"Generating image with official Volcano Engine SDK...")
            print(f"Model: {self.model_id}")
            print(f"Enhanced prompt: {prompt}")
            
            # Use official SDK method - exact format from documentation
            images_response = self.ark_client.images.generate(
                model=self.model_id,
                prompt=prompt
            )
            
            if images_response and images_response.data and len(images_response.data) > 0:
                image_url = images_response.data[0].url
                print(f"Image generated successfully: {image_url}")
                
                # Download the image
                success = self._download_image_from_url(image_url, output_path)
                if success:
                    # Post-process to correct aspect ratio
                    self._resize_to_vertical_format(output_path)
                    return True
            else:
                print("No image data received from API")
                return False
                
        except Exception as e:
            print(f"Error generating image with official SDK: {e}")
            # Check for specific error types
            if "access" in str(e).lower() or "permission" in str(e).lower():
                print("API access issue - check model permissions in Volcano Engine console")
            elif "quota" in str(e).lower() or "limit" in str(e).lower():
                print("Rate limit or quota exceeded")
            elif "invalid" in str(e).lower():
                print("Invalid request parameters")
            return False
    
    def _enhance_content_background_prompt(self, original_prompt: str, video_config: VideoConfig = None) -> str:
        """增强通用内容背景生成提示词，适应各种内容类型"""
        
        # 分析原始提示词，确定内容类型和风格需求
        content_type = self._analyze_content_type(original_prompt)
        
        # 根据内容类型选择合适的设计框架
        if content_type == "文学":
            design_framework = self._get_literary_design_framework()
        elif content_type == "科技":
            design_framework = self._get_tech_design_framework()
        elif content_type == "商业":
            design_framework = self._get_business_design_framework()
        elif content_type == "教育":
            design_framework = self._get_education_design_framework()
        elif content_type == "生活":
            design_framework = self._get_lifestyle_design_framework()
        else:
            design_framework = self._get_general_design_framework()
        
        # 获取正确的视频比例信息
        aspect_ratio = "9:16"  # 默认垂直
        orientation_desc = "垂直"
        device_desc = "移动端"
        
        if video_config:
            if video_config.aspect_ratio.value == "16:9":
                aspect_ratio = "16:9"
                orientation_desc = "横向"
                device_desc = "桌面端或电视"
            elif video_config.aspect_ratio.value == "1:1":
                aspect_ratio = "1:1"
                orientation_desc = "正方形"
                device_desc = "社交媒体平台"
        
        # 原始提示词深度扩展
        expanded_prompt = f"""
        {design_framework}
        
        ## 具体设计要求：
        基于以下创意描述进行深度视觉设计：
        {original_prompt}
        
        ## 技术规格：
        - {orientation_desc}构图比例 {aspect_ratio}，适配{device_desc}视频
        - 高分辨率精细制作，支持细节放大观看
        - 现代视觉工艺要求，色彩层次丰富
        - 文字区域预留，便于后期内容添加
        - 适合作为视频背景使用
        
        ## 视觉品质：
        - 体现内容主题的核心精神和价值
        - 融入当代设计理念，展现时代美学
        - 承载内容的深度思考和文化内涵
        - 启发观众的视觉感知和情感共鸣
        """
        
        return expanded_prompt
    
    def _analyze_content_type(self, prompt: str) -> str:
        """分析提示词内容类型"""
        prompt_lower = prompt.lower()
        
        # 文学类型检测
        if any(keyword in prompt_lower for keyword in 
               ['书', '文学', '小说', '诗歌', '散文', '作家', '阅读', '书评']):
            return "文学"
        
        # 科技类型检测
        elif any(keyword in prompt_lower for keyword in 
                ['科技', 'ai', '人工智能', '技术', '算法', '数字', '创新', '未来']):
            return "科技"
        
        # 商业类型检测
        elif any(keyword in prompt_lower for keyword in 
                ['商业', '经济', '管理', '市场', '企业', '投资', '金融', '营销']):
            return "商业"
        
        # 教育类型检测
        elif any(keyword in prompt_lower for keyword in 
                ['教育', '学习', '知识', '培训', '课程', '教学', '学校', '学术']):
            return "教育"
        
        # 生活类型检测
        elif any(keyword in prompt_lower for keyword in 
                ['生活', '健康', '美食', '旅行', '运动', '娱乐', '时尚', '文化']):
            return "生活"
        
        else:
            return "通用"
    
    def _get_literary_design_framework(self) -> str:
        """文学类内容设计框架"""
        return """
        专业文学内容视觉设计，融合中华文化美学与现代视觉理念。
        
        ## 视觉美学要求：
        - 采用经典文学色彩：墨色、朱砂、藏青、米白等典雅色调
        - 运用留白艺术，营造书香气息的视觉效果
        - 融入书法、印章、水墨等文化美学元素
        - 体现文学作品的意境深远与内敛含蓄
        
        ## 文化表达：
        - 通过视觉符号传达文学作品的精神内核
        - 体现深厚的文学传统和人文底蕴
        - 展现古典与现代的和谐统一
        - 承载文学的哲学思考和人文关怀
        """
    
    def _get_tech_design_framework(self) -> str:
        """科技类内容设计框架"""
        return """
        现代科技内容视觉设计，体现创新与未来感。
        
        ## 视觉美学要求：
        - 采用科技感色彩：深蓝、银灰、青色、白色等现代色调
        - 运用几何线条，营造科技感和精准性
        - 融入电路、网络、数据等科技元素
        - 体现技术的精确性与未来感
        
        ## 设计理念：
        - 通过视觉语言传达科技创新的力量
        - 体现数字时代的智能与高效
        - 展现科技与人文的融合
        - 承载对未来发展的思考和展望
        """
    
    def _get_business_design_framework(self) -> str:
        """商业类内容设计框架"""
        return """
        专业商业内容视觉设计，体现权威与专业感。
        
        ## 视觉美学要求：
        - 采用商务色彩：深灰、金色、蓝色、白色等专业色调
        - 运用简洁线条，营造专业权威感
        - 融入图表、数据、城市等商业元素
        - 体现商业的严谨性与前瞻性
        
        ## 设计理念：
        - 通过视觉传达商业智慧和洞察
        - 体现市场经济的活力与机遇
        - 展现商业与社会的价值创造
        - 承载对经济发展的分析和思考
        """
    
    def _get_education_design_framework(self) -> str:
        """教育类内容设计框架"""
        return """
        教育内容视觉设计，体现知识传承与启发。
        
        ## 视觉美学要求：
        - 采用教育色彩：浅蓝、绿色、橙色、白色等清新色调
        - 运用温和曲线，营造亲和学习氛围
        - 融入书本、灯泡、树木等教育元素
        - 体现知识的温暖性与启发性
        
        ## 设计理念：
        - 通过视觉激发学习兴趣和求知欲
        - 体现教育的人文关怀和成长价值
        - 展现知识与实践的有机结合
        - 承载对人才培养的理念和期望
        """
    
    def _get_lifestyle_design_framework(self) -> str:
        """生活类内容设计框架"""
        return """
        生活内容视觉设计，体现温暖与人文关怀。
        
        ## 视觉美学要求：
        - 采用生活色彩：暖黄、粉色、绿色、米白等温馨色调
        - 运用自然曲线，营造轻松舒适氛围
        - 融入自然、家庭、情感等生活元素
        - 体现生活的美好性与真实感
        
        ## 设计理念：
        - 通过视觉传达生活的美好和温暖
        - 体现人文关怀和情感共鸣
        - 展现生活与理想的平衡统一
        - 承载对美好生活的向往和追求
        """
    
    def _get_general_design_framework(self) -> str:
        """通用内容设计框架"""
        return """
        通用内容视觉设计，体现简约与现代美学。
        
        ## 视觉美学要求：
        - 采用和谐色彩：中性色调配合主题色彩
        - 运用简洁构图，营造清晰专业氛围
        - 融入几何、抽象等现代设计元素
        - 体现内容的专业性与可读性
        
        ## 设计理念：
        - 通过视觉支撑内容的核心价值
        - 体现现代设计的简约与美感
        - 展现内容与形式的完美结合
        - 承载对品质生活的审美追求
        """
    
    def _download_image_from_url(self, url: str, output_path: str) -> bool:
        """Download image from URL"""
        try:
            print(f"Downloading image from: {url}")
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"Image successfully downloaded to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error downloading image from URL: {e}")
            return False
    
    def _resize_to_vertical_format(self, image_path: str) -> bool:
        """Resize image to vertical 9:16 format for mobile video"""
        try:
            from PIL import Image
            
            # Open the downloaded image
            with Image.open(image_path) as img:
                # Target dimensions for vertical video
                target_width = 1080
                target_height = 1920
                
                # Get current dimensions
                current_width, current_height = img.size
                
                # Calculate scaling to fit within target while maintaining aspect ratio
                width_ratio = target_width / current_width
                height_ratio = target_height / current_height
                scale_ratio = min(width_ratio, height_ratio)
                
                # Calculate new dimensions
                new_width = int(current_width * scale_ratio)
                new_height = int(current_height * scale_ratio)
                
                # Resize the image
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Create a new image with target dimensions and paste the resized image
                final_img = Image.new('RGB', (target_width, target_height), color='#2c3e50')
                
                # Calculate position to center the image
                x_offset = (target_width - new_width) // 2
                y_offset = (target_height - new_height) // 2
                
                # Paste the resized image onto the background
                final_img.paste(resized_img, (x_offset, y_offset))
                
                # Save the final image
                final_img.save(image_path, 'JPEG', quality=95)
                print(f"Image resized to vertical format: {target_width}x{target_height}")
                
            return True
            
        except Exception as e:
            print(f"Error resizing image: {e}")
            return False
    
    def _create_placeholder_image(self, prompt: str, output_path: str) -> bool:
        """Create a high-quality placeholder image if API fails"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Create image with gradient background
            width, height = 1080, 1920
            img = Image.new('RGB', (width, height), color='#1a1a2e')
            draw = ImageDraw.Draw(img)
            
            # Create a sophisticated gradient background
            for y in range(height):
                ratio = y / height
                # Beautiful gradient from dark purple to deep blue
                r = int(26 + ratio * 44)   # 26 -> 70
                g = int(26 + ratio * 56)   # 26 -> 82
                b = int(46 + ratio * 84)   # 46 -> 130
                draw.rectangle([(0, y), (width, y+1)], fill=(r, g, b))
            
            # Add subtle texture overlay
            self._add_texture_overlay(draw, width, height)
            
            # Try to load Chinese font
            try:
                title_font = ImageFont.truetype(Config.FONT_PATH, 90)
                subtitle_font = ImageFont.truetype(Config.FONT_PATH, 55)
                desc_font = ImageFont.truetype(Config.FONT_PATH, 40)
            except:
                try:
                    # macOS system Chinese font
                    title_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 90)
                    subtitle_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 55)
                    desc_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 40)
                except:
                    # Fallback to default font
                    title_font = ImageFont.load_default()
                    subtitle_font = ImageFont.load_default()
                    desc_font = ImageFont.load_default()
            
            # Main title with elegant styling
            title = "AI 智能书评"
            self._draw_text_with_shadow(draw, title, title_font, width, height//2 - 250, '#ffffff', shadow_offset=3)
            
            # Subtitle
            subtitle = "专业 • 智能 • 精美"
            self._draw_text_with_shadow(draw, subtitle, subtitle_font, width, height//2 - 150, '#e8e8e8', shadow_offset=2)
            
            # Add decorative line
            line_y = height//2 - 80
            draw.rectangle([(width//2 - 150, line_y), (width//2 + 150, line_y + 3)], fill='#ffffff')
            
            # Description based on prompt (truncated and wrapped)
            if len(prompt) > 120:
                prompt = prompt[:120] + "..."
            
            # Wrap the prompt text nicely
            wrapped_text = textwrap.fill(prompt, width=22)
            lines = wrapped_text.split('\n')
            
            y_offset = height//2 + 20
            for line in lines[:4]:  # Limit to 4 lines
                self._draw_text_with_shadow(draw, line, desc_font, width, y_offset, '#d0d0d0', shadow_offset=1)
                y_offset += 55
            
            # Add decorative elements
            self._add_decorative_elements(draw, width, height)
            
            # Save image with high quality
            img.save(output_path, 'JPEG', quality=95, optimize=True)
            print(f"High-quality placeholder image created: {output_path}")
            return True
            
        except Exception as e:
            print(f"Error creating placeholder image: {e}")
            return False
    
    def _add_texture_overlay(self, draw, width, height):
        """Add subtle texture overlay for visual appeal"""
        # Add subtle diagonal lines for texture
        for i in range(0, width + height, 60):
            alpha = 15
            color = (255, 255, 255, alpha)
            draw.line([(i, 0), (i - height, height)], fill=color[:3], width=1)
    
    def _draw_text_with_shadow(self, draw, text, font, width, y, color, shadow_offset=2):
        """Draw text with shadow for better readability"""
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        x = (width - text_width) // 2
        
        # Draw shadow
        shadow_color = '#000000'
        for offset in range(1, shadow_offset + 1):
            draw.text((x + offset, y + offset), text, fill=shadow_color, font=font)
        
        # Draw main text
        draw.text((x, y), text, fill=color, font=font)
    
    def _add_decorative_elements(self, draw, width, height):
        """Add elegant decorative elements"""
        center_x, center_y = width // 2, height // 2
        
        # Draw elegant circles
        for i in range(3):
            radius = 120 + i * 90
            alpha = 25 - i * 8
            color = (255, 255, 255, alpha)
            draw.ellipse([
                (center_x - radius, center_y - radius),
                (center_x + radius, center_y + radius)
            ], outline=color[:3], width=2)
        
        # Add corner decorative elements
        corner_size = 80
        corners = [
            (50, 50), (width - corner_size - 50, 50),
            (50, height - corner_size - 50), (width - corner_size - 50, height - corner_size - 50)
        ]
        
        for x, y in corners:
            # Outer frame
            draw.rectangle([x, y, x + corner_size, y + corner_size], outline='#ffffff', width=2)
            # Inner frame
            inner_offset = 15
            draw.rectangle([
                x + inner_offset, y + inner_offset,
                x + corner_size - inner_offset, y + corner_size - inner_offset
            ], outline='#ffffff', width=1)
            
            # Add small decorative dots
            dot_size = 4
            draw.ellipse([
                x + corner_size//2 - dot_size//2, y + corner_size//2 - dot_size//2,
                x + corner_size//2 + dot_size//2, y + corner_size//2 + dot_size//2
            ], fill='#ffffff')