import asyncio
import websockets
import json
import gzip
import uuid
import copy
import os
from typing import Dict, Optional
from .config import Config

class DoubaoTTS:
    """Volcengine (ByteDance Doubao) TTS Service Implementation"""
    
    def __init__(self, app_id: str = None, access_token: str = None):
        # Updated configuration based on WebSocket demo
        self.app_id = app_id or Config.DOUBAO_APP_ID
        self.access_token = access_token or Config.DOUBAO_ACCESS_TOKEN
        self.host = "openspeech.bytedance.com"
        self.api_url = f"wss://{self.host}/api/v1/tts/ws_binary"
        self.cluster = "volcano_tts"
        
        # WebSocket protocol header (from demo)
        # version: b0001 (4 bits) + header size: b0001 (4 bits) + 
        # message type: b0001 (Full client request) (4bits) + message type specific flags: b0000 (none) (4bits) +
        # message serialization method: b0001 (JSON) (4 bits) + message compression: b0001 (gzip) (4bits) +
        # reserved data: 0x00 (1 byte)
        self.default_header = bytearray(b'\x11\x10\x11\x00')
        
        # Check if service is properly configured
        self.is_configured = bool(self.app_id and self.access_token)
        if not self.is_configured:
            print("⚠️ Doubao TTS not configured: Missing app_id or access_token")
        
        # Confirmed working voice types for app_id 2588986021 (TESTED AND VERIFIED)
        self.voices = {
            # Multi-emotion voices (多情感音色) - VERIFIED WORKING
            "zh_female_roumeinvyou_emo_v2_mars_bigtts": {
                "name": "柔美女友（多情感）", 
                "description": "温柔甜美的女声，情感丰富细腻 ✅已验证",
                "gender": "female",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "hate", "excited", "coldness", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "verified": True
            },
            "zh_male_beijingxiaoye_emo_v2_mars_bigtts": {
                "name": "北京小爷（多情感）",
                "description": "北京口音的男声，支持多种情感表达 ✅已验证",
                "gender": "male",
                "accent": "北京",
                "emotions": ["angry", "surprised", "fear", "excited", "coldness", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "verified": True
            },
            
            # Additional multi-emotion voices (user provided list)
            "zh_male_yangguangqingnian_emo_v2_mars_bigtts": {
                "name": "阳光青年（多情感）",
                "description": "阳光活泼的青年男声，情感表达自然",
                "gender": "male",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "fear", "excited", "coldness", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion"
            },
            "zh_female_meilinvyou_emo_v2_mars_bigtts": {
                "name": "魅力女友（多情感）",
                "description": "成熟魅力的女声，情感层次丰富",
                "gender": "female",
                "accent": "标准",
                "emotions": ["sad", "fear", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion"
            },
            "zh_female_shuangkuaisisi_emo_v2_mars_bigtts": {
                "name": "爽快思思（多情感）",
                "description": "爽朗直率的女声，情感表达鲜明",
                "gender": "female",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "surprised", "excited", "coldness", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "languages": ["chinese", "english"]
            },
            "zh_female_tianxinxiaomei_emo_v2_mars_bigtts": {
                "name": "甜心小美（多情感）",
                "description": "甜美可爱的女声，情感表达生动",
                "gender": "female",
                "accent": "标准",
                "emotions": ["sad", "fear", "hate", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion"
            },
            "zh_female_gaolengyujie_emo_v2_mars_bigtts": {
                "name": "高冷御姐（多情感）",
                "description": "成熟冷艳的女性声音，支持全情感",
                "gender": "female",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "hate", "excited", "coldness", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_aojiaobazong_emo_v2_mars_bigtts": {
                "name": "傲娇霸总（多情感）",
                "description": "霸道总裁角色声音，支持多情感",
                "gender": "male",
                "accent": "标准",
                "emotions": ["neutral", "happy", "angry", "hate"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_guangzhoudege_emo_mars_bigtts": {
                "name": "广州德哥（多情感）",
                "description": "广东口音，地方特色浓厚，支持情感",
                "gender": "male",
                "accent": "广东",
                "emotions": ["angry", "fear", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_jingqiangkanye_emo_mars_bigtts": {
                "name": "京腔侃爷（多情感）",
                "description": "北京口音，支持多情感表达",
                "gender": "male",
                "accent": "北京",
                "emotions": ["happy", "angry", "surprised", "hate", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_female_linjuayi_emo_v2_mars_bigtts": {
                "name": "邻居阿姨（多情感）",
                "description": "亲切温和的邻家女声，支持情感",
                "gender": "female",
                "accent": "标准",
                "emotions": ["neutral", "angry", "coldness", "sad", "surprised"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_yourougongzi_emo_v2_mars_bigtts": {
                "name": "优柔公子（多情感）",
                "description": "温文尔雅的男声，情感细腻",
                "gender": "male",
                "accent": "标准",
                "emotions": ["happy", "angry", "fear", "hate", "excited", "neutral", "sad"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_ruyayichen_emo_v2_mars_bigtts": {
                "name": "儒雅男友（多情感）",
                "description": "儒雅温和的男友声音，情感丰富",
                "gender": "male",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "fear", "excited", "coldness", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_junlangnanyou_emo_v2_mars_bigtts": {
                "name": "俊朗男友（多情感）",
                "description": "俊朗帅气的男友声音，支持多情感",
                "gender": "male",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "neutral"],
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            },
            "zh_male_lengkugege_emo_v2_mars_bigtts": {
                "name": "冷酷哥哥（多情感）",
                "description": "冷酷帅气的哥哥声音，支持情感表达",
                "gender": "male",
                "accent": "标准",
                "emotions": ["neutral", "coldness", "angry"],  # Likely emotions based on character
                "emotion_support": True,
                "category": "multi_emotion",
                "source": "剪映"
            }
            
            # Note: These voices may require additional permissions
            # Test with your app_id using test_working_voices.py before using
        }
    
    def _create_request_json(self, text: str, voice_id: str, emotion: str) -> dict:
        """Create request JSON following WebSocket demo format"""
        request_json = {
            "app": {
                "appid": self.app_id,
                "token": self.access_token,
                "cluster": self.cluster
            },
            "user": {
                "uid": "default_user"
            },
            "audio": {
                "voice_type": voice_id,
                "encoding": "mp3",
                "speed_ratio": 1.0,
                "volume_ratio": 1.0,
                "pitch_ratio": 1.0
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "submit"
            }
        }
        
        # Add emotion only if voice supports it and emotion is not neutral
        if emotion and emotion != "neutral" and voice_id in self.voices:
            voice_info = self.voices[voice_id]
            if voice_info.get("emotion_support") and emotion in voice_info.get("emotions", []):
                request_json["audio"]["emotion"] = emotion
        
        return request_json
    
    def _create_websocket_message(self, request_json: dict) -> bytearray:
        """Create WebSocket binary message from JSON request"""
        try:
            # Convert to JSON bytes
            json_str = json.dumps(request_json, ensure_ascii=False)
            payload_bytes = json_str.encode('utf-8')
            
            # Compress with gzip
            payload_bytes = gzip.compress(payload_bytes)
            
            # Create full message: header + payload size + payload
            full_message = bytearray(self.default_header)
            full_message.extend((len(payload_bytes)).to_bytes(4, 'big'))  # payload size (4 bytes)
            full_message.extend(payload_bytes)  # payload
            return full_message
            
        except Exception as e:
            print(f"❌ Error creating WebSocket message: {e}")
            raise
    
    async def _synthesize_speech_async(self, text: str, voice_id: str = "zh_female_roumeinvyou_emo_v2_mars_bigtts", emotion: str = "neutral") -> Optional[bytes]:
        """Synthesize speech using WebSocket protocol"""
        if not self.is_configured:
            print("❌ Doubao TTS not configured, skipping synthesis")
            return None
            
        try:
            print(f"🔗 Doubao TTS WebSocket: {self.api_url}")
            print(f"📝 Voice: {voice_id}, Emotion: {emotion}")
            
            # Create request JSON
            request_json = self._create_request_json(text, voice_id, emotion)
            
            # Create WebSocket message
            message = self._create_websocket_message(request_json)
            
            # WebSocket headers
            headers = {"Authorization": f"Bearer; {self.access_token}"}
            
            # Connect and send request
            audio_data = bytearray()
            
            try:
                async with websockets.connect(self.api_url, additional_headers=headers, ping_interval=None) as ws:
                    await ws.send(message)
                    
                    while True:
                        response = await ws.recv()
                        
                        # Parse response using demo logic
                        done, chunk = self._parse_websocket_response(response)
                        
                        if chunk:
                            audio_data.extend(chunk)
                        
                        if done:
                            break
                            
            except websockets.exceptions.ConnectionClosedError as e:
                print(f"❌ WebSocket connection closed: {e}")
                return None
            except websockets.exceptions.InvalidStatusCode as e:
                print(f"❌ WebSocket invalid status: {e}")
                return None
            
            if len(audio_data) > 0:
                print(f"✅ Doubao TTS WebSocket success: {len(audio_data)} bytes")
                return bytes(audio_data)
            else:
                print("❌ No audio data received from WebSocket")
                return None
                
        except Exception as e:
            print(f"❌ Doubao TTS WebSocket exception: {e}")
            return None
    
    def _parse_websocket_response(self, response: bytes) -> tuple[bool, Optional[bytes]]:
        """Parse WebSocket response following demo format"""
        try:
            # Parse header
            message_type = response[1] >> 4
            message_type_specific_flags = response[1] & 0x0f
            header_size = response[0] & 0x0f
            
            # Extract payload
            payload = response[header_size * 4:]
            
            if message_type == 0xb:  # audio-only server response
                if message_type_specific_flags == 0:  # no sequence number as ACK
                    return False, None
                else:
                    sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                    payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                    audio_chunk = payload[8:]
                    
                    # Check if this is the last chunk
                    done = sequence_number < 0
                    return done, audio_chunk
                    
            elif message_type == 0xf:  # error message
                code = int.from_bytes(payload[:4], "big", signed=False)
                msg_size = int.from_bytes(payload[4:8], "big", signed=False)
                error_msg = payload[8:]
                
                # Decompress if needed
                message_compression = response[2] & 0x0f
                if message_compression == 1:
                    error_msg = gzip.decompress(error_msg)
                
                error_text = error_msg.decode('utf-8')
                if "resource not granted" in error_text:
                    print(f"❌ Doubao TTS: Resource permissions not granted for app_id {self.app_id}")
                else:
                    print(f"❌ Doubao TTS WebSocket error {code}: {error_text}")
                return True, None
                
            else:
                print(f"⚠️ Unknown WebSocket message type: {message_type}")
                return False, None
                
        except Exception as e:
            print(f"❌ Error parsing WebSocket response: {e}")
            return True, None
    
    def synthesize_speech(self, text: str, voice_id: str = "zh_female_roumeinvyou_emo_v2_mars_bigtts", emotion: str = "neutral") -> Optional[bytes]:
        """Synthesize speech using Doubao TTS WebSocket API"""
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self._synthesize_speech_async(text, voice_id, emotion))
        finally:
            loop.close()
    
    def synthesize_to_file(self, text: str, output_path: str, voice_id: str = "zh_female_roumeinvyou_emo_v2_mars_bigtts", emotion: str = "neutral") -> bool:
        """Synthesize speech and save to file"""
        # Check if service is configured
        if not self.is_configured:
            print("❌ Doubao TTS not configured, cannot synthesize to file")
            return False
            
        try:
            print(f"🔊 Doubao TTS: Generating '{text[:50]}...' with voice {voice_id}")
            
            audio_data = self.synthesize_speech(text, voice_id, emotion)
            
            if audio_data and len(audio_data) > 0:
                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # Write audio data to file
                with open(output_path, 'wb') as f:
                    f.write(audio_data)
                
                print(f"✅ Doubao TTS: Generated {len(audio_data)} bytes -> {output_path}")
                return True
            else:
                print(f"❌ Doubao TTS: No audio data received")
                return False
                
        except Exception as e:
            print(f"❌ Doubao TTS file save error: {e}")
            return False
    
    def get_available_voices(self) -> Dict[str, Dict]:
        """Get available voice configurations"""
        return self.voices.copy()
    
    def validate_voice(self, voice_id: str) -> bool:
        """Check if voice ID is valid"""
        return voice_id in self.voices
    
    def get_voice_info(self, voice_id: str) -> Optional[Dict]:
        """Get voice information"""
        return self.voices.get(voice_id)
    
    def test_connection(self) -> bool:
        """Test TTS service connectivity"""
        # Check if service is configured first
        if not self.is_configured:
            print("❌ Doubao TTS credentials not configured (app_id/access_token)")
            return False
            
        try:
            print("🔍 Testing Doubao TTS connectivity...")
            print(f"🔑 App ID: {self.app_id[:8]}...")
            print(f"🎫 Token: {self.access_token[:8]}...")
            
            # Test with simple text
            test_text = "你好"
            audio_data = self.synthesize_speech(test_text, "zh_female_roumeinvyou_emo_v2_mars_bigtts", "neutral")
            
            if audio_data is not None and len(audio_data) > 0:
                print("✅ Doubao TTS connectivity test passed")
                return True
            else:
                print("❌ Doubao TTS connectivity test failed: No audio data")
                return False
                
        except Exception as e:
            print(f"❌ Doubao TTS connection test failed: {e}")
            return False
    
    def _clean_emotion_markers(self, text: str) -> str:
        """Clean and normalize emotion markers in text"""
        import re
        
        # Step 1: Fix common malformed patterns first
        # Fix missing angle brackets: speak emotion"happy"text -> <speak emotion="happy">text</speak>
        # Handle multiple cases more thoroughly
        text = re.sub(r'speak emotion"([^"]*)"([^<]*?)(?=speak emotion)', r'<speak emotion="\1">\2</speak>', text)
        text = re.sub(r'speak emotion"([^"]*)"([^<]*?)(?=<speak)', r'<speak emotion="\1">\2</speak>', text)
        text = re.sub(r'speak emotion"([^"]*)"([^<]*?)$', r'<speak emotion="\1">\2</speak>', text)
        
        # Step 2: Process text more intelligently
        # Split text into tagged and untagged segments
        parts = re.split(r'(<speak emotion="[^"]+">.*?</speak>)', text)
        processed_parts = []
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            if '<speak emotion=' in part and '</speak>' in part:
                # This is a valid emotion tag, keep it
                processed_parts.append(part)
            else:
                # This is untagged text, wrap in neutral if it has content
                # Remove any remaining malformed tags first
                clean_part = re.sub(r'<[^>]*>', '', part)
                clean_part = re.sub(r'\s+', ' ', clean_part).strip()
                if clean_part:
                    processed_parts.append(f'<speak emotion="neutral">{clean_part}</speak>')
        
        # Join all processed parts
        result = ' '.join(processed_parts) if processed_parts else ''
        
        return result
    
    def synthesize_emotion_marked_text(self, text: str, output_path: str, voice_id: str = "zh_female_roumeinvyou_emo_v2_mars_bigtts") -> bool:
        """Synthesize speech from text containing emotion markers"""
        import re
        
        # Clean and normalize the text first
        cleaned_text = self._clean_emotion_markers(text)
        
        # Parse emotion markers from cleaned text like <speak emotion="happy">text</speak>
        emotion_pattern = r'<speak emotion="([^"]+)">([^<]+)</speak>'
        matches = re.findall(emotion_pattern, cleaned_text)
        
        if not matches:
            # No valid emotion markers found, strip all tags and use neutral emotion
            final_text = re.sub(r'<[^>]*>', '', cleaned_text)  # Remove any remaining tags
            final_text = re.sub(r'\s+', ' ', final_text).strip()  # Clean whitespace
            print(f"🔄 No valid emotion markers, using neutral synthesis for: {final_text[:50]}...")
            return self.synthesize_to_file(final_text, output_path, voice_id, "neutral")
        
        # If we have emotion markers, process them
        print(f"🎯 Found {len(matches)} emotion segments to process")
        
        try:
            from pydub import AudioSegment
            combined_audio = AudioSegment.empty()
            
            # Process each emotion segment
            for i, (emotion, segment_text) in enumerate(matches):
                segment_text = segment_text.strip()
                if not segment_text:
                    print(f"⏭️ Skipping empty segment {i+1}")
                    continue
                    
                print(f"🎭 Processing emotion segment {i+1}/{len(matches)}: '{emotion}' for '{segment_text[:30]}...'")
                
                # Validate emotion for this voice
                voice_info = self.voices.get(voice_id, {})
                supported_emotions = voice_info.get("emotions", [])
                
                # Use supported emotion or fallback to neutral
                if voice_info.get("emotion_support") and emotion in supported_emotions:
                    use_emotion = emotion
                    print(f"✅ Using supported emotion: {emotion}")
                else:
                    use_emotion = "neutral"
                    if emotion != "neutral":
                        print(f"⚠️ Emotion '{emotion}' not supported by voice, using neutral")
                
                # Create temporary file for this segment
                import tempfile
                import uuid
                temp_file = os.path.join(tempfile.gettempdir(), f"emotion_segment_{uuid.uuid4().hex[:8]}.mp3")
                
                # Synthesize this segment with the specified emotion
                success = self.synthesize_to_file(segment_text, temp_file, voice_id, use_emotion)
                
                if success and os.path.exists(temp_file):
                    # Add to combined audio
                    segment_audio = AudioSegment.from_file(temp_file)
                    combined_audio += segment_audio
                    
                    # Clean up temp file
                    os.remove(temp_file)
                else:
                    print(f"⚠️ Failed to synthesize emotion segment with emotion '{emotion}', trying neutral")
                    # Fallback to neutral emotion
                    success = self.synthesize_to_file(segment_text.strip(), temp_file, voice_id, "neutral")
                    if success and os.path.exists(temp_file):
                        segment_audio = AudioSegment.from_file(temp_file)
                        combined_audio += segment_audio
                        os.remove(temp_file)
            
            # Export combined audio
            if len(combined_audio) > 0:
                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                combined_audio.export(output_path, format="mp3")
                print(f"✅ Emotion-marked synthesis complete: {output_path}")
                return True
            else:
                print("❌ No audio segments were successfully generated")
                return False
                
        except ImportError:
            print("❌ pydub not available for emotion synthesis, falling back to simple synthesis")
            # Fallback: strip emotion markers and synthesize normally
            clean_text = re.sub(r'<speak[^>]*>([^<]+)</speak>', r'\1', text)
            return self.synthesize_to_file(clean_text, output_path, voice_id, "neutral")
        except Exception as e:
            print(f"❌ Error in emotion synthesis: {e}")
            # Fallback: strip emotion markers and synthesize normally
            clean_text = re.sub(r'<speak[^>]*>([^<]+)</speak>', r'\1', text)
            return self.synthesize_to_file(clean_text, output_path, voice_id, "neutral")