"""
Research API
深度研究系统的Web API接口

提供RESTful API支持多种研究场景和媒体格式的生成
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
import os
from pathlib import Path
import uuid
from dataclasses import asdict

# 导入核心组件
try:
    from research_to_media_pipeline import ResearchToMediaPipeline, MediaGenerationRequest, MediaFormat
    from universal_research_template import ResearchRequest, ResearchScenario
    from deep_research_agent import DeepResearchAgent
except ImportError:
    # 如果相对导入失败，尝试添加路径
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent))
    from research_to_media_pipeline import ResearchToMediaPipeline, MediaGenerationRequest, MediaFormat
    from universal_research_template import ResearchRequest, ResearchScenario
    from deep_research_agent import DeepResearchAgent

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 全局组件
pipeline = ResearchToMediaPipeline()
research_agent = DeepResearchAgent()

# 任务状态存储
task_status = {}

class TaskStatus:
    """任务状态管理"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

def run_async(func):
    """异步函数装饰器"""
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(func(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

@app.route('/api/research/scenarios', methods=['GET'])
def get_research_scenarios():
    """获取支持的研究场景"""
    try:
        scenarios = research_agent.get_supported_scenarios()
        scenario_details = {}
        
        for scenario in scenarios:
            scenario_enum = ResearchScenario(scenario)
            scenario_info = research_agent.template_system.get_scenario_info(scenario_enum)
            scenario_details[scenario] = {
                "name": scenario,
                "description": scenario_info.get("research_type", ""),
                "analysis_depth": scenario_info.get("analysis_depth", ""),
                "task_count": len(scenario_info.get("tasks", []))
            }
        
        return jsonify({
            "success": True,
            "scenarios": scenario_details
        })
    except Exception as e:
        logger.error(f"Get scenarios failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/media-formats', methods=['GET'])
def get_media_formats():
    """获取支持的媒体格式"""
    try:
        formats = {}
        for format_enum in MediaFormat:
            formats[format_enum.value] = {
                "name": format_enum.value,
                "description": get_format_description(format_enum)
            }
        
        return jsonify({
            "success": True,
            "formats": formats
        })
    except Exception as e:
        logger.error(f"Get media formats failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

def get_format_description(format_enum: MediaFormat) -> str:
    """获取格式描述"""
    descriptions = {
        MediaFormat.REPORT: "详细的研究报告文档",
        MediaFormat.VIDEO_SCRIPT: "视频制作脚本",
        MediaFormat.PODCAST_SCRIPT: "播客录制脚本",
        MediaFormat.VIDEO: "完整的视频文件",
        MediaFormat.AUDIO: "音频文件",
        MediaFormat.INTERACTIVE_REPORT: "交互式网页报告"
    }
    return descriptions.get(format_enum, "未知格式")

@app.route('/api/research/generate', methods=['POST'])
def generate_research():
    """生成研究内容"""
    try:
        data = request.json
        
        # 验证必需参数
        if not data.get('target'):
            return jsonify({"success": False, "error": "目标主题不能为空"}), 400
        
        if not data.get('scenario'):
            return jsonify({"success": False, "error": "研究场景不能为空"}), 400
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        
        # 解析请求参数
        scenario = ResearchScenario(data['scenario'])
        target = data['target']
        audience = data.get('audience', 'general')
        output_format = data.get('output_format', 'report')
        
        # 创建研究请求
        research_request = ResearchRequest(
            scenario=scenario,
            target=target,
            audience=audience,
            output_format=output_format,
            additional_requirements=data.get('additional_requirements', {})
        )
        
        # 创建媒体生成请求
        media_format = MediaFormat(data.get('media_format', 'report'))
        media_request = MediaGenerationRequest(
            research_request=research_request,
            target_format=media_format,
            style_preferences=data.get('style_preferences', {}),
            video_config=data.get('video_config'),
            audio_config=data.get('audio_config')
        )
        
        # 初始化任务状态
        task_status[task_id] = {
            "status": TaskStatus.PENDING,
            "progress": 0,
            "message": "任务已创建，等待处理",
            "created_at": datetime.now().isoformat(),
            "request": asdict(research_request)
        }
        
        # 异步处理任务
        import threading
        thread = threading.Thread(target=process_research_task, args=(task_id, media_request))
        thread.start()
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "message": "研究任务已创建，正在处理中"
        })
        
    except Exception as e:
        logger.error(f"Generate research failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

def process_research_task(task_id: str, media_request: MediaGenerationRequest):
    """处理研究任务"""
    try:
        # 更新状态
        task_status[task_id]["status"] = TaskStatus.PROCESSING
        task_status[task_id]["progress"] = 10
        task_status[task_id]["message"] = "开始深度研究..."
        
        # 运行异步任务
        @run_async
        async def run_task():
            return await pipeline.process_research_to_media(media_request)
        
        # 更新进度
        task_status[task_id]["progress"] = 50
        task_status[task_id]["message"] = "正在生成媒体内容..."
        
        # 执行任务
        result = run_task()
        
        # 更新完成状态
        task_status[task_id]["status"] = TaskStatus.COMPLETED
        task_status[task_id]["progress"] = 100
        task_status[task_id]["message"] = "任务完成"
        task_status[task_id]["result"] = {
            "format": result.format.value,
            "quality_score": result.quality_score,
            "processing_time": result.processing_time,
            "media_files": result.media_files,
            "content_preview": result.primary_content[:500] + "..." if len(result.primary_content) > 500 else result.primary_content
        }
        task_status[task_id]["completed_at"] = datetime.now().isoformat()
        
    except Exception as e:
        logger.error(f"Process research task failed: {str(e)}")
        task_status[task_id]["status"] = TaskStatus.FAILED
        task_status[task_id]["message"] = f"任务失败: {str(e)}"
        task_status[task_id]["failed_at"] = datetime.now().isoformat()

@app.route('/api/research/status/<task_id>', methods=['GET'])
def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        return jsonify({
            "success": True,
            "task_id": task_id,
            "status": status["status"],
            "progress": status["progress"],
            "message": status["message"],
            "created_at": status["created_at"],
            "result": status.get("result"),
            "completed_at": status.get("completed_at"),
            "failed_at": status.get("failed_at")
        })
        
    except Exception as e:
        logger.error(f"Get task status failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/result/<task_id>', methods=['GET'])
def get_research_result(task_id: str):
    """获取研究结果"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        if status["status"] != TaskStatus.COMPLETED:
            return jsonify({"success": False, "error": "任务尚未完成"}), 400
        
        result = status.get("result")
        if not result:
            return jsonify({"success": False, "error": "结果不存在"}), 404
        
        return jsonify({
            "success": True,
            "task_id": task_id,
            "result": result
        })
        
    except Exception as e:
        logger.error(f"Get research result failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/download/<task_id>/<file_index>', methods=['GET'])
def download_media_file(task_id: str, file_index: int):
    """下载媒体文件"""
    try:
        if task_id not in task_status:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        status = task_status[task_id]
        if status["status"] != TaskStatus.COMPLETED:
            return jsonify({"success": False, "error": "任务尚未完成"}), 400
        
        result = status.get("result")
        if not result or "media_files" not in result:
            return jsonify({"success": False, "error": "媒体文件不存在"}), 404
        
        media_files = result["media_files"]
        file_index = int(file_index)
        
        if file_index >= len(media_files):
            return jsonify({"success": False, "error": "文件索引超出范围"}), 400
        
        file_path = media_files[file_index]
        if not os.path.exists(file_path):
            return jsonify({"success": False, "error": "文件不存在"}), 404
        
        return send_file(file_path, as_attachment=True)
        
    except Exception as e:
        logger.error(f"Download media file failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/quick-analysis', methods=['POST'])
def quick_analysis():
    """快速分析（仅生成报告）"""
    try:
        data = request.json
        
        if not data.get('target'):
            return jsonify({"success": False, "error": "目标主题不能为空"}), 400
        
        # 创建快速分析请求
        research_request = ResearchRequest(
            scenario=ResearchScenario(data.get('scenario', 'book_analysis')),
            target=data['target'],
            audience=data.get('audience', 'general'),
            output_format='report'
        )
        
        # 直接执行研究
        @run_async
        async def run_quick_analysis():
            return await research_agent.conduct_deep_research(research_request)
        
        result = run_quick_analysis()
        
        return jsonify({
            "success": True,
            "result": {
                "target": result.target,
                "scenario": result.scenario.value,
                "content": result.content,
                "quality_score": result.quality_score,
                "processing_time": result.processing_time,
                "sources": result.sources,
                "metadata": result.metadata
            }
        })
        
    except Exception as e:
        logger.error(f"Quick analysis failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/batch-generate', methods=['POST'])
def batch_generate():
    """批量生成"""
    try:
        data = request.json
        
        if not data.get('requests'):
            return jsonify({"success": False, "error": "批量请求不能为空"}), 400
        
        batch_id = str(uuid.uuid4())
        task_ids = []
        
        for req_data in data['requests']:
            if not req_data.get('target') or not req_data.get('scenario'):
                continue
            
            task_id = str(uuid.uuid4())
            task_ids.append(task_id)
            
            # 创建请求
            research_request = ResearchRequest(
                scenario=ResearchScenario(req_data['scenario']),
                target=req_data['target'],
                audience=req_data.get('audience', 'general'),
                output_format=req_data.get('output_format', 'report')
            )
            
            media_request = MediaGenerationRequest(
                research_request=research_request,
                target_format=MediaFormat(req_data.get('media_format', 'report')),
                style_preferences=req_data.get('style_preferences', {})
            )
            
            # 初始化任务状态
            task_status[task_id] = {
                "status": TaskStatus.PENDING,
                "progress": 0,
                "message": "批量任务已创建",
                "created_at": datetime.now().isoformat(),
                "batch_id": batch_id,
                "request": asdict(research_request)
            }
            
            # 启动任务
            import threading
            thread = threading.Thread(target=process_research_task, args=(task_id, media_request))
            thread.start()
        
        return jsonify({
            "success": True,
            "batch_id": batch_id,
            "task_ids": task_ids,
            "message": f"批量任务已创建，共{len(task_ids)}个任务"
        })
        
    except Exception as e:
        logger.error(f"Batch generate failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/batch-status/<batch_id>', methods=['GET'])
def get_batch_status(batch_id: str):
    """获取批量任务状态"""
    try:
        batch_tasks = {}
        for task_id, status in task_status.items():
            if status.get("batch_id") == batch_id:
                batch_tasks[task_id] = {
                    "status": status["status"],
                    "progress": status["progress"],
                    "message": status["message"],
                    "target": status["request"]["target"]
                }
        
        if not batch_tasks:
            return jsonify({"success": False, "error": "批量任务不存在"}), 404
        
        # 计算总体进度
        total_progress = sum(task["progress"] for task in batch_tasks.values())
        average_progress = total_progress / len(batch_tasks)
        
        # 统计状态
        status_count = {}
        for task in batch_tasks.values():
            status = task["status"]
            status_count[status] = status_count.get(status, 0) + 1
        
        return jsonify({
            "success": True,
            "batch_id": batch_id,
            "tasks": batch_tasks,
            "summary": {
                "total_tasks": len(batch_tasks),
                "average_progress": average_progress,
                "status_distribution": status_count
            }
        })
        
    except Exception as e:
        logger.error(f"Get batch status failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/metrics', methods=['GET'])
def get_system_metrics():
    """获取系统指标"""
    try:
        # 获取研究Agent指标
        research_metrics = research_agent.get_performance_metrics()
        
        # 获取管道指标
        pipeline_metrics = pipeline.get_pipeline_metrics()
        
        # 获取当前任务状态统计
        current_tasks = {}
        for status_info in task_status.values():
            status = status_info["status"]
            current_tasks[status] = current_tasks.get(status, 0) + 1
        
        return jsonify({
            "success": True,
            "metrics": {
                "research_agent": research_metrics,
                "pipeline": pipeline_metrics,
                "current_tasks": current_tasks,
                "total_tasks": len(task_status)
            }
        })
        
    except Exception as e:
        logger.error(f"Get system metrics failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/research/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        # 检查核心组件状态
        health_status = {
            "research_agent": "healthy",
            "pipeline": "healthy",
            "api_server": "healthy",
            "timestamp": datetime.now().isoformat()
        }
        
        return jsonify({
            "success": True,
            "health": health_status
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

# 静态文件服务
@app.route('/api/research/examples', methods=['GET'])
def get_examples():
    """获取使用示例"""
    examples = {
        "book_analysis": {
            "target": "Structures: Or Why Things Don't Fall Down by J.E. Gordon",
            "scenario": "book_analysis",
            "audience": "general",
            "media_format": "video",
            "style_preferences": {
                "tone": "专业而亲切",
                "duration": "10分钟"
            }
        },
        "industry_report": {
            "target": "人工智能",
            "scenario": "industry_report",
            "audience": "business",
            "media_format": "report",
            "additional_requirements": {
                "focus_area": "中国市场",
                "time_frame": "2024-2025"
            }
        },
        "research_progress": {
            "target": "量子计算",
            "scenario": "research_progress",
            "audience": "academic",
            "media_format": "podcast_script"
        },
        "news_analysis": {
            "target": "OpenAI ChatGPT发布",
            "scenario": "news_analysis",
            "audience": "general",
            "media_format": "video"
        }
    }
    
    return jsonify({
        "success": True,
        "examples": examples
    })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "接口不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "内部服务器错误"}), 500

if __name__ == '__main__':
    # 确保输出目录存在
    Path("output").mkdir(exist_ok=True)
    
    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )