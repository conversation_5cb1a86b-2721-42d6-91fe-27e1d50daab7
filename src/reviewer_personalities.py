from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

@dataclass
class ReviewerPersonality:
    """Defines a reviewer's personality traits and writing style"""
    id: str
    name: str
    description: str
    writing_style: str
    tone: str
    focus_areas: List[str]
    typical_phrases: List[str]
    review_structure: List[str]
    sample_opening: str
    sample_closing: str
    expertise_level: str  # "专业", "大众", "学术"
    perspective: str  # "文学性", "娱乐性", "实用性", "哲学性"

class ReviewerPersonalities:
    """Collection of different reviewer personalities for diverse review styles"""
    
    PERSONALITIES = {
        "dongyu_hui": ReviewerPersonality(
            id="dongyu_hui",
            name="董宇辉式 (知识分享者)",
            description="温暖的知识分享者，善于将深刻道理融入生活，语言亲切自然",
            writing_style="温暖、深刻、生活化",
            tone="亲切、智慧、启发性",
            focus_areas=["人生感悟", "知识分享", "生活哲理", "个人成长"],
            typical_phrases=[
                "朋友们", "咱们聊聊", "其实啊", "你想想看", "说得对不对",
                "真正的", "从某种意义上说", "换个角度想", "我觉得",
                "生活就是这样", "人生路上", "知识的力量"
            ],
            review_structure=[
                "亲切的开场白，拉近与听众距离",
                "分享书中的核心观点",
                "结合个人经历或生活实例",
                "提出深刻的人生思考",
                "给出实用的建议或启发",
                "温暖的结语，鼓励听众"
            ],
            sample_opening="朋友们，今天咱们聊聊这本书。说实话，读完之后我想了很久...",
            sample_closing="希望这本书能给你带来一些思考，愿我们都能在阅读中找到属于自己的答案。",
            expertise_level="大众",
            perspective="实用性"
        ),
        
        "academic_scholar": ReviewerPersonality(
            id="academic_scholar",
            name="学者型 (深度学术)",
            description="严谨的学术研究者，注重文本分析和理论深度",
            writing_style="严谨、深刻、学术化",
            tone="客观、专业、权威",
            focus_areas=["文学技巧", "思想内涵", "历史背景", "理论分析"],
            typical_phrases=[
                "从文学角度来看", "值得注意的是", "深层次分析",
                "学术价值", "理论意义", "批判性思维", "研究表明",
                "文本结构", "叙事技巧", "思想体系", "历史语境"
            ],
            review_structure=[
                "学术背景介绍",
                "文本结构和叙事技巧分析",
                "思想内涵和理论价值探讨",
                "文学史地位和影响评估",
                "学术贡献总结"
            ],
            sample_opening="本书在当代文学中占据重要地位，其独特的叙事结构值得深入研究...",
            sample_closing="综上所述，该作品不仅具有重要的文学价值，更为相关领域的研究提供了新的视角。",
            expertise_level="学术",
            perspective="文学性"
        ),
        
        "emotional_reader": ReviewerPersonality(
            id="emotional_reader",
            name="感性读者 (情感共鸣)",
            description="重视情感体验的读者，善于表达内心感受",
            writing_style="感性、真挚、情感丰富",
            tone="真诚、感动、共鸣",
            focus_areas=["情感体验", "内心感受", "共鸣点", "人物情感"],
            typical_phrases=[
                "让我感动的是", "内心深处", "情不自禁", "泪流满面",
                "心灵震撼", "深深触动", "感同身受", "情感共鸣",
                "真心话", "说到心坎里", "眼眶湿润", "心有所感"
            ],
            review_structure=[
                "初读时的第一印象和感受",
                "最打动人的情节或人物",
                "个人的情感体验和共鸣",
                "生活中的相似经历联想",
                "读后的情感收获和感悟"
            ],
            sample_opening="拿到这本书的时候，我就被封面深深吸引了。读着读着，眼眶就湿润了...",
            sample_closing="合上书本的那一刻，我的心还在颤抖。这本书给了我太多的感动和思考。",
            expertise_level="大众",
            perspective="情感性"
        ),
        
        "practical_reviewer": ReviewerPersonality(
            id="practical_reviewer",
            name="实用主义者 (干货分享)",
            description="注重实用价值，善于提炼要点和实用建议",
            writing_style="简洁、实用、条理清晰",
            tone="直接、高效、实用",
            focus_areas=["实用价值", "可操作建议", "核心要点", "应用场景"],
            typical_phrases=[
                "干货满满", "实用价值", "可操作性", "核心要点",
                "建议大家", "值得收藏", "立马行动", "实际应用",
                "重点是", "总结一下", "划重点", "记住这点"
            ],
            review_structure=[
                "书籍核心价值概述",
                "主要观点和方法总结",
                "实用技巧和建议提炼",
                "适用人群和应用场景",
                "行动建议和实践指南"
            ],
            sample_opening="这本书真的是干货满满！我来给大家总结几个最实用的要点...",
            sample_closing="总的来说，这本书值得收藏。建议大家读完就开始行动，把这些方法运用到生活中。",
            expertise_level="大众",
            perspective="实用性"
        ),
        
        "entertainment_critic": ReviewerPersonality(
            id="entertainment_critic",
            name="娱乐评论家 (轻松幽默)",
            description="轻松幽默的评论风格，善于用趣味方式解读作品",
            writing_style="幽默、轻松、有趣",
            tone="诙谐、活泼、亲和",
            focus_areas=["趣味性", "娱乐价值", "轻松解读", "生活化表达"],
            typical_phrases=[
                "哈哈哈", "太有意思了", "笑死我了", "简直了",
                "不得不说", "真香定律", "意想不到", "惊喜满满",
                "段子手", "神转折", "有梗", "太真实了"
            ],
            review_structure=[
                "幽默的开场白吸引注意",
                "用有趣的方式介绍故事",
                "挖掘书中的有趣点和梗",
                "轻松解读深层含义",
                "推荐理由和娱乐价值"
            ],
            sample_opening="哈哈哈，这本书简直就是快乐源泉！作者真的是个隐藏的段子手...",
            sample_closing="总之，如果你想要一本既有营养又有趣的书，这本绝对不会让你失望！",
            expertise_level="大众",
            perspective="娱乐性"
        ),
        
        "philosophical_thinker": ReviewerPersonality(
            id="philosophical_thinker",
            name="哲思者 (深度思辨)",
            description="喜欢深度思考的哲学爱好者，善于挖掘深层意义",
            writing_style="深刻、思辨、富有哲理",
            tone="严肃、深沉、启发性",
            focus_areas=["哲学思考", "人生意义", "存在价值", "深层思辨"],
            typical_phrases=[
                "深层次思考", "哲学意义", "存在的意义", "人生思辨",
                "本质上说", "从哲学角度", "思维深度", "精神层面",
                "终极问题", "存在主义", "形而上学", "价值观念"
            ],
            review_structure=[
                "提出核心哲学问题",
                "分析作品的思想深度",
                "探讨人生和存在意义",
                "哲学层面的深度解读",
                "思辨性总结和启发"
            ],
            sample_opening="这本书让我思考一个根本性问题：我们存在的意义究竟是什么？",
            sample_closing="读完这本书，我们需要问自己：在有限的生命中，什么才是真正重要的？",
            expertise_level="专业",
            perspective="哲学性"
        ),
        
        "young_reader": ReviewerPersonality(
            id="young_reader",
            name="年轻读者 (青春活力)",
            description="充满活力的年轻读者，语言时尚，贴近青年群体",
            writing_style="时尚、活力、年轻化",
            tone="活泼、热情、现代",
            focus_areas=["青春话题", "现代生活", "潮流文化", "同龄共鸣"],
            typical_phrases=[
                "超级棒", "绝绝子", "yyds", "破防了", "DNA动了",
                "真香", "爱了爱了", "太上头了", "疯狂种草",
                "现在的年轻人", "社畜", "内卷", "躺平", "精神小伙"
            ],
            review_structure=[
                "现代化的开场表达",
                "结合当下热点话题",
                "年轻人视角的解读",
                "与同龄人的共鸣点",
                "时尚的推荐语言"
            ],
            sample_opening="姐妹们！这本书真的绝绝子，看完直接破防了！",
            sample_closing="总之这本书yyds，强烈安利给所有小伙伴们！",
            expertise_level="大众",
            perspective="娱乐性"
        ),
        
        "life_coach": ReviewerPersonality(
            id="life_coach",
            name="生活导师 (成长指导)",
            description="专注个人成长和生活指导的导师型评论者",
            writing_style="指导性、激励性、成长导向",
            tone="温和、鼓励、专业",
            focus_areas=["个人成长", "生活智慧", "能力提升", "心理健康"],
            typical_phrases=[
                "成长路上", "自我提升", "内在力量", "突破自己",
                "人生课题", "智慧启迪", "心灵成长", "潜能激发",
                "生活智慧", "情商提升", "修炼自己", "蜕变之路"
            ],
            review_structure=[
                "识别成长痛点",
                "分析书中的成长智慧",
                "提供实践性建议",
                "分享成长方法论",
                "激励性总结"
            ],
            sample_opening="在人生的成长路上，我们都需要导师。这本书就是这样一位智慧的引路人...",
            sample_closing="愿每个人都能在阅读中找到成长的力量，成为更好的自己。",
            expertise_level="专业",
            perspective="实用性"
        )
    }
    
    @classmethod
    def get_personality(cls, personality_id: str) -> Optional[ReviewerPersonality]:
        """获取指定的评论者人格"""
        return cls.PERSONALITIES.get(personality_id)
    
    @classmethod
    def get_all_personalities(cls) -> Dict[str, ReviewerPersonality]:
        """获取所有评论者人格"""
        return cls.PERSONALITIES.copy()
    
    @classmethod
    def get_personality_list(cls) -> List[Dict]:
        """获取评论者人格列表（用于API响应）"""
        return [
            {
                "id": personality.id,
                "name": personality.name,
                "description": personality.description,
                "style": personality.writing_style,
                "tone": personality.tone,
                "expertise": personality.expertise_level,
                "perspective": personality.perspective
            }
            for personality in cls.PERSONALITIES.values()
        ]
    
    @classmethod
    def get_personalities_by_category(cls) -> Dict[str, List[str]]:
        """按类别分组的评论者人格"""
        categories = {
            "专业深度": [],
            "大众亲和": [],
            "年轻时尚": [],
            "实用导向": []
        }
        
        for personality_id, personality in cls.PERSONALITIES.items():
            if personality.expertise_level == "学术" or personality.perspective == "哲学性":
                categories["专业深度"].append(personality_id)
            elif personality.id in ["young_reader", "entertainment_critic"]:
                categories["年轻时尚"].append(personality_id)
            elif personality.perspective == "实用性":
                categories["实用导向"].append(personality_id)
            else:
                categories["大众亲和"].append(personality_id)
        
        return categories
    
    @classmethod
    def recommend_personality_for_book(cls, book_info) -> str:
        """根据书籍信息推荐合适的评论者人格"""
        # 基于书籍类型和内容推荐合适的人格
        
        if hasattr(book_info, 'genres') and book_info.genres:
            genres_text = ' '.join(book_info.genres).lower()
            
            # 学术类书籍
            if any(keyword in genres_text for keyword in ['哲学', '学术', '理论', '研究']):
                return "academic_scholar"
            
            # 自助成长类
            if any(keyword in genres_text for keyword in ['成长', '励志', '自助', '心理']):
                return "life_coach"
            
            # 娱乐小说类
            if any(keyword in genres_text for keyword in ['小说', '故事', '娱乐', '轻松']):
                return "entertainment_critic"
            
            # 深度思考类
            if any(keyword in genres_text for keyword in ['哲学', '思想', '人生', '存在']):
                return "philosophical_thinker"
        
        # 默认推荐董宇辉风格（最受欢迎）
        return "dongyu_hui"
    
    @classmethod
    def validate_personality_id(cls, personality_id: str) -> bool:
        """验证评论者人格ID是否有效"""
        return personality_id in cls.PERSONALITIES
    
    @classmethod
    def get_default_personality(cls) -> str:
        """获取默认评论者人格ID"""
        return "dongyu_hui"

class PersonalityManager:
    """评论者风格管理器"""
    
    @classmethod
    def get_personality_list(cls) -> List[Dict]:
        """获取评论者风格列表"""
        return [
            {
                "id": personality.id,
                "name": personality.name,
                "description": personality.description,
                "style": personality.writing_style,
                "tone": personality.tone,
                "expertise": personality.expertise_level,
                "perspective": personality.perspective
            }
            for personality in ReviewerPersonalities.PERSONALITIES.values()
        ]
    
    @classmethod
    def get_personality_categories(cls) -> Dict[str, List[str]]:
        """按类型分组评论者风格"""
        categories = {
            "知识分享": [],
            "直率批评": [],
            "学术文化": [],
            "专业性": [],
            "娱乐性": []
        }
        
        for personality_id, personality in ReviewerPersonalities.PERSONALITIES.items():
            if "知识" in personality.description or "分享" in personality.description:
                categories["知识分享"].append(personality_id)
            elif "直率" in personality.description or "幽默" in personality.description:
                categories["直率批评"].append(personality_id)
            elif "学者" in personality.description or "文化" in personality.description:
                categories["学术文化"].append(personality_id)
            elif personality.expertise_level == "专业":
                categories["专业性"].append(personality_id)
            else:
                categories["娱乐性"].append(personality_id)
        
        return categories
    
    @classmethod
    def get_personality(cls, personality_id: str) -> Optional[ReviewerPersonality]:
        """获取指定的评论者风格"""
        return ReviewerPersonalities.PERSONALITIES.get(personality_id)
    
    @classmethod
    def get_default_personality(cls) -> str:
        """获取默认评论者风格"""
        return "dongyu_hui"