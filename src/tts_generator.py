import os
import re
from typing import List, Tuple
from pydub import AudioSegment
from .config import Config
from .doubao_tts import DoubaoTTS

class TTSGenerator:
    def __init__(self, voice_id: str = None):
        # Initialize Doubao TTS service
        self.doubao_tts = DoubaoTTS()
        
        # Use provided voice_id or default to first available Doubao voice
        if voice_id and self.doubao_tts.validate_voice(voice_id):
            self.voice_id = voice_id
        else:
            # Get first available Doubao voice as default
            available_voices = list(self.doubao_tts.get_available_voices().keys())
            self.voice_id = available_voices[0] if available_voices else "zh_female_roumeinvyou_emo_v2_mars_bigtts"
            if voice_id:
                print(f"⚠️ Voice {voice_id} not available, using default: {self.voice_id}")
        
        # Log which voice is being used
        voice_info = self.doubao_tts.get_voice_info(self.voice_id)
        if voice_info:
            print(f"🎤 Using Doubao TTS voice: {voice_info['name']} ({self.voice_id})")
        else:
            print(f"🎤 Using Doubao TTS voice: {self.voice_id}")
        
        # Verify Doubao TTS is available
        if not self.doubao_tts.is_configured:
            raise RuntimeError("❌ Doubao TTS not configured. Please set DOUBAO_APP_ID and DOUBAO_ACCESS_TOKEN.")
    
    def test_voice_connectivity(self) -> bool:
        """Test if Doubao TTS service is available"""
        try:
            print("🔍 Testing Doubao TTS connectivity...")
            if self.doubao_tts.test_connection():
                print("✅ Doubao TTS is available")
                return True
            else:
                print("❌ Doubao TTS is not available")
                return False
        except Exception as e:
            print(f"❌ Doubao TTS test failed: {e}")
            return False
        
    def split_sentences(self, text: str) -> List[str]:
        """Split text into sentences based on Chinese punctuation"""
        # Chinese sentence delimiters
        sentence_endings = r'[。！？；]'
        
        # Split and restore punctuation
        result = []
        parts = re.split(f'({sentence_endings})', text)
        
        for i in range(0, len(parts)-1, 2):
            if i+1 < len(parts) and parts[i].strip():
                sentence = parts[i] + parts[i+1]
                # Clean and validate sentence
                sentence = sentence.strip()
                if len(sentence) > 1 and len(sentence) < 500:  # Reasonable length
                    result.append(sentence)
        
        # If no sentences found, split by length
        if not result and text.strip():
            # Split long text into chunks of ~100 characters
            words = text.split()
            current_chunk = []
            current_length = 0
            
            for word in words:
                if current_length + len(word) > 100 and current_chunk:
                    result.append(' '.join(current_chunk))
                    current_chunk = [word]
                    current_length = len(word)
                else:
                    current_chunk.append(word)
                    current_length += len(word) + 1
            
            if current_chunk:
                result.append(' '.join(current_chunk))
        
        return result
    
    
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text to make it suitable for TTS"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove problematic characters that might cause TTS issues
        text = re.sub(r'[^\w\s\u4e00-\u9fff，。！？；：、""''（）《》【】]', '', text)
        
        # Ensure proper spacing around punctuation
        text = re.sub(r'([，。！？；：])([^\s])', r'\1 \2', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        # Ensure text ends with proper punctuation for natural speech
        if text and not re.search(r'[。！？；]$', text):
            text += '。'
        
        return text
    
    
    def generate_speech(self, text: str, output_path: str) -> bool:
        """Generate speech using Doubao TTS API"""
        try:
            print("🎵 Using Doubao TTS service...")
            
            # Check if text contains emotion markers (various formats)
            if '<speak emotion=' in text or 'speak emotion"' in text or 'emotion=' in text:
                print("🎭 Detected emotion markers, using emotion synthesis...")
                success = self.doubao_tts.synthesize_emotion_marked_text(
                    text, 
                    output_path, 
                    voice_id=self.voice_id
                )
            else:
                success = self.doubao_tts.synthesize_to_file(
                    text, 
                    output_path, 
                    voice_id=self.voice_id,
                    emotion="neutral"
                )
            
            if success:
                print(f"✅ Doubao TTS generated: {output_path}")
                return True
            else:
                print("❌ Doubao TTS failed")
                return False
                
        except Exception as e:
            print(f"❌ Doubao TTS error: {e}")
            return False
    
    def generate_opening_narration(self, opening_text: str, output_path: str) -> float:
        """Generate opening narration and return duration in seconds"""
        success = self.generate_speech(opening_text, output_path)
        
        if success and os.path.exists(output_path):
            # Get duration using pydub
            audio = AudioSegment.from_file(output_path)
            return len(audio) / 1000.0  # Convert to seconds
        
        return 0.0
    
    def generate_sentence_audio(self, sentences: List[str], temp_dir: str) -> List[Tuple[str, str, float]]:
        """
        Generate audio for each sentence
        Returns: List of (sentence, audio_path, duration)
        """
        results = []
        
        print(f"🎵 Generating audio for {len(sentences)} sentences...")
        
        for i, sentence in enumerate(sentences):
            # Skip empty sentences
            if not sentence or not sentence.strip():
                print(f"⏭️ Skipping empty sentence {i}")
                continue
            
            # Clean sentence for logging
            clean_sentence = sentence.strip()
            if len(clean_sentence) > 50:
                log_sentence = clean_sentence[:50] + "..."
            else:
                log_sentence = clean_sentence
                
            print(f"🔊 [{i+1}/{len(sentences)}] Generating: '{log_sentence}'")
                
            audio_path = os.path.join(temp_dir, f"sentence_{i:03d}.mp3")
            success = self.generate_speech(sentence, audio_path)
            
            if success and os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                try:
                    audio = AudioSegment.from_file(audio_path)
                    duration = len(audio) / 1000.0
                    results.append((sentence, audio_path, duration))
                    print(f"✅ Audio generated: {duration:.1f}s")
                except Exception as e:
                    print(f"❌ Error reading audio file {audio_path}: {e}")
            else:
                print(f"❌ Failed to generate audio for sentence {i}: '{clean_sentence}'")
                # Try to generate a simple placeholder audio
                try:
                    placeholder_text = "无法生成音频。"
                    placeholder_success = self.generate_speech(placeholder_text, audio_path)
                    if placeholder_success and os.path.exists(audio_path):
                        audio = AudioSegment.from_file(audio_path)
                        duration = len(audio) / 1000.0
                        results.append((placeholder_text, audio_path, duration))
                        print(f"⚠️ Used placeholder audio instead")
                except Exception as e:
                    print(f"❌ Even placeholder audio failed: {e}")
        
        print(f"🎵 Audio generation complete: {len(results)}/{len(sentences)} successful")
        return results
    
    def concatenate_audio_files(self, audio_files: List[str], output_path: str) -> bool:
        """Concatenate multiple audio files into one"""
        try:
            combined = AudioSegment.empty()
            
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    audio = AudioSegment.from_file(audio_file)
                    combined += audio
            
            combined.export(output_path, format="mp3")
            return True
            
        except Exception as e:
            print(f"Error concatenating audio: {e}")
            return False