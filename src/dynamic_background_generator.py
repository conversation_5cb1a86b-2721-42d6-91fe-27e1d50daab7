import os
import re
import random
from typing import List, Dict, Tuple, Optional
from .config import Config
from .context_analyzer import ContextualThemeAnalyzer, IntelligentBackgroundPlanner

class ContentAnalyzer:
    """Analyze script content to extract visual cues for background images"""
    
    def __init__(self):
        # Keywords that suggest specific visual content
        self.visual_keywords = {
            "nature": ["自然", "森林", "山", "海", "湖", "河", "树", "花", "草原", "田野", "风景"],
            "city": ["城市", "大厦", "街道", "建筑", "都市", "繁华", "夜景", "灯光"],
            "historical": ["历史", "古代", "传统", "文化", "遗迹", "古建筑", "宫殿", "寺庙"],
            "emotional": ["爱情", "友谊", "家庭", "温暖", "浪漫", "感动", "幸福"],
            "adventure": ["冒险", "旅行", "探索", "神秘", "未知", "奇幻", "魔法"],
            "war": ["战争", "军事", "战斗", "士兵", "英雄", "胜利"],
            "science": ["科学", "技术", "未来", "太空", "实验", "发明", "研究"],
            "mystery": ["悬疑", "推理", "犯罪", "侦探", "秘密", "阴谋"],
            "horror": ["恐怖", "黑暗", "鬼", "怪物", "恐惧", "阴森"],
            "fantasy": ["奇幻", "魔法", "精灵", "龙", "魔法师", "仙境"],
            "school": ["学校", "校园", "学生", "教室", "青春", "学习"],
            "business": ["商业", "公司", "办公", "会议", "成功", "职场"]
        }
        
        # Emotion-based visual themes
        self.emotion_visuals = {
            "happy": "bright colorful landscape with sunshine",
            "sad": "melancholic scenery with soft lighting",
            "angry": "dramatic stormy landscape",
            "excited": "vibrant dynamic cityscape",
            "fear": "mysterious dark forest",
            "neutral": "peaceful natural landscape",
            "surprised": "magical wonderland scene",
            "coldness": "winter mountain landscape"
        }
        
        # Fallback landscape types
        self.landscape_types = [
            "serene mountain lake at sunset",
            "peaceful forest with sunlight filtering through trees",
            "beautiful cherry blossom garden in spring",
            "tranquil bamboo forest with gentle mist",
            "majestic snow-capped mountains",
            "calm ocean waves at golden hour",
            "rolling green hills with wildflowers",
            "ancient stone bridge over a stream",
            "misty morning in a pine forest",
            "colorful autumn leaves in a park"
        ]

    def analyze_segment_content(self, text: str, timeline_position: float) -> Dict:
        """
        Analyze a text segment to determine appropriate visual content
        Returns: Dict with image_type, description, keywords, confidence
        """
        # Clean text first
        clean_text = self._clean_text(text)
        
        # Extract visual keywords
        detected_categories = self._detect_visual_categories(clean_text)
        
        # Extract emotions from text
        emotions = self._extract_emotions(text)
        
        # Determine image type and description
        if detected_categories:
            # Use the most confident category
            primary_category = max(detected_categories.items(), key=lambda x: x[1])
            image_type = primary_category[0]
            confidence = primary_category[1]
            
            # Generate description based on category
            description = self._generate_category_description(image_type, clean_text)
        elif emotions:
            # Use emotion-based visual
            primary_emotion = emotions[0]
            image_type = "emotional"
            description = self.emotion_visuals.get(primary_emotion, self.emotion_visuals["neutral"])
            confidence = 0.7
        else:
            # Fallback to landscape
            image_type = "landscape"
            description = random.choice(self.landscape_types)
            confidence = 0.5
        
        return {
            "image_type": image_type,
            "description": description,
            "keywords": list(detected_categories.keys()) if detected_categories else [],
            "emotions": emotions,
            "confidence": confidence,
            "timeline_position": timeline_position,
            "text_excerpt": clean_text[:100] + "..." if len(clean_text) > 100 else clean_text
        }
    
    def _clean_text(self, text: str) -> str:
        """Clean text by removing emotion tags and formatting"""
        import re
        # Remove emotion tags
        cleaned = re.sub(r'<speak\s+emotion=["\'][^"\']*["\']>(.*?)</speak>', r'\1', text)
        cleaned = re.sub(r'<[^>]+>', '', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned
    
    def _detect_visual_categories(self, text: str) -> Dict[str, float]:
        """Detect visual categories in text and return confidence scores"""
        categories = {}
        
        for category, keywords in self.visual_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            
            if score > 0:
                # Normalize score based on text length and keyword frequency
                confidence = min(score / len(keywords) * 2, 1.0)
                categories[category] = confidence
        
        return categories
    
    def _extract_emotions(self, text: str) -> List[str]:
        """Extract emotion markers from text"""
        import re
        emotions = []
        
        # Extract from emotion tags
        emotion_matches = re.findall(r'<speak\s+emotion=["\']([^"\']*)["\']>', text)
        emotions.extend(emotion_matches)
        
        # Remove duplicates while preserving order
        return list(dict.fromkeys(emotions))
    
    def _generate_category_description(self, category: str, text: str) -> str:
        """Generate detailed image description based on category and text content"""
        base_descriptions = {
            "nature": "beautiful natural landscape with lush greenery and peaceful atmosphere",
            "city": "modern cityscape with impressive architecture and urban energy",
            "historical": "ancient historical site with traditional architecture and cultural heritage",
            "emotional": "warm and touching scene that evokes deep feelings",
            "adventure": "exciting adventure scene with sense of exploration and discovery",
            "war": "dramatic historical battlefield or military scene",
            "science": "futuristic scientific laboratory or space scene",
            "mystery": "mysterious and intriguing scene with atmospheric lighting",
            "horror": "dark and mysterious scene with dramatic shadows",
            "fantasy": "magical fantasy landscape with enchanting elements",
            "school": "beautiful school campus with youthful and energetic atmosphere",
            "business": "modern professional office environment or business district"
        }
        
        base = base_descriptions.get(category, "beautiful scenic landscape")
        
        # Add specific details based on text content
        if "夜" in text or "晚" in text:
            base += " at night with beautiful lighting"
        elif "日" in text or "阳" in text:
            base += " in bright daylight with warm sunshine"
        elif "雨" in text:
            base += " with gentle rain and atmospheric effects"
        elif "雪" in text:
            base += " with snow and winter atmosphere"
        
        return base


class DynamicBackgroundGenerator:
    """Generate timeline-based background images with transitions"""
    
    def __init__(self, book_metadata: Dict = None):
        self.content_analyzer = ContentAnalyzer()
        self.contextual_analyzer = ContextualThemeAnalyzer()
        self.background_planner = IntelligentBackgroundPlanner(
            min_duration=15.0,  # Minimum 15 seconds between changes
            importance_threshold=0.6  # Only change for important scenes
        )
        self.transition_interval = (15, 25)  # Longer intervals to reduce frequency
        self.book_metadata = book_metadata or {}
        self.book_title = self.book_metadata.get('title', '')
        self.book_author = self.book_metadata.get('author', '')
        
    def plan_background_sequence(self, alignment_data: List[Dict], total_duration: float) -> List[Dict]:
        """
        Plan contextual background sequence based on content analysis
        Returns: List of background segments with timing and contextual descriptions
        """
        print(f"🧠 Planning contextual background sequence for {total_duration:.1f}s video")
        
        # Use intelligent background planner for context-aware segments
        contextual_segments = self.background_planner.plan_contextual_backgrounds(
            alignment_data, self.book_metadata
        )
        
        # Convert contextual segments to background segments
        segments = []
        
        for i, ctx_segment in enumerate(contextual_segments):
            segment = {
                "index": i,
                "start_time": ctx_segment["start_time"],
                "end_time": min(ctx_segment["end_time"], total_duration),
                "duration": min(ctx_segment["end_time"], total_duration) - ctx_segment["start_time"],
                "content_analysis": ctx_segment["context_analysis"],
                "image_prompt": self._create_contextual_image_prompt(ctx_segment["context_analysis"]),
                "transition_type": self._select_transition_type(i),
                "relevant_text": ctx_segment["relevant_text"],
                "characters": ctx_segment.get("characters", []),
                "locations": ctx_segment.get("locations", []),
                "importance": ctx_segment["importance"]
            }
            
            segments.append(segment)
            
            print(f"  📍 Segment {i+1}: {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")
            print(f"     Importance: {segment['importance']:.2f}")
            print(f"     Prompt: {segment['image_prompt'][:80]}...")
            
            if segment['characters']:
                chars = [c['name'] for c in segment['characters'][:2]]
                print(f"     Characters: {', '.join(chars)}")
        
        # If no contextual segments found, create fallback
        if not segments:
            print("⚠️ No contextual segments found, creating fallback segment")
            segments = self._create_fallback_segments(total_duration)
        
        print(f"✅ Planned {len(segments)} contextual background segments")
        return segments
    
    def _get_text_for_timeframe(self, alignment_data: List[Dict], start_time: float, end_time: float) -> str:
        """Extract text content that plays during the specified timeframe"""
        relevant_segments = []
        
        for segment in alignment_data:
            seg_start = segment['start'] / 1000.0  # Convert to seconds
            seg_end = segment['end'] / 1000.0
            
            # Check if segment overlaps with timeframe
            if seg_start < end_time and seg_end > start_time:
                relevant_segments.append(segment['text'])
        
        return " ".join(relevant_segments)
    
    def _create_contextual_image_prompt(self, context_analysis: Dict) -> str:
        """Create contextual image prompt based on specific content analysis"""
        
        # Use the scene description from contextual analysis
        base_prompt = context_analysis.get('scene_description', 'beautiful atmospheric scene')
        
        # The contextual analyzer already creates detailed, specific prompts
        # Just add final style touches
        style_modifiers = [
            "professional artwork",
            "8K resolution",
            "masterpiece quality"
        ]
        
        full_prompt = f"{base_prompt}, {', '.join(style_modifiers)}"
        
        return full_prompt
    
    def _create_fallback_segments(self, total_duration: float) -> List[Dict]:
        """Create fallback segments when no contextual analysis is available"""
        segments = []
        current_time = 0
        segment_index = 0
        
        while current_time < total_duration:
            segment_duration = random.uniform(*self.transition_interval)
            end_time = min(current_time + segment_duration, total_duration)
            
            # Create generic content analysis
            content_analysis = {
                'scene_description': f'beautiful atmospheric scene inspired by {self.book_title}' if self.book_title else 'beautiful atmospheric scene',
                'image_type': 'atmospheric',
                'confidence': 0.5,
                'importance': 0.5
            }
            
            segment = {
                "index": segment_index,
                "start_time": current_time,
                "end_time": end_time,
                "duration": end_time - current_time,
                "content_analysis": content_analysis,
                "image_prompt": self._create_contextual_image_prompt(content_analysis),
                "transition_type": self._select_transition_type(segment_index),
                "relevant_text": "Fallback segment",
                "characters": [],
                "locations": [],
                "importance": 0.5
            }
            
            segments.append(segment)
            current_time = end_time
            segment_index += 1
        
        return segments
    
    def _select_transition_type(self, segment_index: int) -> str:
        """Select appropriate transition type for the segment"""
        transitions = [
            "fade",
            "crossfade", 
            "slide_left",
            "slide_right",
            "zoom_in",
            "zoom_out",
            "pan_left",
            "pan_right"
        ]
        
        # First segment has no transition
        if segment_index == 0:
            return "none"
        
        # Vary transitions for visual interest
        return random.choice(transitions)
    
    def generate_background_images(self, background_segments: List[Dict], temp_dir: str) -> List[Dict]:
        """
        Generate background images for each segment
        Returns: Updated segments with image paths
        """
        updated_segments = []
        
        print(f"🖼️ Generating {len(background_segments)} background images...")
        
        for i, segment in enumerate(background_segments):
            image_filename = f"background_{segment['index']:03d}.jpg"
            image_path = os.path.join(temp_dir, image_filename)
            
            # Get image type from context analysis or fallback
            context_analysis = segment.get('content_analysis', {})
            image_type = context_analysis.get('image_type', context_analysis.get('book_type', 'contextual'))
            print(f"  [{i+1}/{len(background_segments)}] Generating: {image_type}")
            
            # Try to generate image with background-specific prompt
            success = self._generate_background_image(segment['image_prompt'], image_path)
            
            if success and os.path.exists(image_path):
                segment['image_path'] = image_path
                segment['generation_success'] = True
                print(f"    ✅ Generated: {image_path}")
            else:
                # Create fallback image
                print(f"    ⚠️ Generation failed, creating fallback...")
                segment['image_path'] = self._create_fallback_image(image_path)
                segment['generation_success'] = False
            
            updated_segments.append(segment)
        
        print(f"✅ Background image generation complete")
        return updated_segments
    
    def _generate_background_image(self, prompt: str, output_path: str) -> bool:
        """Generate background image optimized for video backgrounds"""
        try:
            from .image_generator import ImageGenerator
            
            # Create a background-optimized prompt
            background_prompt = self._create_background_prompt(prompt)
            
            image_gen = ImageGenerator()
            
            # Use the cover image generator but with background-specific prompt
            success = image_gen.generate_cover_image(background_prompt, output_path)
            
            return success
            
        except Exception as e:
            print(f"❌ Background image generation error: {e}")
            return False
    
    def _create_background_prompt(self, original_prompt: str) -> str:
        """Create background-optimized prompt"""
        background_prompt = f"""
        Create a beautiful cinematic background scene for video content.
        
        {original_prompt}
        
        Technical requirements:
        - Horizontal landscape orientation suitable for video backgrounds
        - Soft, non-distracting composition that works well behind text
        - Beautiful lighting and atmosphere
        - High quality and detailed
        - Suitable for video overlay content
        - Avoid text or busy foreground elements
        - Focus on ambient mood and atmosphere
        
        Style: Cinematic, atmospheric, professional video background
        """
        
        return background_prompt
    
    def _create_fallback_image(self, output_path: str) -> str:
        """Create a simple fallback background image"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import random
            
            # Create a gradient background
            width, height = 1920, 1080
            image = Image.new('RGB', (width, height))
            draw = ImageDraw.Draw(image)
            
            # Random gradient colors
            colors = [
                [(135, 206, 235), (255, 182, 193)],  # Sky blue to light pink
                [(255, 165, 0), (255, 69, 0)],       # Orange to red-orange
                [(147, 112, 219), (186, 85, 211)],   # Purple gradient
                [(34, 139, 34), (144, 238, 144)],    # Green gradient
                [(70, 130, 180), (176, 196, 222)]    # Steel blue gradient
            ]
            
            start_color, end_color = random.choice(colors)
            
            # Create vertical gradient
            for y in range(height):
                ratio = y / height
                r = int(start_color[0] * (1 - ratio) + end_color[0] * ratio)
                g = int(start_color[1] * (1 - ratio) + end_color[1] * ratio)
                b = int(start_color[2] * (1 - ratio) + end_color[2] * ratio)
                draw.line([(0, y), (width, y)], fill=(r, g, b))
            
            # Add some decorative elements
            for _ in range(20):
                x = random.randint(0, width)
                y = random.randint(0, height)
                size = random.randint(10, 50)
                alpha_color = (*random.choice([start_color, end_color]), 80)
                
                # Create overlay for transparent circles
                overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
                overlay_draw = ImageDraw.Draw(overlay)
                overlay_draw.ellipse([x-size, y-size, x+size, y+size], fill=alpha_color)
                
                # Composite with main image
                image = Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
            
            image.save(output_path, 'JPEG', quality=85)
            return output_path
            
        except Exception as e:
            print(f"Error creating fallback image: {e}")
            return None