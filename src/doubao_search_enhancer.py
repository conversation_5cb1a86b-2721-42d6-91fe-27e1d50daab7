"""
豆包1.6 Thinking模型联网搜索增强器
利用豆包1.6的边想边搜功能，为内容生成提供实时网络信息
"""

import requests
import json
import os
from typing import Dict, List, Optional, Any
from .config import Config


class DoubaoSearchEnhancer:
    """豆包1.6 Thinking联网搜索增强器"""
    
    def __init__(self):
        self.api_key = Config.ARK_API_KEY
        self.base_url = Config.ARK_BASE_URL
        self.model = "doubao-seed-1-6-thinking-250615"  # 使用Thinking版本
        
        self.search_enabled = bool(self.api_key)
        if not self.search_enabled:
            print("⚠️ Doubao Search Enhancer not configured: Missing ARK_API_KEY")
    
    def enhance_prompt_with_search(self, user_prompt: str, semantic_context=None) -> tuple[str, Dict[str, Any]]:
        """
        使用豆包1.6 Thinking的联网功能增强用户提示词
        
        Args:
            user_prompt: 用户原始提示词
            semantic_context: 语义上下文（可选）
            
        Returns:
            tuple: (增强后的提示词, 搜索元数据)
        """
        
        if not self.search_enabled:
            print("🔄 Search enhancement disabled, using original prompt")
            return user_prompt, {"search_enabled": False}
        
        print("🌐 Starting intelligent search enhancement with Doubao 1.6 Thinking...")
        
        # 构建搜索增强提示词
        search_prompt = self._build_search_enhancement_prompt(user_prompt, semantic_context)
        
        try:
            # 调用豆包1.6 Thinking进行智能搜索
            search_result = self._call_doubao_thinking_search(search_prompt)
            
            if search_result:
                # 解析搜索结果并生成增强提示词
                enhanced_prompt = self._parse_and_enhance_prompt(user_prompt, search_result)
                
                metadata = {
                    "search_enabled": True,
                    "search_successful": True,
                    "enhancement_applied": True,
                    "original_length": len(user_prompt),
                    "enhanced_length": len(enhanced_prompt),
                    "model_used": self.model
                }
                
                print(f"✅ Search enhancement successful - expanded prompt from {len(user_prompt)} to {len(enhanced_prompt)} chars")
                return enhanced_prompt, metadata
            
        except Exception as e:
            print(f"❌ Search enhancement failed: {e}")
        
        # 失败时返回原始提示词
        metadata = {
            "search_enabled": True,
            "search_successful": False,
            "enhancement_applied": False,
            "fallback_used": True
        }
        
        return user_prompt, metadata
    
    def _build_search_enhancement_prompt(self, user_prompt: str, semantic_context=None) -> str:
        """构建搜索增强提示词"""
        
        # 基础搜索指令
        base_instruction = """
你是一个专业的信息研究助手，具备强大的联网搜索和信息整合能力。

请根据用户的请求，进行深度的联网搜索和信息收集，然后生成一个信息丰富、内容详实的增强版本。

## 任务要求：
1. **深度搜索**：对用户提到的主题进行全面的联网搜索
2. **信息验证**：确保搜索到的信息准确可靠
3. **内容整合**：将搜索结果整合成有条理的知识框架
4. **时效性**：重点关注最新的发展动态和趋势
5. **多角度**：从不同角度和层面收集相关信息

## 搜索策略：
- 搜索最新的新闻报道和行业动态
- 查找权威机构的研究报告和数据
- 收集专家观点和行业分析
- 了解相关的案例研究和实践经验
- 探索未来发展趋势和前沿技术

## 输出格式：
请按以下结构组织搜索结果：

### 1. 核心概念解析
[对主题的基础概念进行深入解释]

### 2. 最新发展动态
[搜索到的最新新闻、趋势、数据]

### 3. 权威观点整合
[专家意见、机构报告、研究发现]

### 4. 实践案例分析
[具体的应用案例、成功故事、经验教训]

### 5. 未来展望预测
[行业趋势、发展预测、机遇挑战]

### 6. 深度洞察总结
[基于搜索信息的深度分析和独特见解]
"""
        
        # 添加领域特定的搜索指导
        domain_guidance = ""
        if semantic_context and hasattr(semantic_context, 'domain'):
            domain_specific = {
                "科技": "重点搜索最新技术突破、产品发布、研发趋势、行业报告",
                "商业": "关注市场动态、公司财报、投资趋势、商业模式创新",
                "教育": "搜索教育政策、教学方法、学习技术、教育数据统计",
                "生活": "查找生活方式趋势、健康数据、消费行为、社会现象",
                "文学": "搜索最新出版动态、文学评论、作者访谈、文化现象"
            }
            
            domain_guidance = f"\n## 领域特定搜索重点：\n{domain_specific.get(semantic_context.domain, '进行全面的信息搜索')}"
        
        # 用户原始请求
        user_request = f"\n## 用户原始请求：\n{user_prompt}"
        
        # 关键词提取指导
        keyword_instruction = """
## 搜索关键词策略：
请根据用户请求自动提取和扩展搜索关键词，包括：
- 核心概念的中英文术语
- 相关的技术名词和专业术语
- 行业标准名称和缩写
- 知名公司、产品、人物名称
- 时间相关的限定词（如"2024年"、"最新"等）
"""
        
        return base_instruction + domain_guidance + user_request + keyword_instruction
    
    def _call_doubao_thinking_search(self, prompt: str) -> Optional[str]:
        """调用豆包1.6 Thinking模型进行联网搜索"""
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # 构建请求数据，启用搜索功能
            data = {
                'model': self.model,
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'temperature': 0.7,
                'max_tokens': 4000,
                # 启用搜索功能的参数（根据豆包API文档）
                'stream': False,
                'tools': [
                    {
                        'type': 'web_search',
                        'web_search': {
                            'enable': True
                        }
                    }
                ]
            }
            
            print(f"🔍 Calling Doubao 1.6 Thinking with search enabled...")
            response = requests.post(
                f'{self.base_url}/chat/completions',
                headers=headers,
                json=data,
                timeout=120  # 增加超时时间，因为搜索需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"✅ Search-enhanced response received: {len(content)} characters")
                    return content
                else:
                    print("❌ No valid response in API result")
                    return None
            else:
                print(f"❌ API request failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error calling Doubao Thinking API: {e}")
            return None
    
    def _parse_and_enhance_prompt(self, original_prompt: str, search_result: str) -> str:
        """解析搜索结果并生成增强的提示词"""
        
        # 构建增强后的提示词
        enhanced_prompt = f"""
## 原始用户需求
{original_prompt}

## 基于联网搜索的深度信息补充

{search_result}

## 内容生成要求
请基于以上搜索到的最新信息和深度分析，生成高质量的内容。要求：

1. **信息时效性**：充分利用搜索到的最新信息和数据
2. **内容权威性**：引用可靠来源和专家观点
3. **视角多元性**：结合不同角度和层面的信息
4. **实用价值**：提供有价值的洞察和实用建议
5. **逻辑清晰**：信息组织有序，论述条理分明

请确保生成的内容既专业深入，又通俗易懂，适合视频解说的表达方式。
"""
        
        return enhanced_prompt
    
    def get_search_metadata(self) -> Dict[str, Any]:
        """获取搜索增强器的元数据信息"""
        return {
            "enhancer_name": "Doubao 1.6 Thinking Search Enhancer",
            "model": self.model,
            "search_enabled": self.search_enabled,
            "base_url": self.base_url,
            "features": [
                "Real-time web search",
                "Information verification", 
                "Content integration",
                "Multi-perspective analysis",
                "Trend monitoring"
            ]
        }


class SearchEnhancedContentGenerator:
    """集成搜索增强功能的内容生成器"""
    
    def __init__(self):
        self.search_enhancer = DoubaoSearchEnhancer()
    
    def generate_with_search_enhancement(self, user_prompt: str, personality_id: str = None, 
                                       semantic_context=None) -> tuple[str, Dict[str, Any]]:
        """
        生成搜索增强的内容
        
        Returns:
            tuple: (增强内容, 元数据)
        """
        
        # Step 1: 使用搜索增强用户提示词
        enhanced_prompt, search_metadata = self.search_enhancer.enhance_prompt_with_search(
            user_prompt, semantic_context
        )
        
        # Step 2: 这里可以集成到现有的脚本生成器中
        # 返回增强后的提示词供其他组件使用
        
        metadata = {
            "search_enhancement": search_metadata,
            "enhanced_prompt_length": len(enhanced_prompt),
            "enhancement_ratio": len(enhanced_prompt) / len(user_prompt) if user_prompt else 1
        }
        
        return enhanced_prompt, metadata