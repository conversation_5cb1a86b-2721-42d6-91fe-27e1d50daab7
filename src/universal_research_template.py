"""
Universal Research Prompt Template System
通用深度研究提示词模板系统

基于用户提供的优秀DeepResearch提示词设计，扩展为支持多种研究场景的通用模板系统
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json

class ResearchScenario(Enum):
    """研究场景枚举"""
    BOOK_ANALYSIS = "book_analysis"
    INDUSTRY_REPORT = "industry_report"
    RESEARCH_PROGRESS = "research_progress"
    NEWS_ANALYSIS = "news_analysis"
    MARKET_ANALYSIS = "market_analysis"
    BOOK_RECOMMENDATION = "book_recommendation"
    COMPETITIVE_INTELLIGENCE = "competitive_intelligence"
    TECHNOLOGY_ASSESSMENT = "technology_assessment"
    INVESTMENT_ANALYSIS = "investment_analysis"
    TREND_ANALYSIS = "trend_analysis"

@dataclass
class ResearchTask:
    """研究任务定义"""
    name: str
    description: str
    deliverables: List[str]
    search_keywords: List[str]
    analysis_depth: str  # "basic", "intermediate", "advanced"
    visualization_type: Optional[str] = None

@dataclass
class ResearchRequest:
    """研究请求数据结构"""
    scenario: ResearchScenario
    target: str  # 研究对象
    language: str = "zh-CN"
    audience: str = "general"  # general, professional, academic, business
    output_format: str = "report"  # report, video, podcast, interactive
    additional_requirements: Optional[Dict[str, Any]] = None
    
class UniversalResearchPromptTemplate:
    """通用研究提示词模板系统"""
    
    def __init__(self):
        self.base_framework = self._initialize_base_framework()
        self.scenario_configs = self._initialize_scenario_configs()
        self.audience_adaptations = self._initialize_audience_adaptations()
        
    def _initialize_base_framework(self) -> Dict[str, str]:
        """初始化基础框架模板"""
        return {
            "objective_template": "对【{target}】进行{research_type}，提供{analysis_depth}的理解和分析",
            "general_requirements": """
通用要求：
- 语言选择：最终报告必须使用{language}撰写，确保目标受众的可读性
- 目标受众：报告面向{audience_description}
- 报告长度：提供详细全面的分析，不严格限制长度。优先考虑深度和彻底性，确保对{target}内容和意义的完整理解
- 参考来源：优先使用可信权威的来源，包括：
  * 维基百科用于基础背景和交叉参考
  * 相关书籍、学术期刊和科普出版物
  * 权威媒体和杂志
  * 作者访谈、出版商网站或官方相关材料
  * 避免使用未经验证或低质量的来源
- 可视化：适当时加入表格、图表或其他视觉辅助工具（如时间线、主题图或对比表）以增强对复杂概念、关系或数据的理解。确保视觉元素清晰标记并与分析直接相关
""",
            "search_strategy": """
搜索参数：
使用以下关键词进行全面搜索以获取高质量来源：
- 主要文本：{target}的完整文本（如可获取）或通过出版商网站、Google Books或图书馆数据库获取的详细摘要
- 二级来源：学术评论、权威书评、作者访谈或讲座、可信账户的社交媒体讨论（需与主要来源交叉验证）
- 关键词：{search_keywords}
- 交叉参考信息确保准确性，特别是对于争议性话题或作者背景
""",
            "output_requirements": """
输出要求：
- 将回应组织为{task_count}个清晰标记的中文章节
- 使用清晰、引人入胜且易于理解的语言，适合一般读者，学术概念需简单解释
- 使用格式为[web:<number>]的网络结果和[post:<number>]的社交媒体帖子进行引用，置于相关段落句号之后
- 突出信息缺口（如无法获取完整文本、有限的学术评论）并建议进一步研究方向
- 在适当时加入可视化元素，确保清晰标记并在文本中引用
- 保持中性、分析性的语调，平衡欣赏优点和批判性评价缺点
"""
        }
    
    def _initialize_scenario_configs(self) -> Dict[ResearchScenario, Dict]:
        """初始化场景配置"""
        return {
            ResearchScenario.BOOK_ANALYSIS: {
                "research_type": "全面系统的深度阅读",
                "analysis_depth": "深入",
                "tasks": [
                    ResearchTask(
                        name="章节总结",
                        description="对书籍内容进行系统总结，覆盖每个章节或主要部分",
                        deliverables=["章节主要论点总结", "关键证据和案例", "在整体论述中的作用"],
                        search_keywords=["{target} 章节总结", "{target} 内容概述", "{target} 结构分析"],
                        analysis_depth="intermediate"
                    ),
                    ResearchTask(
                        name="核心观点与精华",
                        description="提取并展示书籍的核心论点或中心思想",
                        deliverables=["核心论点", "关键洞察", "思想精华", "观点互联关系图"],
                        search_keywords=["{target} 核心观点", "{target} 主要论点", "{target} 思想精华"],
                        analysis_depth="advanced",
                        visualization_type="mind_map"
                    ),
                    ResearchTask(
                        name="逻辑缺陷与争议分析",
                        description="批判性分析书籍的逻辑缺陷、不一致性或论证弱点",
                        deliverables=["逻辑缺陷分析", "争议性方面", "批评与反驳对比表"],
                        search_keywords=["{target} 批评", "{target} 争议", "{target} 逻辑分析"],
                        analysis_depth="advanced",
                        visualization_type="comparison_table"
                    ),
                    ResearchTask(
                        name="价值延展与评价",
                        description="提出对书籍思想或论点的有意义延伸",
                        deliverables=["思想延伸", "价值判断", "现代应用", "延伸应用图"],
                        search_keywords=["{target} 现代意义", "{target} 应用价值", "{target} 思想延伸"],
                        analysis_depth="advanced",
                        visualization_type="application_chart"
                    ),
                    ResearchTask(
                        name="同类与对立书籍",
                        description="识别同类型和对立观点的相关书籍",
                        deliverables=["同类书籍对比", "对立观点书籍", "主题对比表"],
                        search_keywords=["{target} 类似书籍", "{target} 相关作品", "{target} 对立观点"],
                        analysis_depth="intermediate",
                        visualization_type="comparison_table"
                    ),
                    ResearchTask(
                        name="作者背景与书籍定位",
                        description="提供作者简明传记并将书籍置于其生涯坐标系统中",
                        deliverables=["作者传记", "创作背景", "生涯定位", "创作时间线"],
                        search_keywords=["{target} 作者", "作者传记", "创作背景", "作者访谈"],
                        analysis_depth="intermediate",
                        visualization_type="timeline"
                    )
                ]
            },
            
            ResearchScenario.INDUSTRY_REPORT: {
                "research_type": "全面深入的行业分析研究",
                "analysis_depth": "专业级",
                "tasks": [
                    ResearchTask(
                        name="行业概况与发展历程",
                        description="分析行业定义、分类、发展阶段和历史沿革",
                        deliverables=["行业基本信息", "发展时间线", "关键里程碑", "发展阶段图"],
                        search_keywords=["{target}行业概况", "{target}行业发展", "{target}行业历史"],
                        analysis_depth="intermediate",
                        visualization_type="timeline"
                    ),
                    ResearchTask(
                        name="市场规模与结构分析",
                        description="量化分析市场规模、增长率、结构特征",
                        deliverables=["市场规模数据", "增长趋势图", "结构分析表", "细分市场占比"],
                        search_keywords=["{target}市场规模", "{target}市场增长", "{target}市场结构"],
                        analysis_depth="advanced",
                        visualization_type="charts_and_graphs"
                    ),
                    ResearchTask(
                        name="竞争格局与主要参与者",
                        description="识别主要竞争者、市场份额、竞争优势",
                        deliverables=["竞争者分析", "市场份额图", "SWOT分析", "竞争优势对比"],
                        search_keywords=["{target}竞争格局", "{target}主要企业", "{target}市场份额"],
                        analysis_depth="advanced",
                        visualization_type="competitive_matrix"
                    ),
                    ResearchTask(
                        name="发展趋势与驱动因素",
                        description="分析行业发展趋势、技术驱动、政策影响",
                        deliverables=["趋势预测", "驱动因素分析", "影响因子图", "技术发展路径"],
                        search_keywords=["{target}发展趋势", "{target}技术驱动", "{target}政策影响"],
                        analysis_depth="advanced",
                        visualization_type="trend_analysis"
                    ),
                    ResearchTask(
                        name="机会与挑战评估",
                        description="识别市场机会、潜在风险、发展瓶颈",
                        deliverables=["机会分析", "风险评估", "挑战应对策略", "机会风险矩阵"],
                        search_keywords=["{target}市场机会", "{target}行业风险", "{target}发展挑战"],
                        analysis_depth="advanced",
                        visualization_type="opportunity_risk_matrix"
                    ),
                    ResearchTask(
                        name="投资建议与战略方向",
                        description="提供投资建议、战略建议、发展建议",
                        deliverables=["投资建议", "战略框架", "行动路径", "投资价值评估"],
                        search_keywords=["{target}投资机会", "{target}投资价值", "{target}战略建议"],
                        analysis_depth="advanced",
                        visualization_type="strategic_framework"
                    )
                ]
            },
            
            ResearchScenario.RESEARCH_PROGRESS: {
                "research_type": "系统梳理和深度分析的研究进展",
                "analysis_depth": "学术级",
                "tasks": [
                    ResearchTask(
                        name="研究现状与基础理论",
                        description="梳理当前研究状况、理论基础、核心概念",
                        deliverables=["研究现状概述", "理论框架图", "概念体系", "基础理论分析"],
                        search_keywords=["{target}研究现状", "{target}理论基础", "{target}核心概念"],
                        analysis_depth="advanced",
                        visualization_type="theoretical_framework"
                    ),
                    ResearchTask(
                        name="关键突破与重要成果",
                        description="识别近期重要突破、关键成果、里程碑事件",
                        deliverables=["突破性成果", "成果时间线", "影响力分析", "创新点总结"],
                        search_keywords=["{target}最新突破", "{target}重要成果", "{target}研究里程碑"],
                        analysis_depth="advanced",
                        visualization_type="breakthrough_timeline"
                    ),
                    ResearchTask(
                        name="技术路线与方法创新",
                        description="分析主要技术路线、方法论创新、实验设计",
                        deliverables=["技术路线图", "方法对比表", "创新点分析", "实验设计总结"],
                        search_keywords=["{target}技术路线", "{target}方法创新", "{target}实验方法"],
                        analysis_depth="advanced",
                        visualization_type="technical_roadmap"
                    ),
                    ResearchTask(
                        name="前沿动态与热点话题",
                        description="追踪最新研究动态、热点议题、争议焦点",
                        deliverables=["热点话题图", "争议分析", "动态追踪", "趋势预测"],
                        search_keywords=["{target}最新动态", "{target}热点话题", "{target}研究争议"],
                        analysis_depth="intermediate",
                        visualization_type="hotspot_map"
                    ),
                    ResearchTask(
                        name="瓶颈分析与技术挑战",
                        description="识别研究瓶颈、技术难点、未解决问题",
                        deliverables=["瓶颈分析", "挑战清单", "解决方案探讨", "技术难点图"],
                        search_keywords=["{target}技术瓶颈", "{target}研究挑战", "{target}未解决问题"],
                        analysis_depth="advanced",
                        visualization_type="challenge_analysis"
                    ),
                    ResearchTask(
                        name="发展预测与研究方向",
                        description="预测未来发展趋势、新兴研究方向、机遇展望",
                        deliverables=["发展预测", "研究方向图", "机遇分析", "未来展望"],
                        search_keywords=["{target}发展趋势", "{target}未来方向", "{target}研究机遇"],
                        analysis_depth="advanced",
                        visualization_type="future_roadmap"
                    )
                ]
            },
            
            ResearchScenario.NEWS_ANALYSIS: {
                "research_type": "深度新闻分析和事件解读",
                "analysis_depth": "深度",
                "tasks": [
                    ResearchTask(
                        name="事件梳理与时间线",
                        description="系统梳理事件发展过程，构建完整时间线",
                        deliverables=["事件时间线", "关键节点", "发展脉络", "事件关联图"],
                        search_keywords=["{target}事件发展", "{target}新闻时间线", "{target}关键节点"],
                        analysis_depth="intermediate",
                        visualization_type="event_timeline"
                    ),
                    ResearchTask(
                        name="深层原因与背景分析",
                        description="挖掘事件背后的深层原因、历史背景、系统性因素",
                        deliverables=["原因分析", "背景解读", "系统性因素", "根本原因图"],
                        search_keywords=["{target}深层原因", "{target}历史背景", "{target}系统因素"],
                        analysis_depth="advanced",
                        visualization_type="causal_analysis"
                    ),
                    ResearchTask(
                        name="影响分析与波及效应",
                        description="分析事件的直接影响、间接影响和长期影响",
                        deliverables=["影响评估", "波及效应", "长期影响", "影响范围图"],
                        search_keywords=["{target}影响分析", "{target}波及效应", "{target}长期影响"],
                        analysis_depth="advanced",
                        visualization_type="impact_analysis"
                    ),
                    ResearchTask(
                        name="各方反应与立场分析",
                        description="分析各利益相关方的反应、立场和应对策略",
                        deliverables=["各方立场", "反应分析", "应对策略", "立场对比表"],
                        search_keywords=["{target}各方反应", "{target}立场分析", "{target}应对策略"],
                        analysis_depth="intermediate",
                        visualization_type="stakeholder_analysis"
                    ),
                    ResearchTask(
                        name="后续发展与趋势预测",
                        description="预测事件后续发展趋势和可能的演变路径",
                        deliverables=["发展预测", "趋势分析", "演变路径", "情景分析"],
                        search_keywords=["{target}后续发展", "{target}趋势预测", "{target}未来走向"],
                        analysis_depth="advanced",
                        visualization_type="scenario_analysis"
                    ),
                    ResearchTask(
                        name="启示思考与经验教训",
                        description="提炼事件的启示意义、经验教训和普遍价值",
                        deliverables=["启示总结", "经验教训", "普遍价值", "思考框架"],
                        search_keywords=["{target}启示意义", "{target}经验教训", "{target}普遍价值"],
                        analysis_depth="advanced",
                        visualization_type="insights_framework"
                    )
                ]
            },
            
            ResearchScenario.MARKET_ANALYSIS: {
                "research_type": "全面的市场分析和商业洞察",
                "analysis_depth": "专业级",
                "tasks": [
                    ResearchTask(
                        name="市场现状与基本面分析",
                        description="分析市场当前状况、基本面数据、整体健康度",
                        deliverables=["市场现状", "基本面数据", "健康度评估", "现状分析图"],
                        search_keywords=["{target}市场现状", "{target}基本面", "{target}市场数据"],
                        analysis_depth="intermediate",
                        visualization_type="market_overview"
                    ),
                    ResearchTask(
                        name="供需关系与平衡分析",
                        description="深入分析市场供需关系、平衡状况、影响因素",
                        deliverables=["供需分析", "平衡状况", "影响因素", "供需关系图"],
                        search_keywords=["{target}供需关系", "{target}市场供应", "{target}市场需求"],
                        analysis_depth="advanced",
                        visualization_type="supply_demand_chart"
                    ),
                    ResearchTask(
                        name="价格走势与影响因素",
                        description="分析价格历史走势、当前水平、影响因素",
                        deliverables=["价格走势", "影响因素", "价格预测", "价格分析图"],
                        search_keywords=["{target}价格走势", "{target}价格分析", "{target}影响因素"],
                        analysis_depth="advanced",
                        visualization_type="price_trend_chart"
                    ),
                    ResearchTask(
                        name="竞争态势与市场地位",
                        description="分析市场竞争格局、主要参与者、竞争策略",
                        deliverables=["竞争分析", "市场地位", "竞争策略", "竞争格局图"],
                        search_keywords=["{target}竞争分析", "{target}市场竞争", "{target}竞争格局"],
                        analysis_depth="advanced",
                        visualization_type="competitive_landscape"
                    ),
                    ResearchTask(
                        name="风险评估与不确定性",
                        description="识别市场风险、不确定性因素、风险管理策略",
                        deliverables=["风险识别", "不确定性分析", "风险管理", "风险矩阵"],
                        search_keywords=["{target}市场风险", "{target}不确定性", "{target}风险管理"],
                        analysis_depth="advanced",
                        visualization_type="risk_matrix"
                    ),
                    ResearchTask(
                        name="策略建议与投资机会",
                        description="提供市场策略建议、投资机会、操作建议",
                        deliverables=["策略建议", "投资机会", "操作建议", "投资策略框架"],
                        search_keywords=["{target}投资策略", "{target}投资机会", "{target}操作建议"],
                        analysis_depth="advanced",
                        visualization_type="investment_strategy"
                    )
                ]
            },
            
            ResearchScenario.BOOK_RECOMMENDATION: {
                "research_type": "专业的图书推荐和评价分析",
                "analysis_depth": "实用性",
                "tasks": [
                    ResearchTask(
                        name="推荐理由与核心价值",
                        description="阐述推荐该书的核心理由和独特价值",
                        deliverables=["推荐理由", "核心价值", "独特性分析", "价值提炼"],
                        search_keywords=["{target}推荐理由", "{target}核心价值", "{target}独特性"],
                        analysis_depth="intermediate"
                    ),
                    ResearchTask(
                        name="内容亮点与精华提炼",
                        description="提取书籍的主要亮点、精华内容、关键洞察",
                        deliverables=["内容亮点", "精华提炼", "关键洞察", "亮点总结"],
                        search_keywords=["{target}内容亮点", "{target}精华内容", "{target}关键洞察"],
                        analysis_depth="advanced"
                    ),
                    ResearchTask(
                        name="适读人群与阅读建议",
                        description="明确适合的读者群体和具体的阅读建议",
                        deliverables=["适读人群", "阅读建议", "阅读方法", "学习路径"],
                        search_keywords=["{target}适读人群", "{target}阅读建议", "{target}学习方法"],
                        analysis_depth="intermediate"
                    ),
                    ResearchTask(
                        name="阅读体验与实用性",
                        description="评估阅读体验、实际应用价值、操作性",
                        deliverables=["阅读体验", "实用性评估", "应用价值", "操作指南"],
                        search_keywords=["{target}阅读体验", "{target}实用性", "{target}应用价值"],
                        analysis_depth="intermediate"
                    ),
                    ResearchTask(
                        name="相关推荐与延伸阅读",
                        description="推荐相关书籍、延伸阅读材料、学习资源",
                        deliverables=["相关推荐", "延伸阅读", "学习资源", "阅读清单"],
                        search_keywords=["{target}相关书籍", "{target}延伸阅读", "{target}学习资源"],
                        analysis_depth="intermediate",
                        visualization_type="reading_roadmap"
                    ),
                    ResearchTask(
                        name="综合评价与购买建议",
                        description="提供综合评价、购买建议、性价比分析",
                        deliverables=["综合评价", "购买建议", "性价比分析", "决策指南"],
                        search_keywords=["{target}综合评价", "{target}购买建议", "{target}性价比"],
                        analysis_depth="intermediate"
                    )
                ]
            }
        }
    
    def _initialize_audience_adaptations(self) -> Dict[str, Dict]:
        """初始化受众适配"""
        return {
            "general": {
                "description": "追求自我提升、终身学习和批判性思维的一般读者",
                "language_style": "清晰、引人入胜且易于理解",
                "complexity_level": "中等",
                "explanation_depth": "简单解释学术概念"
            },
            "professional": {
                "description": "相关领域的专业人士和从业者",
                "language_style": "专业、准确、深入",
                "complexity_level": "高",
                "explanation_depth": "深入的专业分析"
            },
            "academic": {
                "description": "学者、研究人员和学术界人士",
                "language_style": "严谨、学术、引用丰富",
                "complexity_level": "很高",
                "explanation_depth": "详细的理论分析和文献综述"
            },
            "business": {
                "description": "企业决策者、管理者和商业分析师",
                "language_style": "简洁、实用、注重行动",
                "complexity_level": "中高",
                "explanation_depth": "商业应用和实际价值"
            },
            "student": {
                "description": "学生和教育工作者",
                "language_style": "教育性、启发性、结构化",
                "complexity_level": "中等",
                "explanation_depth": "循序渐进的解释和案例"
            }
        }
    
    def generate_research_prompt(self, request: ResearchRequest) -> str:
        """生成研究提示词"""
        
        # 获取场景配置
        scenario_config = self.scenario_configs.get(request.scenario)
        if not scenario_config:
            raise ValueError(f"Unsupported research scenario: {request.scenario}")
        
        # 获取受众适配
        audience_config = self.audience_adaptations.get(request.audience, 
                                                       self.audience_adaptations["general"])
        
        # 生成目标描述
        objective = self.base_framework["objective_template"].format(
            target=request.target,
            research_type=scenario_config["research_type"],
            analysis_depth=scenario_config["analysis_depth"]
        )
        
        # 生成任务列表
        tasks = scenario_config["tasks"]
        tasks_section = self._generate_tasks_section(tasks, request.target)
        
        # 生成搜索关键词
        all_keywords = []
        for task in tasks:
            task_keywords = [kw.format(target=request.target) for kw in task.search_keywords]
            all_keywords.extend(task_keywords)
        
        # 生成通用要求
        general_requirements = self.base_framework["general_requirements"].format(
            language="中文（简体）" if request.language == "zh-CN" else request.language,
            audience_description=audience_config["description"],
            target=request.target
        )
        
        # 生成搜索策略
        search_strategy = self.base_framework["search_strategy"].format(
            target=request.target,
            search_keywords='", "'.join(all_keywords[:20])  # 限制关键词数量
        )
        
        # 生成输出要求
        output_requirements = self.base_framework["output_requirements"].format(
            task_count=len(tasks)
        )
        
        # 构建完整提示词
        full_prompt = f"""深度研究工具提示词: {request.scenario.value.upper()}

目标: {objective}

{general_requirements}

任务和交付物:
{tasks_section}

{search_strategy}

{output_requirements}

附加要求:
{self._generate_additional_requirements(request)}

请开始深度研究过程，提供关于【{request.target}】的全面分析报告。
"""
        
        return full_prompt
    
    def _generate_tasks_section(self, tasks: List[ResearchTask], target: str) -> str:
        """生成任务部分"""
        tasks_text = ""
        for i, task in enumerate(tasks, 1):
            visualization_note = ""
            if task.visualization_type:
                visualization_note = f"\n   - 可视化类型: {task.visualization_type}"
            
            tasks_text += f"""
{i}. {task.name}
   {task.description}
   
   对于每个部分：
   - 交付物: {', '.join(task.deliverables)}
   - 分析深度: {task.analysis_depth}
   - 搜索关键词: {', '.join([kw.format(target=target) for kw in task.search_keywords])}{visualization_note}
   - 说明{task.name}在整体分析中的作用和意义
   
   使用表格呈现关键信息以便快速参考。
"""
        
        return tasks_text
    
    def _generate_additional_requirements(self, request: ResearchRequest) -> str:
        """生成附加要求"""
        additional_req = ""
        
        if request.additional_requirements:
            for key, value in request.additional_requirements.items():
                additional_req += f"- {key}: {value}\n"
        
        # 根据输出格式添加特定要求
        if request.output_format == "video":
            additional_req += """- 视频适配: 内容需要适合视频化表达，包含视觉元素描述
- 时长控制: 考虑视频时长要求，突出关键信息
- 叙事结构: 采用适合视频讲述的叙事结构
"""
        elif request.output_format == "podcast":
            additional_req += """- 音频适配: 内容需要适合音频表达，注重听觉体验
- 对话友好: 采用对话式语言，便于语音播报
- 节奏控制: 考虑音频节奏，适当加入停顿和强调
"""
        elif request.output_format == "interactive":
            additional_req += """- 交互设计: 内容需要支持交互式展示
- 模块化: 采用模块化结构，便于互动展示
- 用户体验: 考虑用户体验，提供多层次的信息展示
"""
        
        return additional_req if additional_req else "- 标准分析报告格式"
    
    def get_scenario_info(self, scenario: ResearchScenario) -> Dict:
        """获取场景信息"""
        return self.scenario_configs.get(scenario, {})
    
    def list_available_scenarios(self) -> List[str]:
        """列出可用场景"""
        return [scenario.value for scenario in ResearchScenario]
    
    def validate_request(self, request: ResearchRequest) -> bool:
        """验证研究请求"""
        if not request.target:
            return False
        
        if request.scenario not in self.scenario_configs:
            return False
        
        if request.audience not in self.audience_adaptations:
            return False
        
        return True

# 使用示例
if __name__ == "__main__":
    # 创建模板系统
    template_system = UniversalResearchPromptTemplate()
    
    # 创建书籍分析请求
    book_request = ResearchRequest(
        scenario=ResearchScenario.BOOK_ANALYSIS,
        target="Structures: Or Why Things Don't Fall Down by J.E. Gordon",
        audience="general",
        output_format="report"
    )
    
    # 生成提示词
    prompt = template_system.generate_research_prompt(book_request)
    print("=== 生成的书籍分析提示词 ===")
    print(prompt)
    
    # 创建行业报告请求
    industry_request = ResearchRequest(
        scenario=ResearchScenario.INDUSTRY_REPORT,
        target="人工智能",
        audience="business",
        output_format="video",
        additional_requirements={"focus_area": "中国市场", "time_frame": "2024-2025"}
    )
    
    # 生成提示词
    industry_prompt = template_system.generate_research_prompt(industry_request)
    print("\n=== 生成的行业报告提示词 ===")
    print(industry_prompt)