import os
import shutil
import uuid
from datetime import datetime
from typing import Dict, Optional
from .config import Config
from .script_generator import ScriptGenerator
from .enhanced_script_generator import EnhancedScriptGenerator
from .image_generator import ImageGenerator
from .tts_generator import TTSGenerator
from .audio_aligner import AudioAligner
from .video_assembler import VideoAssembler
from .video_config import <PERSON>Config, VideoConfigManager
from .book_cover_crawler import BookCoverCrawler
from .background_generator import BackgroundGenerator
from .voice_config import VoiceManager
from .reviewer_personalities import ReviewerPersonalities

class BookReviewPipeline:
    def __init__(self, video_config: VideoConfig = None, voice_id: str = None, personality_id: str = None, use_web_data: bool = True):
        self.video_config = video_config or VideoConfig()
        self.voice_id = voice_id or VoiceManager.get_default_voice()
        self.personality_id = personality_id or ReviewerPersonalities.get_default_personality()
        self.use_web_data = use_web_data
        
        # Log configuration
        print(f"📋 Pipeline configuration:")
        print(f"   🎤 Voice: {self.voice_id}")
        print(f"   🎭 Personality: {self.personality_id}")
        print(f"   🌐 Web data enhancement: {self.use_web_data}")
        
        # Choose script generator based on configuration
        if self.use_web_data:
            self.script_gen = EnhancedScriptGenerator()
            print("   📚 Using enhanced script generator with web data")
        else:
            self.script_gen = ScriptGenerator()
            print("   📝 Using basic script generator")
        
        self.image_gen = ImageGenerator()
        self.tts_gen = TTSGenerator(self.voice_id)
        self.aligner = AudioAligner()
        self.assembler = VideoAssembler(self.video_config)
        self.cover_crawler = BookCoverCrawler()
        self.bg_generator = BackgroundGenerator()
        
        # Create directories
        os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
        os.makedirs(Config.TEMP_DIR, exist_ok=True)
        
    def process_content(self, prompt_text: str, bgm_path: Optional[str] = None, job_id: Optional[str] = None) -> Dict:
        """
        Main pipeline to process content prompt and generate video
        Returns: Dictionary with status and output paths
        """
        
        # Generate unique ID for this job if not provided
        if job_id is None:
            job_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        job_dir = os.path.join(Config.TEMP_DIR, job_id)
        os.makedirs(job_dir, exist_ok=True)
        
        result = {
            "status": "processing",
            "job_id": job_id,
            "prompt_text": prompt_text,
            "steps": {}
        }
        
        try:
            # Step 1: Generate script from prompt
            print(f"[1/6] Generating script from prompt...")
            print(f"   📝 Prompt: {prompt_text[:100]}{'...' if len(prompt_text) > 100 else ''}")
            
            if self.use_web_data and hasattr(self.script_gen, 'generate_from_prompt'):
                # Enhanced script generation with web data and personality
                review_script, illustration_prompt, content_data = self.script_gen.generate_from_prompt(
                    prompt_text, self.personality_id, self.use_web_data
                )
                result["content_data"] = content_data
                result["data_enhanced"] = True
                print(f"✅ Enhanced script generated with {content_data.get('personality_used', 'default')} personality")
                print(f"   📊 Data confidence: {content_data.get('confidence_score', 0):.2%}")
            else:
                # Fallback to basic script generation from prompt
                review_script, illustration_prompt = self.script_gen.generate_from_prompt_basic(prompt_text)
                result["data_enhanced"] = False
                print("✅ Basic script generated from prompt")
            
            result["steps"]["script_generation"] = "completed"
            result["script"] = review_script
            
            # Step 2: Generate cover image from content
            print("[2/6] Generating cover image from content...")
            cover_path = os.path.join(job_dir, "generated_cover.jpg")
            
            # Use the illustration prompt generated from content
            cover_success = self.image_gen.generate_cover_image(illustration_prompt, cover_path)
            
            if not cover_success:
                # Fallback to themed background generation
                print("   🔄 Falling back to themed background generation...")
                background_path = os.path.join(job_dir, "background.jpg")
                bg_success = self.bg_generator.generate_themed_background_from_prompt(
                    prompt_text, self.video_config, background_path
                )
                if bg_success:
                    cover_path = background_path
                    cover_success = True
            
            # Copy final cover to outputs directory for persistent access
            if cover_success and os.path.exists(cover_path):
                persistent_cover_path = os.path.join(Config.OUTPUT_DIR, f"cover_{job_id}.jpg")
                shutil.copy2(cover_path, persistent_cover_path)
                result["persistent_cover_path"] = persistent_cover_path
            
            result["steps"]["image_generation"] = "completed" if cover_success else "failed"
            result["cover_image_path"] = cover_path if cover_success else None
            result["cover_generated"] = cover_success
            
            # Continue with the rest of the pipeline...
            content_metadata = {
                'prompt_text': prompt_text
            }
            return self._continue_audio_and_video_processing(result, review_script, cover_path, job_dir, bgm_path, job_id, content_metadata)
            
        except Exception as e:
            print(f"❌ Pipeline error: {str(e)}")
            result["status"] = "failed"
            result["error"] = str(e)
            return result

    def process_book(self, book_title: str, author: str, bgm_path: Optional[str] = None, job_id: Optional[str] = None) -> Dict:
        """
        Main pipeline to process a book and generate video
        Returns: Dictionary with status and output paths
        """
        
        # Generate unique ID for this job if not provided
        if job_id is None:
            job_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        job_dir = os.path.join(Config.TEMP_DIR, job_id)
        os.makedirs(job_dir, exist_ok=True)
        
        result = {
            "status": "processing",
            "job_id": job_id,
            "book_title": book_title,
            "author": author,
            "steps": {}
        }
        
        try:
            # Step 1: Generate script and illustration prompt
            print(f"[1/6] Generating enhanced script for '{book_title}' by {author}...")
            
            if self.use_web_data and hasattr(self.script_gen, 'generate_enhanced_review'):
                # Enhanced script generation with web data and personality
                review_script, illustration_prompt, book_data = self.script_gen.generate_enhanced_review(
                    book_title, author, self.personality_id, self.use_web_data
                )
                result["book_data"] = book_data
                result["data_enhanced"] = True
                print(f"✅ Enhanced script generated with {book_data.get('personality_used', 'default')} personality")
                print(f"   📊 Data confidence: {book_data.get('confidence_score', 0):.2%}")
            else:
                # Fallback to basic script generation
                review_script, illustration_prompt = self.script_gen.generate_book_review(book_title, author)
                result["data_enhanced"] = False
                print("✅ Basic script generated")
            
            result["steps"]["script_generation"] = "completed"
            result["script"] = review_script
            
            # Step 2: Search and download authentic book cover
            print("[2/6] Searching for authentic book cover...")
            authentic_cover_path = os.path.join(job_dir, "authentic_cover.jpg")
            cover_found = self.cover_crawler.search_book_cover(book_title, author, authentic_cover_path)
            
            # Step 3: Generate themed background
            print("[3/6] Generating themed background...")
            background_path = os.path.join(job_dir, "background.jpg")
            bg_success = self.bg_generator.generate_themed_background(
                book_title, author, self.video_config, background_path
            )
            
            # Step 4: Combine cover and background
            print("[4/6] Combining cover with themed background...")
            final_cover_path = os.path.join(job_dir, "final_cover.jpg")
            
            if cover_found and bg_success:
                overlay_success = self.bg_generator.overlay_book_cover(
                    background_path, authentic_cover_path, self.video_config, final_cover_path
                )
                if overlay_success:
                    cover_path = final_cover_path
                else:
                    # Use background only if overlay fails
                    cover_path = background_path if bg_success else authentic_cover_path
            elif bg_success:
                # Use background only
                cover_path = background_path
            else:
                # Use authentic cover or placeholder
                cover_path = authentic_cover_path
            
            # Copy final cover to outputs directory for persistent access
            if os.path.exists(cover_path):
                persistent_cover_path = os.path.join(Config.OUTPUT_DIR, f"cover_{job_id}.jpg")
                shutil.copy2(cover_path, persistent_cover_path)
                result["persistent_cover_path"] = persistent_cover_path
            
            result["steps"]["image_generation"] = "completed"
            result["cover_image_path"] = cover_path
            result["authentic_cover_found"] = cover_found
            result["background_generated"] = bg_success
            
            # Continue with the rest of the pipeline...
            content_metadata = {
                'title': book_title,
                'author': author
            }
            return self._continue_audio_and_video_processing(result, review_script, cover_path, job_dir, bgm_path, job_id, content_metadata)
            
        except Exception as e:
            print(f"❌ Pipeline error: {str(e)}")
            result["status"] = "failed"
            result["error"] = str(e)
            return result

    def _continue_audio_and_video_processing(self, result: Dict, review_script: str, cover_path: str, job_dir: str, bgm_path: Optional[str], job_id: str, content_metadata: Dict = None) -> Dict:
        """Continue processing with audio and video generation"""
        try:
            # Step 3: Split sentences and generate opening
            print("[3/6] Processing text and generating opening narration...")
            sentences = self.tts_gen.split_sentences(review_script)
            
            # Extract opening sentence
            opening_text = sentences[0] if sentences else review_script[:50]
            opening_audio_path = os.path.join(job_dir, "opening.mp3")
            opening_duration = self.tts_gen.generate_opening_narration(opening_text, opening_audio_path)
            result["steps"]["opening_generation"] = "completed"
            
            # Step 4: Generate audio for all sentences
            print("[4/6] Generating speech for all sentences...")
            audio_segments = self.tts_gen.generate_sentence_audio(sentences, job_dir)
            if not audio_segments:
                raise Exception("Failed to generate audio segments")
            result["steps"]["tts_generation"] = "completed"
            
            # Step 5: Align audio and text
            print("[5/6] Aligning audio and text...")
            alignment_data = self.aligner.align_sentences(audio_segments)
            
            # Split long sentences for better subtitle display
            # 使用更严格的限制：3秒最大显示时间，12个字符最大长度
            alignment_data = self.aligner.split_long_sentences(alignment_data, max_duration=3000, max_chars=12)
            
            result["steps"]["alignment"] = "completed"
            
            # Step 6: Assemble final video with dynamic backgrounds
            print("[6/6] Assembling final video with dynamic backgrounds...")
            
            # Create output filename
            if content_metadata and 'title' in content_metadata and 'author' in content_metadata:
                output_filename = f"{content_metadata['title'].replace(' ', '_')}_{content_metadata['author'].replace(' ', '_')}_{job_id}.mp4"
            else:
                output_filename = f"content_{job_id}.mp4"
            
            output_path = os.path.join(Config.OUTPUT_DIR, output_filename)
            
            # Generate subtitle file for external use
            subtitle_filename = output_filename.replace('.mp4', '.ass')
            subtitle_path = os.path.join(Config.OUTPUT_DIR, subtitle_filename)
            self.assembler.create_ass_subtitles(alignment_data, subtitle_path)
            
            # Use provided BGM or default
            if not bgm_path or not os.path.exists(bgm_path):
                bgm_path = Config.BGM_PATH
            
            # Check if dynamic backgrounds are enabled
            if self.video_config.enable_dynamic_backgrounds:
                print("🎨 Dynamic backgrounds enabled - creating video with animated transitions...")
                video_success = self.assembler.assemble_video_with_dynamic_backgrounds(
                    cover_path,
                    audio_segments,
                    alignment_data,
                    bgm_path,
                    output_path,
                    job_dir,  # Pass temp directory for background image generation
                    content_metadata or {}
                )
            else:
                print("📷 Using static background mode...")
                video_success = self.assembler.assemble_video(
                    cover_path,
                    audio_segments,
                    alignment_data,
                    bgm_path,
                    output_path
                )
            
            if not video_success:
                # Try simple video as fallback
                print("Trying simple video assembly...")
                # Concatenate all audio
                all_audio_path = os.path.join(job_dir, "full_narration.mp3")
                audio_files = [seg[1] for seg in audio_segments]
                self.tts_gen.concatenate_audio_files(audio_files, all_audio_path)
                
                video_success = self.assembler.create_simple_video(
                    cover_path,
                    all_audio_path,
                    output_path
                )
            
            if video_success:
                result["steps"]["video_assembly"] = "completed"
                result["status"] = "completed"
                result["output_path"] = output_path
                result["output_url"] = f"/download/{output_filename}"
            else:
                raise Exception("Failed to assemble video")
            
        except Exception as e:
            print(f"❌ Audio/Video processing error: {str(e)}")
            result["status"] = "failed"
            result["error"] = str(e)
        
        finally:
            # Clean up temp files
            try:
                shutil.rmtree(job_dir)
            except:
                pass
        
        return result
    
    def get_sample_bgm_list(self) -> list:
        """Get list of available BGM files with metadata"""
        bgm_dir = "./assets/bgm"
        metadata_file = os.path.join(bgm_dir, "bgm_metadata.json")
        
        if not os.path.exists(bgm_dir):
            return []
        
        # Try to load metadata
        bgm_metadata = {}
        if os.path.exists(metadata_file):
            try:
                import json
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    for bgm in metadata.get('bgm_files', []):
                        bgm_metadata[bgm['filename']] = bgm
            except Exception as e:
                print(f"⚠️ Failed to load BGM metadata: {e}")
        
        bgm_files = []
        
        # First, add actual files that exist
        for file in os.listdir(bgm_dir):
            if file.endswith(('.mp3', '.wav', '.m4a')) and file != 'bgm_metadata.json':
                file_path = os.path.join(bgm_dir, file)
                
                if os.path.exists(file_path):  # Only include files that actually exist
                    # Get metadata if available
                    if file in bgm_metadata:
                        metadata = bgm_metadata[file]
                        bgm_files.append({
                            "filename": file,
                            "name": metadata.get('name', file),
                            "description": metadata.get('description', ''),
                            "style": metadata.get('style', 'general'),
                            "duration": metadata.get('duration', 0),
                            "tags": metadata.get('tags', []),
                            "path": file_path,
                            "exists": True,
                            "file_size": os.path.getsize(file_path)
                        })
                    else:
                        # Fallback for files without metadata
                        bgm_files.append({
                            "filename": file,
                            "name": file.replace('.mp3', '').replace('_', ' ').title(),
                            "description": f"背景音乐文件：{file}",
                            "style": "general",
                            "duration": 0,
                            "tags": [],
                            "path": file_path,
                            "exists": True,
                            "file_size": os.path.getsize(file_path)
                        })
        
        # Add placeholder entries for metadata without actual files (marked as unavailable)
        for filename, metadata in bgm_metadata.items():
            file_path = os.path.join(bgm_dir, filename)
            if not os.path.exists(file_path):
                bgm_files.append({
                    "filename": filename,
                    "name": f"{metadata.get('name', filename)} (即将上线)",
                    "description": f"{metadata.get('description', '')} [文件暂未上传]",
                    "style": metadata.get('style', 'general'),
                    "duration": metadata.get('duration', 0),
                    "tags": metadata.get('tags', []),
                    "path": file_path,
                    "exists": False,
                    "file_size": 0
                })
        
        # Sort by name
        bgm_files.sort(key=lambda x: x['name'])
        return bgm_files