import os
import requests
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import random
from .config import Config
from .video_config import VideoConfig, VideoConfigManager
from .concept_expander import ConceptExpander

# Import official Volcano Engine SDK
try:
    from volcenginesdkarkruntime import Ark
    VOLCENGINE_SDK_AVAILABLE = True
except ImportError:
    VOLCENGINE_SDK_AVAILABLE = False

class BackgroundGenerator:
    """Generate themed backgrounds for book review videos"""
    
    def __init__(self):
        self.api_key = Config.DOUBAO_API_KEY
        self.base_url = Config.DOUBAO_IMAGE_ENDPOINT
        self.model_id = Config.DOUBAO_MODEL_ID
        
        # Initialize official SDK client
        if VOLCENGINE_SDK_AVAILABLE:
            self.ark_client = Ark(
                base_url=self.base_url,
                api_key=self.api_key
            )
        else:
            self.ark_client = None
        
        # Initialize concept expander for enhanced image generation
        self.concept_expander = ConceptExpander()
    
    def generate_themed_background(self, book_title: str, author: str, 
                                 video_config: VideoConfig, output_path: str) -> bool:
        """
        Generate a themed background matching the book's content
        Returns: True if successful, False otherwise
        """
        
        # Get video dimensions
        width, height = VideoConfigManager.get_dimensions(video_config)
        
        # Create themed prompt based on book
        theme_prompt = self.create_theme_prompt(book_title, author, video_config)
        
        # Try to generate with AI first
        if self.ark_client and VOLCENGINE_SDK_AVAILABLE:
            success = self.generate_ai_background(theme_prompt, width, height, output_path)
            if success:
                return True
        
        # Fallback to procedural generation
        print("AI generation failed, creating procedural background...")
        return self.create_procedural_background(book_title, author, width, height, output_path)
    
    def create_theme_prompt(self, book_title: str, author: str, video_config: VideoConfig) -> str:
        """Create a themed prompt based on book content and video configuration"""
        
        # Get aspect ratio description
        aspect_desc = {
            "9:16": "竖版手机屏幕比例",
            "16:9": "横版宽屏比例",
            "1:1": "正方形社交媒体比例"
        }[video_config.aspect_ratio.value]
        
        # Basic theme prompt
        prompt = f"""
        创建一个优雅的书籍主题背景图，用于《{book_title}》（作者：{author}）的书评视频。
        
        ## 设计要求：
        - 画面比例：{aspect_desc}
        - 风格：简约典雅，文学气息浓厚
        - 色调：温暖柔和，适合阅读氛围
        - 元素：书架、书籍、文字、古典文化符号
        
        ## 视觉效果：
        - 渐变背景，从深色到浅色过渡
        - 模糊的书籍和文字作为装饰元素
        - 留出足够空间放置封面图片
        - 整体氛围宁静、知性、文艺
        
        ## 技术规格：
        - 高分辨率，适合视频制作
        - 主体内容区域保持相对空白
        - 边缘装饰性元素丰富
        - 色彩搭配和谐统一
        
        ## 文化元素：
        - 融入中国传统文化符号
        - 体现书籍和知识的价值
        - 营造学习和思考的氛围
        - 展现文学作品的魅力
        """
        
        return prompt
    
    def generate_ai_background(self, prompt: str, width: int, height: int, output_path: str) -> bool:
        """Generate background using AI image generation"""
        try:
            print(f"Generating AI background with Doubao API...")
            print(f"Dimensions: {width}x{height}")
            
            # Generate image using official SDK
            images_response = self.ark_client.images.generate(
                model=self.model_id,
                prompt=prompt
            )
            
            if not images_response.data:
                print("❌ No images generated")
                return False
            
            # Get the first image URL
            image_url = images_response.data[0].url
            print(f"Generated background URL: {image_url}")
            
            # Download the image
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            # Save and resize the image
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            # Resize to exact dimensions
            with Image.open(output_path) as img:
                img = img.resize((width, height), Image.Resampling.LANCZOS)
                img.save(output_path, 'JPEG', quality=95)
            
            print(f"✅ AI background saved: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ AI background generation failed: {e}")
            return False
    
    def create_procedural_background(self, book_title: str, author: str, 
                                   width: int, height: int, output_path: str) -> bool:
        """Create a procedural background using PIL"""
        try:
            print(f"Creating procedural background: {width}x{height}")
            
            # Create base gradient background
            img = self.create_gradient_background(width, height)
            
            # Add decorative elements
            img = self.add_book_elements(img, book_title, author)
            
            # Add subtle texture
            img = self.add_texture(img)
            
            # Save the background
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            img.save(output_path, 'JPEG', quality=95)
            
            print(f"✅ Procedural background saved: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Procedural background creation failed: {e}")
            return False
    
    def create_gradient_background(self, width: int, height: int) -> Image.Image:
        """Create a gradient background"""
        
        # Define color schemes for different moods
        color_schemes = [
            # Warm academic
            [(245, 240, 230), (220, 200, 180)],
            # Cool literary
            [(230, 235, 245), (200, 210, 230)],
            # Classic scholarly
            [(240, 235, 225), (210, 200, 185)],
            # Modern minimal
            [(250, 250, 250), (235, 235, 235)]
        ]
        
        # Choose a color scheme
        top_color, bottom_color = random.choice(color_schemes)
        
        # Create gradient
        img = Image.new('RGB', (width, height))
        draw = ImageDraw.Draw(img)
        
        # Draw gradient
        for y in range(height):
            ratio = y / height
            r = int(top_color[0] + (bottom_color[0] - top_color[0]) * ratio)
            g = int(top_color[1] + (bottom_color[1] - top_color[1]) * ratio)
            b = int(top_color[2] + (bottom_color[2] - top_color[2]) * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        return img
    
    def add_book_elements(self, img: Image.Image, book_title: str, author: str) -> Image.Image:
        """Add decorative book-related elements"""
        draw = ImageDraw.Draw(img)
        width, height = img.size
        
        # Add subtle geometric patterns
        pattern_color = (0, 0, 0, 20)  # Very light overlay
        
        # Draw subtle book spine patterns on the sides
        spine_width = width // 20
        for i in range(0, height, 40):
            # Left side
            draw.rectangle([0, i, spine_width, i + 30], fill=pattern_color)
            # Right side  
            draw.rectangle([width - spine_width, i + 15, width, i + 35], fill=pattern_color)
        
        # Add corner decorations
        corner_size = min(width, height) // 8
        
        # Top corners - subtle ornamental lines
        for i in range(5):
            offset = i * 3
            # Top left
            draw.line([(offset, 0), (corner_size - offset, 0)], fill=(0, 0, 0, 30), width=1)
            draw.line([(0, offset), (0, corner_size - offset)], fill=(0, 0, 0, 30), width=1)
            # Top right
            draw.line([(width - corner_size + offset, 0), (width - offset, 0)], fill=(0, 0, 0, 30), width=1)
            draw.line([(width, offset), (width, corner_size - offset)], fill=(0, 0, 0, 30), width=1)
        
        return img
    
    def add_texture(self, img: Image.Image) -> Image.Image:
        """Add subtle texture to the background"""
        
        # Create a noise texture
        width, height = img.size
        noise = Image.new('L', (width, height))
        noise_pixels = []
        
        for _ in range(width * height):
            noise_pixels.append(random.randint(240, 255))
        
        noise.putdata(noise_pixels)
        
        # Apply very light blur to the noise
        noise = noise.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        # Convert to RGBA for blending
        noise_rgba = Image.new('RGBA', (width, height))
        for y in range(height):
            for x in range(width):
                pixel_value = noise.getpixel((x, y))
                alpha = 10  # Very subtle
                noise_rgba.putpixel((x, y), (pixel_value, pixel_value, pixel_value, alpha))
        
        # Blend with original image
        img_rgba = img.convert('RGBA')
        blended = Image.alpha_composite(img_rgba, noise_rgba)
        
        return blended.convert('RGB')
    
    def overlay_book_cover(self, background_path: str, cover_path: str, 
                          video_config: VideoConfig, output_path: str) -> bool:
        """Overlay book cover onto the themed background"""
        try:
            print(f"Overlaying book cover onto background...")
            
            # Open images
            background = Image.open(background_path)
            cover = Image.open(cover_path)
            
            bg_width, bg_height = background.size
            
            # Calculate cover placement based on video aspect ratio
            if video_config.aspect_ratio.value == "9:16":
                # Mobile vertical - place cover in upper portion
                cover_height = bg_height // 3
                cover_width = int(cover_height * (cover.width / cover.height))
                y_position = bg_height // 6
            elif video_config.aspect_ratio.value == "16:9":
                # Desktop horizontal - place cover on left side
                cover_height = bg_height // 2
                cover_width = int(cover_height * (cover.width / cover.height))
                y_position = bg_height // 4
            else:  # 1:1 square
                # Square - place cover in center-left
                cover_height = int(bg_height / 2.5)  # Use int() to ensure integer result
                cover_width = int(cover_height * (cover.width / cover.height))
                y_position = int((bg_height - cover_height) / 2)
            
            # Resize cover
            cover_resized = cover.resize((cover_width, cover_height), Image.Resampling.LANCZOS)
            
            # Calculate x position (slightly left of center)
            x_position = (bg_width - cover_width) // 3
            
            # Add subtle shadow behind cover
            shadow_offset = 8
            shadow = Image.new('RGBA', background.size, (0, 0, 0, 0))
            shadow_draw = ImageDraw.Draw(shadow)
            shadow_draw.rectangle([
                x_position + shadow_offset, 
                y_position + shadow_offset,
                x_position + cover_width + shadow_offset,
                y_position + cover_height + shadow_offset
            ], fill=(0, 0, 0, 60))
            
            # Blur the shadow
            shadow = shadow.filter(ImageFilter.GaussianBlur(radius=5))
            
            # Composite everything
            background_rgba = background.convert('RGBA')
            result = Image.alpha_composite(background_rgba, shadow)
            result.paste(cover_resized, (x_position, y_position))
            
            # Save final image
            final_img = result.convert('RGB')
            final_img.save(output_path, 'JPEG', quality=95)
            
            print(f"✅ Book cover overlaid successfully: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Cover overlay failed: {e}")
            return False

    def generate_themed_background_from_prompt(self, prompt_text: str, video_config: VideoConfig, output_path: str) -> bool:
        """
        Generate a themed background based on content prompt using concept expansion
        Returns: True if successful, False otherwise
        """
        
        # Get video dimensions
        width, height = VideoConfigManager.get_dimensions(video_config)
        
        # Expand concepts from prompt for better visual understanding
        print("🧠 Expanding concepts for enhanced image generation...")
        semantic_context = self.concept_expander.expand_user_input(prompt_text)
        
        # Create enhanced visual prompt using expanded concepts
        visual_prompt = self.create_enhanced_visual_prompt(prompt_text, semantic_context, video_config)
        
        # Try to generate with AI first
        if self.ark_client and VOLCENGINE_SDK_AVAILABLE:
            success = self.generate_ai_background(visual_prompt, width, height, output_path)
            if success:
                return True
        
        # Fallback to procedural generation with expanded concepts
        print("Falling back to enhanced procedural background generation")
        return self.create_enhanced_procedural_background(prompt_text, semantic_context, video_config, output_path)

    def create_visual_prompt_from_content(self, prompt_text: str, video_config: VideoConfig) -> str:
        """Create visual description from content prompt"""
        
        # Get video orientation for appropriate composition
        width, height = VideoConfigManager.get_dimensions(video_config)
        orientation = "vertical" if height > width else "horizontal"
        
        # Analyze prompt for visual cues
        content_keywords = prompt_text.lower()
        
        # Base visual style
        base_prompt = f"现代简约风格背景图，{orientation}构图，适合视频内容"
        
        # Add style based on content type
        if any(keyword in content_keywords for keyword in ["书评", "文学", "阅读", "书籍"]):
            visual_style = "书香气息，温暖的色调，简洁的几何元素，体现知识与文化的氛围"
        elif any(keyword in content_keywords for keyword in ["科技", "技术", "AI", "数字"]):
            visual_style = "现代科技感，蓝色调为主，简洁的线条和光效，体现创新与未来"
        elif any(keyword in content_keywords for keyword in ["生活", "情感", "心理", "人生"]):
            visual_style = "温暖人文色调，柔和渐变，舒缓的氛围，体现生活的美好"
        elif any(keyword in content_keywords for keyword in ["商业", "经济", "管理", "职场"]):
            visual_style = "专业商务风格，简洁大气，深色调配金色点缀，体现专业与权威"
        elif any(keyword in content_keywords for keyword in ["教育", "学习", "知识", "培训"]):
            visual_style = "清新学术风格，明亮色调，简洁的图形元素，营造学习氛围"
        else:
            visual_style = "现代简约风格，和谐色彩搭配，简洁美观，适合多种内容主题"
        
        # Combine elements
        final_prompt = f"{base_prompt}，{visual_style}。画面构图简洁明了，色彩搭配和谐，具有很强的视觉吸引力，适合作为视频背景使用。"
        
        print(f"🎨 Generated visual prompt: {final_prompt[:100]}...")
        return final_prompt

    def create_procedural_background_from_prompt(self, prompt_text: str, video_config: VideoConfig, output_path: str) -> bool:
        """Create procedural background based on content prompt"""
        
        try:
            width, height = VideoConfigManager.get_dimensions(video_config)
            
            # Analyze prompt to determine color scheme
            content_lower = prompt_text.lower()
            
            if any(keyword in content_lower for keyword in ["书评", "文学", "阅读"]):
                # Warm, literary colors
                colors = [(245, 235, 220), (210, 180, 140), (139, 115, 85)]
            elif any(keyword in content_lower for keyword in ["科技", "AI", "技术"]):
                # Cool, tech colors
                colors = [(25, 25, 112), (70, 130, 180), (176, 196, 222)]
            elif any(keyword in content_lower for keyword in ["商业", "经济", "职场"]):
                # Professional colors
                colors = [(47, 79, 79), (105, 105, 105), (169, 169, 169)]
            elif any(keyword in content_lower for keyword in ["教育", "学习", "知识"]):
                # Fresh, educational colors
                colors = [(240, 248, 255), (135, 206, 235), (70, 130, 180)]
            else:
                # Default warm colors
                colors = [(250, 245, 240), (245, 222, 179), (222, 184, 135)]
            
            # Create gradient background
            img = self.create_gradient_background_with_colors(width, height, colors)
            
            # Add subtle pattern based on content type
            img = self.add_content_pattern(img, content_lower)
            
            # Add texture
            img = self.add_texture(img)
            
            # Save image
            img_rgb = img.convert('RGB')
            img_rgb.save(output_path, 'JPEG', quality=95)
            
            print(f"✅ Procedural background created from prompt: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Procedural background creation failed: {e}")
            return False

    def create_gradient_background_with_colors(self, width: int, height: int, colors: list) -> Image.Image:
        """Create gradient background with specific colors"""
        
        img = Image.new('RGBA', (width, height))
        
        # Create vertical gradient with multiple colors
        for y in range(height):
            # Calculate color based on position
            ratio = y / height
            
            if ratio < 0.5:
                # Interpolate between first and second color
                local_ratio = ratio * 2
                r = int(colors[0][0] * (1 - local_ratio) + colors[1][0] * local_ratio)
                g = int(colors[0][1] * (1 - local_ratio) + colors[1][1] * local_ratio)
                b = int(colors[0][2] * (1 - local_ratio) + colors[1][2] * local_ratio)
            else:
                # Interpolate between second and third color
                local_ratio = (ratio - 0.5) * 2
                r = int(colors[1][0] * (1 - local_ratio) + colors[2][0] * local_ratio)
                g = int(colors[1][1] * (1 - local_ratio) + colors[2][1] * local_ratio)
                b = int(colors[1][2] * (1 - local_ratio) + colors[2][2] * local_ratio)
            
            # Draw horizontal line
            for x in range(width):
                img.putpixel((x, y), (r, g, b, 255))
        
        return img

    def add_content_pattern(self, img: Image.Image, content_type: str) -> Image.Image:
        """Add subtle pattern based on content type"""
        
        draw = ImageDraw.Draw(img)
        width, height = img.size
        
        # Very subtle pattern overlay
        pattern_alpha = 15  # Very light
        
        if "书" in content_type or "文学" in content_type:
            # Add subtle book-like lines
            line_spacing = height // 20
            for i in range(0, height, line_spacing):
                draw.line([(0, i), (width, i)], fill=(0, 0, 0, pattern_alpha), width=1)
        
        elif "科技" in content_type or "技术" in content_type:
            # Add subtle grid pattern
            grid_size = min(width, height) // 15
            for x in range(0, width, grid_size):
                draw.line([(x, 0), (x, height)], fill=(255, 255, 255, pattern_alpha), width=1)
            for y in range(0, height, grid_size):
                draw.line([(0, y), (width, y)], fill=(255, 255, 255, pattern_alpha), width=1)
        
        elif "商业" in content_type or "经济" in content_type:
            # Add subtle diagonal lines
            spacing = min(width, height) // 10
            for i in range(-height, width, spacing):
                draw.line([(i, 0), (i + height, height)], fill=(255, 255, 255, pattern_alpha), width=1)
        
        return img
    
    def create_enhanced_visual_prompt(self, prompt_text: str, semantic_context, video_config: VideoConfig) -> str:
        """Create enhanced visual description using expanded concepts"""
        
        # Get video orientation for appropriate composition
        width, height = VideoConfigManager.get_dimensions(video_config)
        orientation = "vertical" if height > width else "horizontal"
        
        # Extract visual elements from semantic analysis
        domain = semantic_context.domain
        key_concepts = [c.original_concept for c in semantic_context.expanded_concepts[:3]]
        visual_themes = []
        
        # Collect visual themes from expanded concepts
        for concept in semantic_context.expanded_concepts[:2]:
            visual_themes.extend(concept.associated_topics[:2])
        
        # Domain-specific visual styles
        domain_styles = {
            "文学": "书香气息浓厚，温暖金棕色调，古典装饰元素，文化韵味",
            "科技": "现代科技感，深蓝渐变色调，几何线条，未来主义设计",
            "商业": "专业商务风格，深灰配金色，简洁大气，权威感",
            "教育": "清新学术氛围，明亮蓝绿色调，知识符号元素",
            "生活": "温暖生活气息，柔和暖色调，自然舒适元素",
            "产品": "现代设计感，简约线条，高品质视觉效果",
            "通用": "现代简约风格，和谐色彩搭配，平衡美观"
        }
        
        base_style = domain_styles.get(domain, domain_styles["通用"])
        
        # Build comprehensive visual prompt
        enhanced_prompt = f"""
        创建一个{orientation}构图的高质量背景图，具有以下特征：
        
        ## 主题背景
        领域：{domain}
        核心概念：{', '.join(key_concepts) if key_concepts else '通用内容'}
        视觉主题：{', '.join(visual_themes[:4]) if visual_themes else '简约现代'}
        
        ## 视觉风格
        {base_style}
        
        ## 构图要求
        - 画面构图：{orientation}，适合视频背景
        - 视觉层次：清晰的主次关系，便于叠加文字和图像
        - 色彩搭配：和谐统一，符合主题氛围
        - 细节处理：精致美观，高质量渲染
        
        ## 技术规格
        - 分辨率：{width}x{height}像素
        - 画质：高清，适合视频制作
        - 格式：适合作为动态视频背景
        - 兼容性：确保在不同设备上显示效果良好
        
        ## 设计原则
        - 内容适配：为文字和图像叠加预留空间
        - 视觉平衡：避免过于复杂的图案干扰主要内容
        - 美学价值：体现专业设计水准，提升整体品质
        - 主题呼应：视觉元素与内容主题高度契合
        """
        
        print(f"🎨 Enhanced visual prompt created with {len(key_concepts)} concepts and {len(visual_themes)} themes")
        return enhanced_prompt
    
    def create_enhanced_procedural_background(self, prompt_text: str, semantic_context, video_config: VideoConfig, output_path: str) -> bool:
        """Create enhanced procedural background using semantic analysis"""
        
        try:
            width, height = VideoConfigManager.get_dimensions(video_config)
            
            # Determine color scheme based on domain and concepts
            domain = semantic_context.domain
            concepts = [c.original_concept for c in semantic_context.expanded_concepts]
            
            # Enhanced color selection based on semantic analysis
            color_scheme = self._select_enhanced_color_scheme(domain, concepts, prompt_text)
            
            # Create gradient background with selected colors
            img = self.create_gradient_background_with_colors(width, height, color_scheme)
            
            # Add semantic-based patterns
            img = self._add_semantic_patterns(img, semantic_context)
            
            # Add conceptual visual elements
            img = self._add_conceptual_elements(img, semantic_context)
            
            # Add texture
            img = self.add_texture(img)
            
            # Save image
            img_rgb = img.convert('RGB')
            img_rgb.save(output_path, 'JPEG', quality=95)
            
            print(f"✅ Enhanced procedural background created with semantic analysis: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Enhanced procedural background creation failed: {e}")
            # Fallback to original method
            return self.create_procedural_background_from_prompt(prompt_text, video_config, output_path)
    
    def _select_enhanced_color_scheme(self, domain: str, concepts: list, prompt_text: str) -> list:
        """Select color scheme based on semantic analysis"""
        
        # Domain-based color schemes
        domain_colors = {
            "文学": [(245, 235, 220), (210, 180, 140), (160, 130, 95)],  # Warm literary
            "科技": [(25, 25, 50), (70, 130, 180), (135, 206, 235)],      # Cool tech
            "商业": [(47, 79, 79), (105, 105, 105), (169, 169, 169)],     # Professional
            "教育": [(240, 248, 255), (135, 206, 235), (70, 130, 180)],   # Fresh academic
            "生活": [(255, 245, 238), (245, 222, 179), (222, 184, 135)],  # Warm life
            "产品": [(248, 248, 255), (220, 220, 220), (169, 169, 169)]   # Clean product
        }
        
        base_colors = domain_colors.get(domain, [(250, 245, 240), (245, 222, 179), (222, 184, 135)])
        
        # Adjust colors based on specific concepts
        content_lower = prompt_text.lower()
        
        # Concept-based adjustments
        if any(word in content_lower for word in ['创新', '未来', '发展']):
            # Add more blue/purple tones for innovation
            base_colors = [(r-10, g-5, b+15) for r, g, b in base_colors]
        
        if any(word in content_lower for word in ['传统', '经典', '历史']):
            # Add warmer, more traditional tones
            base_colors = [(r+15, g+10, b-5) for r, g, b in base_colors]
        
        if any(word in content_lower for word in ['健康', '自然', '环保']):
            # Add green tones
            base_colors = [(r-5, g+10, b-5) for r, g, b in base_colors]
        
        # Ensure colors are within valid range
        normalized_colors = []
        for r, g, b in base_colors:
            normalized_colors.append((
                max(0, min(255, r)),
                max(0, min(255, g)),
                max(0, min(255, b))
            ))
        
        return normalized_colors
    
    def _add_semantic_patterns(self, img: Image.Image, semantic_context) -> Image.Image:
        """Add patterns based on semantic analysis"""
        
        draw = ImageDraw.Draw(img)
        width, height = img.size
        domain = semantic_context.domain
        
        # Very subtle pattern overlay
        pattern_alpha = 12  # Even lighter than before
        
        if domain == "文学":
            # Subtle book-like elements
            line_spacing = height // 25
            for i in range(0, height, line_spacing):
                if i % (line_spacing * 3) == 0:  # Every third line
                    draw.line([(width//10, i), (width*9//10, i)], fill=(0, 0, 0, pattern_alpha), width=1)
        
        elif domain == "科技":
            # Minimal tech grid
            grid_size = min(width, height) // 20
            for x in range(0, width, grid_size * 3):  # Wider spacing
                draw.line([(x, 0), (x, height)], fill=(100, 150, 200, pattern_alpha), width=1)
            for y in range(0, height, grid_size * 3):
                draw.line([(0, y), (width, y)], fill=(100, 150, 200, pattern_alpha), width=1)
        
        elif domain == "商业":
            # Subtle diagonal emphasis
            spacing = min(width, height) // 12
            for i in range(-height, width, spacing * 2):  # Wider spacing
                draw.line([(i, 0), (i + height//2, height//2)], fill=(255, 255, 255, pattern_alpha), width=1)
        
        return img
    
    def _add_conceptual_elements(self, img: Image.Image, semantic_context) -> Image.Image:
        """Add visual elements based on expanded concepts"""
        
        draw = ImageDraw.Draw(img)
        width, height = img.size
        
        # Add very subtle conceptual symbols
        for i, concept in enumerate(semantic_context.expanded_concepts[:2]):
            if concept.associated_topics:
                # Add small decorative elements in corners
                corner_size = min(width, height) // 20
                alpha = 15
                
                if i == 0:  # Top-left area
                    for j in range(3):
                        x = j * (corner_size // 4)
                        y = j * (corner_size // 4)
                        draw.ellipse([x, y, x + 5, y + 5], fill=(0, 0, 0, alpha))
                
                elif i == 1:  # Bottom-right area
                    for j in range(3):
                        x = width - corner_size + j * (corner_size // 4)
                        y = height - corner_size + j * (corner_size // 4)
                        draw.ellipse([x, y, x + 5, y + 5], fill=(0, 0, 0, alpha))
        
        return img