"""
Enhanced Workflow Pipeline
增强工作流管道

整合深度研究Agent到原有视频生成系统的完整流程：
用户输入 → 提示词增强重写 → 深度研究报告 → 编写视频脚本/播客脚本 → 媒体生成
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path

# 导入深度研究组件
from universal_research_template import (
    UniversalResearchPromptTemplate, 
    ResearchRequest, 
    ResearchScenario
)
from deep_research_agent import DeepResearchAgent, ResearchResult

# 导入原有组件
import sys
import os
from pathlib import Path

# 确保可以导入src目录下的模块
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

try:
    from concept_expander import ConceptExpander
    from doubao_search_enhancer import DoubaoSearchEnhancer  
    from enhanced_script_generator import EnhancedScriptGenerator
    from doubao_tts import DoubaoTTS
    from video_assembler import VideoAssembler
    from image_generator import ImageGenerator
    from video_config import VideoConfig
    from config import Config
    
    print("✅ 所有生产组件加载成功")
    USE_MOCK_COMPONENTS = False
except ImportError as e:
    print(f"⚠️  部分原有组件未找到: {str(e)}，使用模拟组件")
    USE_MOCK_COMPONENTS = True
    
    class ConceptExpander:
        async def expand_user_input(self, text):
            from dataclasses import dataclass
            @dataclass
            class MockSemanticContext:
                domain = "general"
                content_type = "analysis"
                intent = "research"
                expanded_concepts = []
            return MockSemanticContext()
    
    class DoubaoSearchEnhancer:
        async def enhance_prompt_with_search(self, prompt, context=None):
            return prompt, {"search_results": []}
    
    class EnhancedScriptGenerator:
        async def generate_from_prompt(self, prompt, **kwargs):
            return f"模拟生成的脚本内容: {prompt[:100]}..."
    
    class DoubaoTTS:
        async def generate_audio(self, **kwargs):
            return "temp/mock_audio.wav"
    
    class VideoAssembler:
        async def assemble_video(self, **kwargs):
            return "temp/mock_video.mp4"
    
    class ImageGenerator:
        async def generate_image(self, **kwargs):
            return "temp/mock_image.jpg"
    
    class VideoConfig:
        def __init__(self):
            self.aspect_ratio = "16:9"
            self.resolution = "1920x1080"
            self.duration = 300
    
    class Config:
        pass

class WorkflowStep(Enum):
    """工作流步骤枚举"""
    USER_INPUT = "user_input"
    PROMPT_ENHANCEMENT = "prompt_enhancement"
    DEEP_RESEARCH = "deep_research"
    SCRIPT_GENERATION = "script_generation"
    MEDIA_PRODUCTION = "media_production"
    FINAL_OUTPUT = "final_output"

class OutputFormat(Enum):
    """输出格式枚举"""
    RESEARCH_REPORT = "research_report"
    VIDEO_SCRIPT = "video_script"
    PODCAST_SCRIPT = "podcast_script"
    FULL_VIDEO = "full_video"
    AUDIO_PODCAST = "audio_podcast"

@dataclass
class WorkflowRequest:
    """工作流请求"""
    user_input: str
    research_scenario: ResearchScenario
    output_format: OutputFormat
    audience: str = "general"
    video_config: Optional[VideoConfig] = None
    additional_requirements: Optional[Dict[str, Any]] = None
    
@dataclass
class WorkflowStepResult:
    """工作流步骤结果"""
    step: WorkflowStep
    success: bool
    content: str
    metadata: Dict[str, Any]
    processing_time: float
    timestamp: datetime

@dataclass
class WorkflowResult:
    """完整工作流结果"""
    request: WorkflowRequest
    steps: List[WorkflowStepResult]
    final_output: str
    media_files: List[str]
    quality_score: float
    total_processing_time: float
    timestamp: datetime

class EnhancedWorkflowPipeline:
    """增强工作流管道"""
    
    def __init__(self):
        # 核心组件
        self.concept_expander = ConceptExpander()
        self.search_enhancer = DoubaoSearchEnhancer()
        self.research_agent = DeepResearchAgent()
        self.script_generator = EnhancedScriptGenerator()
        self.tts_engine = DoubaoTTS()
        self.video_assembler = VideoAssembler()
        self.image_generator = ImageGenerator()
        
        # 配置
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        
        # 性能统计
        self.workflow_stats = {
            "total_processed": 0,
            "success_count": 0,
            "average_processing_time": 0.0,
            "step_performance": {}
        }
    
    async def execute_workflow(self, request: WorkflowRequest) -> WorkflowResult:
        """执行完整工作流"""
        start_time = datetime.now()
        workflow_steps = []
        
        try:
            self.logger.info(f"开始执行工作流: {request.user_input}")
            
            # 步骤1: 用户输入处理
            step1_result = await self._process_user_input(request)
            workflow_steps.append(step1_result)
            
            # 步骤2: 提示词增强重写
            step2_result = await self._enhance_and_rewrite_prompt(
                request, step1_result.content
            )
            workflow_steps.append(step2_result)
            
            # 步骤3: 深度研究报告
            step3_result = await self._conduct_deep_research(
                request, step2_result.content
            )
            workflow_steps.append(step3_result)
            
            # 步骤4: 脚本生成
            step4_result = await self._generate_script(
                request, step3_result.content
            )
            workflow_steps.append(step4_result)
            
            # 步骤5: 媒体制作
            step5_result = await self._produce_media(
                request, step4_result.content
            )
            workflow_steps.append(step5_result)
            
            # 计算质量评分
            quality_score = self._calculate_workflow_quality(workflow_steps)
            
            # 计算总处理时间
            total_time = (datetime.now() - start_time).total_seconds()
            
            # 构建结果
            result = WorkflowResult(
                request=request,
                steps=workflow_steps,
                final_output=step5_result.content,
                media_files=step5_result.metadata.get("media_files", []),
                quality_score=quality_score,
                total_processing_time=total_time,
                timestamp=datetime.now()
            )
            
            # 更新统计
            self._update_workflow_stats(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"工作流执行失败: {str(e)}")
            raise
    
    async def _process_user_input(self, request: WorkflowRequest) -> WorkflowStepResult:
        """步骤1: 处理用户输入"""
        step_start = datetime.now()
        
        try:
            # 解析用户输入
            parsed_input = {
                "original_input": request.user_input,
                "detected_intent": self._detect_user_intent(request.user_input),
                "extracted_keywords": self._extract_keywords(request.user_input),
                "suggested_scenario": request.research_scenario.value,
                "target_audience": request.audience
            }
            
            # 概念扩展
            semantic_context = await self.concept_expander.expand_user_input(
                request.user_input
            )
            
            processed_content = f"""
用户输入处理结果:
原始输入: {request.user_input}
检测意图: {parsed_input['detected_intent']}
关键词: {', '.join(parsed_input['extracted_keywords'])}
研究场景: {parsed_input['suggested_scenario']}
目标受众: {parsed_input['target_audience']}

语义分析:
领域: {semantic_context.domain}
内容类型: {semantic_context.content_type}
用户意图: {semantic_context.intent}
"""
            
            processing_time = (datetime.now() - step_start).total_seconds()
            
            return WorkflowStepResult(
                step=WorkflowStep.USER_INPUT,
                success=True,
                content=processed_content,
                metadata={
                    "parsed_input": parsed_input,
                    "semantic_context": semantic_context,
                    "processing_details": "用户输入解析和概念扩展完成"
                },
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            processing_time = (datetime.now() - step_start).total_seconds()
            return WorkflowStepResult(
                step=WorkflowStep.USER_INPUT,
                success=False,
                content=f"用户输入处理失败: {str(e)}",
                metadata={"error": str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )
    
    async def _enhance_and_rewrite_prompt(self, request: WorkflowRequest, 
                                        previous_content: str) -> WorkflowStepResult:
        """步骤2: 提示词增强重写"""
        step_start = datetime.now()
        
        try:
            # 使用搜索增强器进行提示词增强
            enhanced_prompt, search_data = await self.search_enhancer.enhance_prompt_with_search(
                request.user_input
            )
            
            # 创建研究请求
            research_request = ResearchRequest(
                scenario=request.research_scenario,
                target=request.user_input,
                audience=request.audience,
                output_format=request.output_format.value,
                additional_requirements=request.additional_requirements
            )
            
            # 生成专业研究提示词
            template_system = UniversalResearchPromptTemplate()
            professional_prompt = template_system.generate_research_prompt(research_request)
            
            enhanced_content = f"""
提示词增强重写结果:

1. 原始用户输入:
{request.user_input}

2. 搜索增强信息:
{enhanced_prompt[:500]}...

3. 生成的专业研究提示词:
{professional_prompt}

4. 搜索数据摘要:
找到相关信息: {len(search_data.get('search_results', []))}条
"""
            
            processing_time = (datetime.now() - step_start).total_seconds()
            
            return WorkflowStepResult(
                step=WorkflowStep.PROMPT_ENHANCEMENT,
                success=True,
                content=enhanced_content,
                metadata={
                    "enhanced_prompt": enhanced_prompt,
                    "professional_prompt": professional_prompt,
                    "search_data": search_data,
                    "research_request": asdict(research_request),
                    "prompt_length": len(professional_prompt)
                },
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            processing_time = (datetime.now() - step_start).total_seconds()
            return WorkflowStepResult(
                step=WorkflowStep.PROMPT_ENHANCEMENT,
                success=False,
                content=f"提示词增强失败: {str(e)}",
                metadata={"error": str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )
    
    async def _conduct_deep_research(self, request: WorkflowRequest,
                                   previous_content: str) -> WorkflowStepResult:
        """步骤3: 深度研究报告"""
        step_start = datetime.now()
        
        try:
            # 从前一步获取研究请求
            research_request = ResearchRequest(
                scenario=request.research_scenario,
                target=request.user_input,
                audience=request.audience,
                output_format=request.output_format.value,
                additional_requirements=request.additional_requirements
            )
            
            # 执行深度研究
            research_result = await self.research_agent.conduct_deep_research(research_request)
            
            research_content = f"""
深度研究报告:

研究目标: {research_result.target}
研究场景: {research_result.scenario.value}
质量评分: {research_result.quality_score:.2f}
处理时间: {research_result.processing_time:.2f}秒

研究内容:
{research_result.content}

数据来源: {len(research_result.sources)}个
参考资料: {', '.join(research_result.sources[:5])}...
"""
            
            processing_time = (datetime.now() - step_start).total_seconds()
            
            return WorkflowStepResult(
                step=WorkflowStep.DEEP_RESEARCH,
                success=True,
                content=research_content,
                metadata={
                    "research_result": research_result,
                    "quality_score": research_result.quality_score,
                    "sources_count": len(research_result.sources),
                    "content_length": len(research_result.content)
                },
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            processing_time = (datetime.now() - step_start).total_seconds()
            return WorkflowStepResult(
                step=WorkflowStep.DEEP_RESEARCH,
                success=False,
                content=f"深度研究失败: {str(e)}",
                metadata={"error": str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )
    
    async def _generate_script(self, request: WorkflowRequest,
                             previous_content: str) -> WorkflowStepResult:
        """步骤4: 脚本生成"""
        step_start = datetime.now()
        
        try:
            # 根据输出格式生成不同类型的脚本
            if request.output_format == OutputFormat.VIDEO_SCRIPT:
                script_content = await self._generate_video_script(previous_content, request)
            elif request.output_format == OutputFormat.PODCAST_SCRIPT:
                script_content = await self._generate_podcast_script(previous_content, request)
            else:
                # 默认生成视频脚本
                script_content = await self._generate_video_script(previous_content, request)
            
            script_result = f"""
脚本生成结果:

输出格式: {request.output_format.value}
目标受众: {request.audience}

生成的脚本:
{script_content}

脚本统计:
- 字数: {len(script_content)}字符
- 预估时长: {self._estimate_duration(script_content)}分钟
- 适合格式: {request.output_format.value}
"""
            
            processing_time = (datetime.now() - step_start).total_seconds()
            
            return WorkflowStepResult(
                step=WorkflowStep.SCRIPT_GENERATION,
                success=True,
                content=script_result,
                metadata={
                    "script_content": script_content,
                    "output_format": request.output_format.value,
                    "script_length": len(script_content),
                    "estimated_duration": self._estimate_duration(script_content)
                },
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            processing_time = (datetime.now() - step_start).total_seconds()
            return WorkflowStepResult(
                step=WorkflowStep.SCRIPT_GENERATION,
                success=False,
                content=f"脚本生成失败: {str(e)}",
                metadata={"error": str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )
    
    async def _produce_media(self, request: WorkflowRequest,
                           previous_content: str) -> WorkflowStepResult:
        """步骤5: 媒体制作"""
        step_start = datetime.now()
        
        try:
            media_files = []
            
            # 根据输出格式进行媒体制作
            if request.output_format == OutputFormat.FULL_VIDEO:
                # 生成完整视频
                video_files = await self._produce_full_video(previous_content, request)
                media_files.extend(video_files)
                
            elif request.output_format == OutputFormat.AUDIO_PODCAST:
                # 生成音频播客
                audio_files = await self._produce_audio_podcast(previous_content, request)
                media_files.extend(audio_files)
                
            else:
                # 只保存脚本文件
                script_file = await self._save_script_file(previous_content, request)
                media_files.append(script_file)
            
            media_result = f"""
媒体制作结果:

输出格式: {request.output_format.value}
生成文件数量: {len(media_files)}

生成的文件:
{chr(10).join(f'- {file}' for file in media_files)}

制作状态: 成功完成
"""
            
            processing_time = (datetime.now() - step_start).total_seconds()
            
            return WorkflowStepResult(
                step=WorkflowStep.MEDIA_PRODUCTION,
                success=True,
                content=media_result,
                metadata={
                    "media_files": media_files,
                    "output_format": request.output_format.value,
                    "files_count": len(media_files)
                },
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            processing_time = (datetime.now() - step_start).total_seconds()
            return WorkflowStepResult(
                step=WorkflowStep.MEDIA_PRODUCTION,
                success=False,
                content=f"媒体制作失败: {str(e)}",
                metadata={"error": str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )
    
    def _detect_user_intent(self, user_input: str) -> str:
        """检测用户意图"""
        # 简单的意图检测逻辑
        if any(keyword in user_input.lower() for keyword in ['分析', '研究', '深入']):
            return "深度分析"
        elif any(keyword in user_input.lower() for keyword in ['报告', '行业', '市场']):
            return "报告生成"
        elif any(keyword in user_input.lower() for keyword in ['视频', '脚本', '播客']):
            return "媒体制作"
        else:
            return "通用研究"
    
    def _extract_keywords(self, user_input: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        import re
        words = re.findall(r'\b\w+\b', user_input)
        return [word for word in words if len(word) > 2][:10]
    
    async def _generate_video_script(self, research_content: str, 
                                   request: WorkflowRequest) -> str:
        """生成视频脚本"""
        script_prompt = f"""
基于以下深度研究内容，生成一个吸引人的视频脚本：

研究内容：
{research_content[:2000]}...

要求：
1. 适合视频讲解的结构
2. 包含视觉提示和说明
3. 语言生动，适合{request.audience}受众
4. 时长控制在8-12分钟
5. 包含开场、主体内容和结尾

请生成完整的视频脚本。
"""
        
        return await self.script_generator.generate_from_prompt(script_prompt)
    
    async def _generate_podcast_script(self, research_content: str,
                                     request: WorkflowRequest) -> str:
        """生成播客脚本"""
        script_prompt = f"""
基于以下深度研究内容，生成一个播客脚本：

研究内容：
{research_content[:2000]}...

要求：
1. 对话式风格，适合音频收听
2. 包含背景音乐提示
3. 语言亲切，适合{request.audience}受众
4. 时长控制在15-20分钟
5. 包含开场音乐、主要内容和结尾

请生成完整的播客脚本。
"""
        
        return await self.script_generator.generate_from_prompt(script_prompt)
    
    def _estimate_duration(self, content: str) -> float:
        """估算内容时长（分钟）"""
        # 假设每分钟150-200字
        words_per_minute = 175
        total_chars = len(content)
        estimated_minutes = total_chars / words_per_minute
        return round(estimated_minutes, 1)
    
    async def _produce_full_video(self, script_content: str,
                                request: WorkflowRequest) -> List[str]:
        """制作完整视频"""
        video_files = []
        
        try:
            # 生成TTS音频
            audio_file = await self.tts_engine.generate_audio(
                text=script_content,
                voice_type="zh_female_roumeinvyou_emo_v2_mars_bigtts"
            )
            video_files.append(audio_file)
            
            # 生成背景图像
            image_file = await self.image_generator.generate_image(
                prompt=f"专业的{request.research_scenario.value}背景图",
                image_type="background"
            )
            video_files.append(image_file)
            
            # 组装视频
            video_config = request.video_config or VideoConfig()
            video_file = await self.video_assembler.assemble_video(
                audio_path=audio_file,
                background_images=[image_file],
                video_config=video_config
            )
            video_files.append(video_file)
            
        except Exception as e:
            self.logger.warning(f"视频制作部分失败: {str(e)}")
            # 至少保存脚本
            script_file = await self._save_script_file(script_content, request)
            video_files.append(script_file)
        
        return video_files
    
    async def _produce_audio_podcast(self, script_content: str,
                                   request: WorkflowRequest) -> List[str]:
        """制作音频播客"""
        audio_files = []
        
        try:
            # 生成播客音频
            audio_file = await self.tts_engine.generate_audio(
                text=script_content,
                voice_type="zh_male_beijingxiaoye_emo_v2_mars_bigtts"
            )
            audio_files.append(audio_file)
            
        except Exception as e:
            self.logger.warning(f"音频制作失败: {str(e)}")
            # 保存脚本作为备选
            script_file = await self._save_script_file(script_content, request)
            audio_files.append(script_file)
        
        return audio_files
    
    async def _save_script_file(self, script_content: str,
                              request: WorkflowRequest) -> str:
        """保存脚本文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"script_{request.output_format.value}_{timestamp}.txt"
        file_path = Path("output") / filename
        
        # 确保目录存在
        file_path.parent.mkdir(exist_ok=True)
        
        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return str(file_path)
    
    def _calculate_workflow_quality(self, steps: List[WorkflowStepResult]) -> float:
        """计算工作流质量"""
        if not steps:
            return 0.0
        
        success_rate = sum(1 for step in steps if step.success) / len(steps)
        
        # 基于研究质量调整
        research_step = next((step for step in steps 
                            if step.step == WorkflowStep.DEEP_RESEARCH), None)
        if research_step and research_step.success:
            research_quality = research_step.metadata.get("quality_score", 0.8)
            return (success_rate + research_quality) / 2
        
        return success_rate
    
    def _update_workflow_stats(self, result: WorkflowResult):
        """更新工作流统计"""
        self.workflow_stats["total_processed"] += 1
        
        if result.quality_score > 0.7:
            self.workflow_stats["success_count"] += 1
        
        # 更新平均处理时间
        total_time = (
            self.workflow_stats["average_processing_time"] * 
            (self.workflow_stats["total_processed"] - 1) +
            result.total_processing_time
        )
        self.workflow_stats["average_processing_time"] = (
            total_time / self.workflow_stats["total_processed"]
        )
        
        # 更新步骤性能统计
        for step_result in result.steps:
            step_name = step_result.step.value
            if step_name not in self.workflow_stats["step_performance"]:
                self.workflow_stats["step_performance"][step_name] = {
                    "count": 0,
                    "success_rate": 0.0,
                    "average_time": 0.0
                }
            
            step_stats = self.workflow_stats["step_performance"][step_name]
            step_stats["count"] += 1
            
            # 更新成功率
            if step_result.success:
                current_successes = step_stats["success_rate"] * (step_stats["count"] - 1)
                step_stats["success_rate"] = (current_successes + 1) / step_stats["count"]
            else:
                current_successes = step_stats["success_rate"] * (step_stats["count"] - 1)
                step_stats["success_rate"] = current_successes / step_stats["count"]
            
            # 更新平均时间
            total_step_time = (
                step_stats["average_time"] * (step_stats["count"] - 1) +
                step_result.processing_time
            )
            step_stats["average_time"] = total_step_time / step_stats["count"]
    
    def get_workflow_stats(self) -> Dict[str, Any]:
        """获取工作流统计"""
        return self.workflow_stats.copy()

# 使用示例
if __name__ == "__main__":
    async def test_enhanced_workflow():
        """测试增强工作流"""
        
        pipeline = EnhancedWorkflowPipeline()
        
        # 创建工作流请求
        request = WorkflowRequest(
            user_input="我想深入了解人工智能在医疗领域的应用和发展前景",
            research_scenario=ResearchScenario.RESEARCH_PROGRESS,
            output_format=OutputFormat.VIDEO_SCRIPT,
            audience="general",
            video_config=VideoConfig()
        )
        
        print("🚀 开始执行增强工作流...")
        print(f"用户输入: {request.user_input}")
        print(f"研究场景: {request.research_scenario.value}")
        print(f"输出格式: {request.output_format.value}")
        
        # 执行工作流
        result = await pipeline.execute_workflow(request)
        
        print(f"\n✅ 工作流完成!")
        print(f"质量评分: {result.quality_score:.2f}")
        print(f"总处理时间: {result.total_processing_time:.2f}秒")
        print(f"生成文件: {len(result.media_files)}个")
        
        # 显示各步骤结果
        print(f"\n📋 步骤执行情况:")
        for i, step in enumerate(result.steps, 1):
            status = "✅" if step.success else "❌"
            print(f"{i}. {step.step.value}: {status} ({step.processing_time:.2f}s)")
        
        # 显示最终输出预览
        print(f"\n📄 最终输出预览:")
        print(result.final_output[:500] + "...")
    
    # 运行测试
    asyncio.run(test_enhanced_workflow())