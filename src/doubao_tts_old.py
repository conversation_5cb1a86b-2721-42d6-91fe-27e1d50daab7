import asyncio
import websockets
import json
import gzip
import uuid
import copy
import os
from typing import Dict, Optional
from .config import Config

class DoubaoTTS:
    """Volcengine (ByteDance Doubao) TTS Service Implementation"""
    
    def __init__(self, app_id: str = None, access_token: str = None):
        # Updated configuration based on WebSocket demo
        self.app_id = app_id or Config.DOUBAO_APP_ID
        self.access_token = access_token or Config.DOUBAO_ACCESS_TOKEN
        self.host = "openspeech.bytedance.com"
        self.api_url = f"wss://{self.host}/api/v1/tts/ws_binary"
        self.cluster = "volcano_tts"
        
        # WebSocket protocol header (from demo)
        # version: b0001 (4 bits) + header size: b0001 (4 bits) + 
        # message type: b0001 (Full client request) (4bits) + message type specific flags: b0000 (none) (4bits) +
        # message serialization method: b0001 (JSON) (4 bits) + message compression: b0001 (gzip) (4bits) +
        # reserved data: 0x00 (1 byte)
        self.default_header = bytearray(b'\x11\x10\x11\x00')
        
        # Check if service is properly configured
        self.is_configured = bool(self.app_id and self.access_token)
        if not self.is_configured:
            print("⚠️ Doubao TTS not configured: Missing app_id or access_token")
        
        # Chinese voices available in Doubao TTS (based on official documentation)
        self.voices = {
            # Multi-emotion voices
            "BJ_xiaoyuan": {
                "name": "北京小爷",
                "description": "北京口音的男声，支持多种情感",
                "gender": "male",
                "accent": "北京",
                "emotions": ["angry", "surprise", "fear", "excited", "cold", "neutral"],
                "emotion_support": True
            },
            "gentle_girlfriend": {
                "name": "柔美女友", 
                "description": "温柔甜美的女声，情感丰富",
                "gender": "female",
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "surprised", "fear", "disgust", "excited", "cold", "neutral"],
                "emotion_support": True
            },
            "sunny_youth": {
                "name": "阳光青年",
                "description": "阳光活力的男声",
                "gender": "male", 
                "accent": "标准",
                "emotions": ["happy", "sad", "angry", "fear", "excited", "cold", "neutral"],
                "emotion_support": True
            },
            
            # Regional accent voices
            "BJ_kangye": {
                "name": "京腔侃爷",
                "description": "北京口音，支持中英文",
                "gender": "male",
                "accent": "北京",
                "languages": ["chinese", "english"],
                "emotion_support": False
            },
            "taiwan_xiaohe": {
                "name": "湾湾小何",
                "description": "台湾口音，亲切自然",
                "gender": "female",
                "accent": "台湾",
                "emotion_support": False
            },
            "sichuan_girl": {
                "name": "呆萌川妹",
                "description": "四川口音，可爱活泼",
                "gender": "female",
                "accent": "四川",
                "emotion_support": False
            },
            
            # Role-play voices
            "cool_lady": {
                "name": "高冷御姐",
                "description": "高冷优雅的女声",
                "gender": "female",
                "accent": "标准",
                "persona": "高冷御姐",
                "emotion_support": True
            },
            "proud_boss": {
                "name": "傲娇霸总",
                "description": "傲娇霸总的男声",
                "gender": "male",
                "accent": "标准", 
                "persona": "傲娇霸总",
                "emotion_support": True
            },
            
            # Specialized voices
            "teacher_tina": {
                "name": "Tina老师",
                "description": "教育专用声音，支持中英文",
                "gender": "female",
                "accent": "标准",
                "use_case": "教育",
                "languages": ["chinese", "english"],
                "emotion_support": False
            },
            "warm_female": {
                "name": "暖阳女声",
                "description": "温暖的客服女声",
                "gender": "female",
                "accent": "标准",
                "use_case": "客服",
                "emotion_support": False
            }
        }
    
    def _create_request_json(self, text: str, voice_id: str, emotion: str) -> dict:
        """Create request JSON following WebSocket demo format"""
        request_json = {
            "app": {
                "appid": self.app_id,
                "token": self.access_token,
                "cluster": self.cluster
            },
            "user": {
                "uid": "default_user"
            },
            "audio": {
                "voice_type": voice_id,
                "encoding": "mp3",
                "speed_ratio": 1.0,
                "volume_ratio": 1.0,
                "pitch_ratio": 1.0
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "submit"
            }
        }
        
        # Add emotion only if voice supports it and emotion is not neutral
        if emotion and emotion != "neutral" and voice_id in self.voices:
            voice_info = self.voices[voice_id]
            if voice_info.get("emotion_support") and emotion in voice_info.get("emotions", []):
                request_json["audio"]["emotion"] = emotion
        
        return request_json
    
    def _create_websocket_message(self, request_json: dict) -> bytearray:
        """Create WebSocket binary message from JSON request"""
        try:
            # Convert to JSON bytes
            json_str = json.dumps(request_json, ensure_ascii=False)
            payload_bytes = json_str.encode('utf-8')
            
            # Compress with gzip
            payload_bytes = gzip.compress(payload_bytes)
            
            # Create full message: header + payload size + payload
            full_message = bytearray(self.default_header)
            full_message.extend((len(payload_bytes)).to_bytes(4, 'big'))  # payload size (4 bytes)
            full_message.extend(payload_bytes)  # payload
            return full_message
            
        except Exception as e:
            print(f"❌ Error creating WebSocket message: {e}")
            raise
    
    async def _synthesize_speech_async(self, text: str, voice_id: str = "gentle_girlfriend", emotion: str = "neutral") -> Optional[bytes]:
        """Synthesize speech using WebSocket protocol"""
        if not self.is_configured:
            print("❌ Doubao TTS not configured, skipping synthesis")
            return None
            
        try:
            print(f"🔗 Doubao TTS WebSocket: {self.api_url}")
            print(f"📝 Voice: {voice_id}, Emotion: {emotion}")
            
            # Create request JSON
            request_json = self._create_request_json(text, voice_id, emotion)
            
            # Create WebSocket message
            message = self._create_websocket_message(request_json)
            
            # WebSocket headers
            headers = {"Authorization": f"Bearer; {self.access_token}"}
            
            # Connect and send request
            audio_data = bytearray()
            
            try:
                async with websockets.connect(self.api_url, additional_headers=headers, ping_interval=None) as ws:
                    await ws.send(message)
                    
                    while True:
                        response = await ws.recv()
                        
                        # Parse response using demo logic
                        done, chunk = self._parse_websocket_response(response)
                        
                        if chunk:
                            audio_data.extend(chunk)
                        
                        if done:
                            break
                            
            except websockets.exceptions.ConnectionClosedError as e:
                print(f"❌ WebSocket connection closed: {e}")
                return None
            except websockets.exceptions.InvalidStatusCode as e:
                print(f"❌ WebSocket invalid status: {e}")
                return None
            
            if len(audio_data) > 0:
                print(f"✅ Doubao TTS WebSocket success: {len(audio_data)} bytes")
                return bytes(audio_data)
            else:
                print("❌ No audio data received from WebSocket")
                return None
                
        except Exception as e:
            print(f"❌ Doubao TTS WebSocket exception: {e}")
            return None
    
    def _parse_websocket_response(self, response: bytes) -> tuple[bool, Optional[bytes]]:
        """Parse WebSocket response following demo format"""
        try:
            # Parse header
            message_type = response[1] >> 4
            message_type_specific_flags = response[1] & 0x0f
            header_size = response[0] & 0x0f
            
            # Extract payload
            payload = response[header_size * 4:]
            
            if message_type == 0xb:  # audio-only server response
                if message_type_specific_flags == 0:  # no sequence number as ACK
                    return False, None
                else:
                    sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                    payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                    audio_chunk = payload[8:]
                    
                    # Check if this is the last chunk
                    done = sequence_number < 0
                    return done, audio_chunk
                    
            elif message_type == 0xf:  # error message
                code = int.from_bytes(payload[:4], "big", signed=False)
                msg_size = int.from_bytes(payload[4:8], "big", signed=False)
                error_msg = payload[8:]
                
                # Decompress if needed
                message_compression = response[2] & 0x0f
                if message_compression == 1:
                    error_msg = gzip.decompress(error_msg)
                
                error_text = error_msg.decode('utf-8')
                if "resource not granted" in error_text:
                    print(f"❌ Doubao TTS: Resource permissions not granted for app_id {self.app_id}")
                else:
                    print(f"❌ Doubao TTS WebSocket error {code}: {error_text}")
                return True, None
                
            else:
                print(f"⚠️ Unknown WebSocket message type: {message_type}")
                return False, None
                
        except Exception as e:
            print(f"❌ Error parsing WebSocket response: {e}")
            return True, None
    
    def synthesize_speech(self, text: str, voice_id: str = "gentle_girlfriend", emotion: str = "neutral") -> Optional[bytes]:
        """Synthesize speech using Doubao TTS WebSocket API"""
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self._synthesize_speech_async(text, voice_id, emotion))
        finally:
            loop.close()
    
    def synthesize_emotion_marked_text(self, marked_text: str, output_path: str, voice_id: str = "zh-CN-Female-1") -> bool:
        """Synthesize text with emotion markers using SSML format"""
        # Check if service is configured
        if not self.is_configured:
            print("❌ Doubao TTS not configured, cannot synthesize emotion-marked text")
            return False
            
        try:
            import re
            from pydub import AudioSegment
            
            print(f"🎭 Processing emotion-marked text with voice {voice_id}")
            
            # Parse emotion markers from text
            emotion_segments = self._parse_emotion_markers(marked_text)
            
            if not emotion_segments:
                # No emotion markers found, use regular synthesis
                return self.synthesize_to_file(marked_text, output_path, voice_id, "neutral")
            
            # Generate audio for each emotion segment
            temp_files = []
            combined_audio = AudioSegment.empty()
            
            for i, (text_content, emotion) in enumerate(emotion_segments):
                if not text_content.strip():
                    continue
                    
                # Create temporary file for this segment
                temp_path = output_path.replace('.mp3', f'_segment_{i}.mp3')
                
                # Synthesize with specific emotion
                success = self.synthesize_to_file(text_content, temp_path, voice_id, emotion)
                
                if success and os.path.exists(temp_path):
                    # Load and combine audio
                    segment_audio = AudioSegment.from_file(temp_path)
                    combined_audio += segment_audio
                    temp_files.append(temp_path)
                    print(f"  ✅ Segment {i+1}: '{text_content[:30]}...' with {emotion}")
                else:
                    print(f"  ❌ Failed segment {i+1}: '{text_content[:30]}...'")
            
            # Export combined audio
            if len(combined_audio) > 0:
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                combined_audio.export(output_path, format="mp3")
                
                # Clean up temporary files
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                
                print(f"🎵 Emotion synthesis complete: {len(temp_files)} segments -> {output_path}")
                return True
            else:
                print("❌ No audio segments generated")
                return False
                
        except Exception as e:
            print(f"❌ Emotion synthesis error: {e}")
            # Fallback to regular synthesis without emotions
            clean_text = self._strip_emotion_markers(marked_text)
            return self.synthesize_to_file(clean_text, output_path, voice_id, "neutral")
    
    def _parse_emotion_markers(self, marked_text: str) -> list:
        """Parse emotion markers from text and return segments"""
        import re
        
        segments = []
        
        # Pattern to match <speak emotion="emotion">text</speak>
        emotion_pattern = r'<speak emotion="([^"]+)">([^<]+)</speak>'
        
        # Find all emotion-marked segments
        matches = re.finditer(emotion_pattern, marked_text)
        last_end = 0
        
        for match in matches:
            # Add any unmarked text before this match
            if match.start() > last_end:
                unmarked_text = marked_text[last_end:match.start()].strip()
                if unmarked_text:
                    segments.append((unmarked_text, "neutral"))
            
            # Add the emotion-marked segment
            emotion = match.group(1)
            text_content = match.group(2).strip()
            if text_content:
                segments.append((text_content, emotion))
            
            last_end = match.end()
        
        # Add any remaining unmarked text
        if last_end < len(marked_text):
            remaining_text = marked_text[last_end:].strip()
            if remaining_text:
                segments.append((remaining_text, "neutral"))
        
        # If no emotion markers found, return the whole text as neutral
        if not segments:
            clean_text = self._strip_emotion_markers(marked_text)
            if clean_text.strip():
                segments.append((clean_text, "neutral"))
        
        return segments
    
    def _strip_emotion_markers(self, marked_text: str) -> str:
        """Remove emotion markers from text"""
        import re
        # Remove SSML emotion tags
        clean_text = re.sub(r'<speak emotion="[^"]+">([^<]+)</speak>', r'\1', marked_text)
        return clean_text.strip()

    def synthesize_to_file(self, text: str, output_path: str, voice_id: str = "zh-CN-Female-1", emotion: str = "neutral") -> bool:
        """Synthesize speech and save to file"""
        # Check if service is configured
        if not self.is_configured:
            print("❌ Doubao TTS not configured, cannot synthesize to file")
            return False
            
        try:
            print(f"🔊 Doubao TTS: Generating '{text[:50]}...' with voice {voice_id}")
            
            audio_data = self.synthesize_speech(text, voice_id, emotion)
            
            if audio_data and len(audio_data) > 0:
                # Ensure output directory exists
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # Write audio data to file
                with open(output_path, 'wb') as f:
                    f.write(audio_data)
                
                print(f"✅ Doubao TTS: Generated {len(audio_data)} bytes -> {output_path}")
                return True
            else:
                print(f"❌ Doubao TTS: No audio data received")
                return False
                
        except Exception as e:
            print(f"❌ Doubao TTS file save error: {e}")
            return False
    
    def get_available_voices(self) -> Dict[str, Dict]:
        """Get available voice configurations"""
        return self.voices.copy()
    
    def validate_voice(self, voice_id: str) -> bool:
        """Check if voice ID is valid"""
        return voice_id in self.voices
    
    def get_voice_info(self, voice_id: str) -> Optional[Dict]:
        """Get voice information"""
        return self.voices.get(voice_id)
    
    def test_connection(self) -> bool:
        """Test TTS service connectivity"""
        # Check if service is configured first
        if not self.is_configured:
            print("❌ Doubao TTS credentials not configured (app_id/access_token)")
            return False
            
        try:
            print("🔍 Testing Doubao TTS connectivity...")
            print(f"🔑 App ID: {self.app_id[:8]}...")
            print(f"🎫 Token: {self.access_token[:8]}...")
            
            # Test with simple text
            test_text = "你好"
            audio_data = self.synthesize_speech(test_text, "gentle_girlfriend", "neutral")
            
            if audio_data is not None and len(audio_data) > 0:
                print("✅ Doubao TTS connectivity test passed")
                return True
            else:
                print("❌ Doubao TTS connectivity test failed: No audio data")
                return False
                
        except Exception as e:
            print(f"❌ Doubao TTS connection test failed: {e}")
            return False