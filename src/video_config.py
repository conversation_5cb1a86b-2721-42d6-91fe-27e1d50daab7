from enum import Enum
from typing import Dict, Tuple
from dataclasses import dataclass

class AspectRatio(Enum):
    """Supported aspect ratios for video generation"""
    VERTICAL_9_16 = "9:16"  # Mobile/Portrait (1080x1920)
    HORIZONTAL_16_9 = "16:9"  # Desktop/Landscape (1920x1080)
    SQUARE_1_1 = "1:1"  # Square (1080x1080)

class VideoQuality(Enum):
    """Video quality presets"""
    HD = "HD"  # 720p
    FULL_HD = "Full HD"  # 1080p
    QHD = "QHD"  # 1440p

class SubtitleStyle(Enum):
    """Subtitle styling options"""
    BOTTOM_CENTER = "bottom_center"
    BOTTOM_LEFT = "bottom_left"
    MIDDLE_CENTER = "middle_center"

@dataclass
class VideoConfig:
    """Video configuration settings"""
    aspect_ratio: AspectRatio = AspectRatio.VERTICAL_9_16
    quality: VideoQuality = VideoQuality.FULL_HD
    fps: int = 30
    subtitle_style: SubtitleStyle = SubtitleStyle.BOTTOM_CENTER
    subtitle_font_size: int = 42
    bgm_volume: float = 0.2
    opening_duration: float = 2.0
    enable_subtitles: bool = True
    enable_fade_effects: bool = True
    enable_dynamic_backgrounds: bool = True
    background_transition_interval: Tuple[int, int] = (10, 20)  # seconds

class VideoConfigManager:
    """Manages video configuration presets and calculations"""
    
    # Resolution mappings for different aspect ratios and qualities
    RESOLUTION_MAP = {
        AspectRatio.VERTICAL_9_16: {
            VideoQuality.HD: (720, 1280),
            VideoQuality.FULL_HD: (1080, 1920),
            VideoQuality.QHD: (1440, 2560)
        },
        AspectRatio.HORIZONTAL_16_9: {
            VideoQuality.HD: (1280, 720),
            VideoQuality.FULL_HD: (1920, 1080),
            VideoQuality.QHD: (2560, 1440)
        },
        AspectRatio.SQUARE_1_1: {
            VideoQuality.HD: (720, 720),
            VideoQuality.FULL_HD: (1080, 1080),
            VideoQuality.QHD: (1440, 1440)
        }
    }
    
    # Subtitle position mappings
    SUBTITLE_POSITIONS = {
        SubtitleStyle.BOTTOM_CENTER: lambda w, h: ('center', h - 180),
        SubtitleStyle.BOTTOM_LEFT: lambda w, h: (60, h - 180),
        SubtitleStyle.MIDDLE_CENTER: lambda w, h: ('center', 'center')
    }
    
    @classmethod
    def get_dimensions(cls, config: VideoConfig) -> Tuple[int, int]:
        """Get video dimensions based on configuration"""
        return cls.RESOLUTION_MAP[config.aspect_ratio][config.quality]
    
    @classmethod
    def get_subtitle_position(cls, config: VideoConfig, width: int, height: int) -> Tuple:
        """Get subtitle position based on configuration"""
        position_func = cls.SUBTITLE_POSITIONS[config.subtitle_style]
        return position_func(width, height)
    
    @classmethod
    def create_preset(cls, preset_name: str) -> VideoConfig:
        """Create predefined video configuration presets"""
        presets = {
            "mobile_portrait": VideoConfig(
                aspect_ratio=AspectRatio.VERTICAL_9_16,
                quality=VideoQuality.FULL_HD,
                subtitle_style=SubtitleStyle.BOTTOM_CENTER
            ),
            "desktop_landscape": VideoConfig(
                aspect_ratio=AspectRatio.HORIZONTAL_16_9,
                quality=VideoQuality.FULL_HD,
                subtitle_style=SubtitleStyle.BOTTOM_CENTER
            ),
            "social_square": VideoConfig(
                aspect_ratio=AspectRatio.SQUARE_1_1,
                quality=VideoQuality.FULL_HD,
                subtitle_style=SubtitleStyle.MIDDLE_CENTER
            ),
            "high_quality_mobile": VideoConfig(
                aspect_ratio=AspectRatio.VERTICAL_9_16,
                quality=VideoQuality.QHD,
                fps=60,
                subtitle_font_size=48
            ),
            "minimal_desktop": VideoConfig(
                aspect_ratio=AspectRatio.HORIZONTAL_16_9,
                quality=VideoQuality.HD,
                enable_fade_effects=False,
                bgm_volume=0.1
            )
        }
        
        return presets.get(preset_name, VideoConfig())
    
    @classmethod
    def get_available_presets(cls) -> Dict[str, str]:
        """Get list of available presets with descriptions"""
        return {
            "mobile_portrait": "移动端竖屏 - 9:16 Full HD，适合手机观看",
            "desktop_landscape": "桌面端横屏 - 16:9 Full HD，适合电脑观看",
            "social_square": "社交媒体方形 - 1:1 Full HD，适合Instagram等平台",
            "high_quality_mobile": "高画质移动端 - 9:16 QHD 60fps，最佳质量",
            "minimal_desktop": "简约桌面端 - 16:9 HD，文件更小"
        }
    
    @classmethod
    def validate_config(cls, config: VideoConfig) -> bool:
        """Validate video configuration"""
        try:
            # Check if resolution mapping exists
            cls.get_dimensions(config)
            
            # Validate ranges
            if not (1 <= config.fps <= 120):
                return False
            if not (0.0 <= config.bgm_volume <= 1.0):
                return False
            if not (0.5 <= config.opening_duration <= 10.0):
                return False
            if not (20 <= config.subtitle_font_size <= 80):
                return False
                
            return True
        except:
            return False