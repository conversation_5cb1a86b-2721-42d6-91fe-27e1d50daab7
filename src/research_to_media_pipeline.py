"""
Research to Media Pipeline
研究到媒体管道

将深度研究结果转换为各种媒体格式的完整流程
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import logging
from pathlib import Path

# 导入研究组件
try:
    from deep_research_agent import DeepResearchAgent, ResearchResult
    from universal_research_template import ResearchRequest, ResearchScenario
    # 导入现有媒体生成组件
    from enhanced_script_generator import EnhancedScriptGenerator
    from concept_expander import ConceptExpander
    from doubao_tts import DoubaoTTS
    from video_assembler import VideoAssembler
    from image_generator import ImageGenerator
    from background_generator import BackgroundGenerator
    from config import VideoConfig, Config
except ImportError:
    print("⚠️  部分媒体组件未找到，使用模拟组件")
    
    # 模拟组件
    class VideoConfig:
        pass
    
    class Config:
        pass
    
    class EnhancedScriptGenerator:
        pass
    
    class ConceptExpander:
        pass
    
    class DoubaoTTS:
        async def generate_audio(self, **kwargs):
            return "temp/mock_audio.wav"
    
    class VideoAssembler:
        async def assemble_video(self, **kwargs):
            return "temp/mock_video.mp4"
    
    class ImageGenerator:
        async def generate_image(self, **kwargs):
            return "temp/mock_image.jpg"
    
    class BackgroundGenerator:
        pass

class MediaFormat(Enum):
    """媒体格式枚举"""
    REPORT = "report"
    VIDEO_SCRIPT = "video_script"
    PODCAST_SCRIPT = "podcast_script"
    VIDEO = "video"
    AUDIO = "audio"
    INTERACTIVE_REPORT = "interactive_report"

@dataclass
class MediaGenerationRequest:
    """媒体生成请求"""
    research_request: ResearchRequest
    target_format: MediaFormat
    style_preferences: Optional[Dict[str, Any]] = None
    video_config: Optional[VideoConfig] = None
    audio_config: Optional[Dict[str, Any]] = None
    
@dataclass
class MediaOutput:
    """媒体输出结果"""
    format: MediaFormat
    primary_content: str
    media_files: List[str]
    metadata: Dict[str, Any]
    quality_score: float
    processing_time: float
    timestamp: datetime

class ContentAdaptationEngine:
    """内容适配引擎"""
    
    def __init__(self):
        self.adaptation_strategies = {
            MediaFormat.VIDEO_SCRIPT: self._adapt_for_video_script,
            MediaFormat.PODCAST_SCRIPT: self._adapt_for_podcast_script,
            MediaFormat.VIDEO: self._adapt_for_video,
            MediaFormat.AUDIO: self._adapt_for_audio,
            MediaFormat.INTERACTIVE_REPORT: self._adapt_for_interactive_report
        }
    
    def adapt_research_content(self, research_result: ResearchResult, 
                             target_format: MediaFormat,
                             style_preferences: Dict[str, Any] = None) -> str:
        """将研究内容适配为指定格式"""
        
        adapter = self.adaptation_strategies.get(target_format)
        if not adapter:
            raise ValueError(f"Unsupported media format: {target_format}")
        
        return adapter(research_result, style_preferences or {})
    
    def _adapt_for_video_script(self, research_result: ResearchResult, 
                               style_preferences: Dict[str, Any]) -> str:
        """适配为视频脚本"""
        
        # 提取关键信息
        title = self._extract_title(research_result)
        key_points = self._extract_key_points(research_result)
        
        # 生成视频脚本结构
        video_script = f"""# {title}

## 开场白
欢迎来到深度研究系列。今天我们将深入探讨{research_result.target}，通过系统性的分析为您呈现全面的洞察。

## 核心内容

### 背景介绍
{self._extract_background(research_result)}

### 主要发现
{self._format_key_findings_for_video(key_points)}

### 深度分析
{self._extract_analysis_for_video(research_result)}

### 实际意义
{self._extract_implications_for_video(research_result)}

## 结语
通过今天的深度研究，我们全面了解了{research_result.target}的各个方面。希望这些分析能为您提供有价值的见解。

## 视觉提示
{self._generate_visual_cues(research_result)}

## 配音说明
- 语调：{style_preferences.get('tone', '专业而亲切')}
- 语速：{style_preferences.get('speed', '中等')}
- 重点强调：{style_preferences.get('emphasis', '关键数据和结论')}
"""
        
        return video_script
    
    def _adapt_for_podcast_script(self, research_result: ResearchResult,
                                 style_preferences: Dict[str, Any]) -> str:
        """适配为播客脚本"""
        
        # 播客更注重对话感和听觉体验
        podcast_script = f"""# 播客脚本：{research_result.target}

## 开场音乐 (5秒)

## 主持人开场
大家好，欢迎收听「深度研究」播客。我是你们的主持人。今天我们要聊的是{research_result.target}，这是一个非常有趣的话题。

## 话题引入
你知道吗？{self._create_engaging_hook(research_result)}

## 主要内容讨论

### 第一部分：基础了解
{self._format_for_audio_discussion(research_result, "background")}

### 第二部分：深度分析
{self._format_for_audio_discussion(research_result, "analysis")}

### 第三部分：实际应用
{self._format_for_audio_discussion(research_result, "application")}

### 第四部分：未来展望
{self._format_for_audio_discussion(research_result, "future")}

## 听众互动环节
如果你对{research_result.target}有自己的看法，欢迎在评论区分享。

## 结束语
今天的节目就到这里，感谢大家的收听。我们下期再见！

## 音频提示
- 背景音乐：{style_preferences.get('background_music', '轻松的器乐')}
- 音效：{style_preferences.get('sound_effects', '适度的转场音效')}
- 停顿：{style_preferences.get('pauses', '自然的对话停顿')}
"""
        
        return podcast_script
    
    def _adapt_for_video(self, research_result: ResearchResult,
                        style_preferences: Dict[str, Any]) -> str:
        """适配为完整视频内容"""
        
        # 生成视频脚本
        video_script = self._adapt_for_video_script(research_result, style_preferences)
        
        # 添加详细的视频制作指导
        video_content = f"""{video_script}

## 视频制作指导

### 视觉元素
- 主题色彩：{style_preferences.get('color_theme', '专业蓝色调')}
- 字体风格：{style_preferences.get('font_style', '现代简洁')}
- 动画效果：{style_preferences.get('animation', '平滑过渡')}

### 镜头建议
{self._generate_camera_directions(research_result)}

### 素材需求
{self._generate_material_requirements(research_result)}

### 时长控制
- 总时长：{style_preferences.get('duration', '8-12分钟')}
- 开场：30秒
- 主体内容：6-10分钟
- 结尾：1-2分钟
"""
        
        return video_content
    
    def _adapt_for_audio(self, research_result: ResearchResult,
                        style_preferences: Dict[str, Any]) -> str:
        """适配为音频内容"""
        
        # 生成播客脚本
        podcast_script = self._adapt_for_podcast_script(research_result, style_preferences)
        
        # 添加音频制作指导
        audio_content = f"""{podcast_script}

## 音频制作指导

### 声音设计
- 主讲声音：{style_preferences.get('narrator_voice', '专业男声')}
- 语调变化：{style_preferences.get('tone_variation', '根据内容调整')}
- 语速控制：{style_preferences.get('speed_control', '150-180字/分钟')}

### 音效设计
{self._generate_audio_effects(research_result)}

### 质量标准
- 采样率：44.1kHz
- 比特率：320kbps
- 动态范围：符合播客标准
"""
        
        return audio_content
    
    def _adapt_for_interactive_report(self, research_result: ResearchResult,
                                    style_preferences: Dict[str, Any]) -> str:
        """适配为交互式报告"""
        
        interactive_content = f"""# 交互式研究报告：{research_result.target}

## 报告结构

### 导航菜单
{self._generate_navigation_menu(research_result)}

### 交互式章节
{self._generate_interactive_sections(research_result)}

### 数据可视化
{self._generate_data_visualizations(research_result)}

### 用户参与元素
{self._generate_user_engagement_elements(research_result)}

## 技术实现
- 平台：{style_preferences.get('platform', 'Web应用')}
- 交互形式：{style_preferences.get('interaction_type', '点击、滚动、悬停')}
- 响应式设计：{style_preferences.get('responsive', '支持多设备')}
"""
        
        return interactive_content
    
    def _extract_title(self, research_result: ResearchResult) -> str:
        """提取标题"""
        content_lines = research_result.content.split('\n')
        for line in content_lines:
            if line.strip().startswith('#'):
                return line.strip().replace('#', '').strip()
        return f"深度研究：{research_result.target}"
    
    def _extract_key_points(self, research_result: ResearchResult) -> List[str]:
        """提取关键点"""
        content = research_result.content
        key_points = []
        
        # 查找项目符号列表
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('- ') or line.strip().startswith('* '):
                key_points.append(line.strip()[2:])
        
        return key_points[:8]  # 限制数量
    
    def _extract_background(self, research_result: ResearchResult) -> str:
        """提取背景信息"""
        content = research_result.content
        
        # 查找背景相关章节
        sections = content.split('##')
        for section in sections:
            if any(keyword in section.lower() for keyword in ['背景', '概况', '介绍', '现状']):
                return section.strip()[:300] + "..."
        
        return content[:300] + "..."
    
    def _format_key_findings_for_video(self, key_points: List[str]) -> str:
        """为视频格式化关键发现"""
        formatted = ""
        for i, point in enumerate(key_points[:5], 1):
            formatted += f"{i}. {point}\n"
        return formatted
    
    def _extract_analysis_for_video(self, research_result: ResearchResult) -> str:
        """为视频提取分析内容"""
        content = research_result.content
        
        # 查找分析相关章节
        sections = content.split('##')
        for section in sections:
            if any(keyword in section.lower() for keyword in ['分析', '解析', '研究', '评估']):
                return section.strip()[:500] + "..."
        
        return "详细分析请参阅完整报告..."
    
    def _extract_implications_for_video(self, research_result: ResearchResult) -> str:
        """为视频提取实际意义"""
        content = research_result.content
        
        # 查找意义相关章节
        sections = content.split('##')
        for section in sections:
            if any(keyword in section.lower() for keyword in ['意义', '价值', '影响', '应用']):
                return section.strip()[:400] + "..."
        
        return "具有重要的理论和实践意义..."
    
    def _generate_visual_cues(self, research_result: ResearchResult) -> str:
        """生成视觉提示"""
        return f"""- 标题卡片：{research_result.target}
- 关键数据图表：展示核心数据
- 概念示意图：illustrate主要概念
- 时间线：显示发展历程
- 对比图：展示不同观点
- 总结图：概括主要结论"""
    
    def _create_engaging_hook(self, research_result: ResearchResult) -> str:
        """创建吸引人的开场"""
        return f"关于{research_result.target}，有一个非常有趣的发现..."
    
    def _format_for_audio_discussion(self, research_result: ResearchResult, 
                                   section_type: str) -> str:
        """为音频讨论格式化内容"""
        content = research_result.content
        
        # 根据类型提取相关内容
        if section_type == "background":
            return "首先，让我们了解一下基本背景..."
        elif section_type == "analysis":
            return "接下来，我们深入分析一下..."
        elif section_type == "application":
            return "那么，这在实际中意味着什么呢？"
        elif section_type == "future":
            return "最后，让我们展望一下未来..."
        
        return "让我们继续讨论..."
    
    def _generate_camera_directions(self, research_result: ResearchResult) -> str:
        """生成镜头指导"""
        return """- 开场：广角镜头，展示整体概念
- 分析部分：特写镜头，突出关键信息
- 数据展示：图表特写，配合动画
- 结论部分：中景镜头，平衡视觉效果"""
    
    def _generate_material_requirements(self, research_result: ResearchResult) -> str:
        """生成素材需求"""
        return """- 背景图像：相关主题的高质量图片
- 图表素材：数据可视化图表
- 动画元素：概念解释动画
- 音效素材：转场和强调音效"""
    
    def _generate_audio_effects(self, research_result: ResearchResult) -> str:
        """生成音频效果"""
        return """- 开场音乐：专业且吸引人
- 转场音效：平滑的章节过渡
- 强调音效：突出重点信息
- 背景音乐：轻柔的器乐伴奏"""
    
    def _generate_navigation_menu(self, research_result: ResearchResult) -> str:
        """生成导航菜单"""
        return """- 概述
- 详细分析
- 关键发现
- 实际应用
- 未来展望
- 参考资料"""
    
    def _generate_interactive_sections(self, research_result: ResearchResult) -> str:
        """生成交互式章节"""
        return """- 可展开的详细内容
- 点击查看更多信息
- 相关链接和引用
- 用户评论和反馈"""
    
    def _generate_data_visualizations(self, research_result: ResearchResult) -> str:
        """生成数据可视化"""
        return """- 交互式图表
- 动态数据展示
- 对比分析工具
- 趋势预测图"""
    
    def _generate_user_engagement_elements(self, research_result: ResearchResult) -> str:
        """生成用户参与元素"""
        return """- 问卷调查
- 评论系统
- 分享功能
- 相关推荐"""

class ResearchToMediaPipeline:
    """研究到媒体管道"""
    
    def __init__(self):
        # 核心组件
        self.research_agent = DeepResearchAgent()
        self.content_adapter = ContentAdaptationEngine()
        self.script_generator = EnhancedScriptGenerator()
        self.tts_engine = DoubaoTTS()
        self.video_assembler = VideoAssembler()
        self.image_generator = ImageGenerator()
        self.background_generator = BackgroundGenerator()
        
        # 配置和日志
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        
        # 性能指标
        self.pipeline_metrics = {
            "total_processed": 0,
            "success_rate": 0.0,
            "average_processing_time": 0.0,
            "format_distribution": {}
        }
    
    async def process_research_to_media(self, request: MediaGenerationRequest) -> MediaOutput:
        """处理研究到媒体的完整流程"""
        start_time = datetime.now()
        
        try:
            # 第1步：执行深度研究
            self.logger.info("开始深度研究...")
            research_result = await self.research_agent.conduct_deep_research(
                request.research_request
            )
            
            # 第2步：内容适配
            self.logger.info(f"适配内容为{request.target_format.value}格式...")
            adapted_content = self.content_adapter.adapt_research_content(
                research_result, 
                request.target_format,
                request.style_preferences
            )
            
            # 第3步：媒体生成
            self.logger.info("生成媒体内容...")
            media_files = await self._generate_media_files(
                adapted_content, 
                request.target_format,
                request.video_config,
                request.audio_config
            )
            
            # 第4步：质量评估
            quality_score = self._assess_media_quality(adapted_content, media_files)
            
            # 第5步：构建输出
            processing_time = (datetime.now() - start_time).total_seconds()
            
            output = MediaOutput(
                format=request.target_format,
                primary_content=adapted_content,
                media_files=media_files,
                metadata={
                    "research_quality": research_result.quality_score,
                    "research_processing_time": research_result.processing_time,
                    "adaptation_quality": quality_score,
                    "total_processing_time": processing_time
                },
                quality_score=quality_score,
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
            # 第6步：更新指标
            self._update_pipeline_metrics(output)
            
            return output
            
        except Exception as e:
            self.logger.error(f"Pipeline processing failed: {str(e)}")
            raise
    
    async def _generate_media_files(self, content: str, format: MediaFormat,
                                  video_config: VideoConfig = None,
                                  audio_config: Dict[str, Any] = None) -> List[str]:
        """生成媒体文件"""
        
        media_files = []
        
        if format == MediaFormat.REPORT:
            # 生成报告文件
            report_path = await self._generate_report_file(content)
            media_files.append(report_path)
            
        elif format == MediaFormat.VIDEO_SCRIPT:
            # 生成脚本文件
            script_path = await self._generate_script_file(content)
            media_files.append(script_path)
            
        elif format == MediaFormat.PODCAST_SCRIPT:
            # 生成播客脚本文件
            podcast_path = await self._generate_podcast_script_file(content)
            media_files.append(podcast_path)
            
        elif format == MediaFormat.VIDEO:
            # 生成完整视频
            video_files = await self._generate_video(content, video_config)
            media_files.extend(video_files)
            
        elif format == MediaFormat.AUDIO:
            # 生成音频文件
            audio_files = await self._generate_audio(content, audio_config)
            media_files.extend(audio_files)
            
        elif format == MediaFormat.INTERACTIVE_REPORT:
            # 生成交互式报告
            interactive_files = await self._generate_interactive_report(content)
            media_files.extend(interactive_files)
        
        return media_files
    
    async def _generate_report_file(self, content: str) -> str:
        """生成报告文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"output/research_report_{timestamp}.md"
        
        Path(report_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return report_path
    
    async def _generate_script_file(self, content: str) -> str:
        """生成脚本文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        script_path = f"output/video_script_{timestamp}.md"
        
        Path(script_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return script_path
    
    async def _generate_podcast_script_file(self, content: str) -> str:
        """生成播客脚本文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        podcast_path = f"output/podcast_script_{timestamp}.md"
        
        Path(podcast_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(podcast_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return podcast_path
    
    async def _generate_video(self, content: str, video_config: VideoConfig = None) -> List[str]:
        """生成视频"""
        
        if not video_config:
            video_config = VideoConfig()
        
        # 从脚本内容提取音频部分
        audio_content = self._extract_audio_content(content)
        
        # 生成TTS音频
        audio_path = await self._generate_tts_audio(audio_content)
        
        # 生成背景图像
        background_images = await self._generate_background_images(content)
        
        # 组装视频
        video_path = await self.video_assembler.assemble_video(
            audio_path=audio_path,
            background_images=background_images,
            video_config=video_config
        )
        
        return [video_path, audio_path] + background_images
    
    async def _generate_audio(self, content: str, audio_config: Dict[str, Any] = None) -> List[str]:
        """生成音频"""
        
        # 从脚本内容提取音频部分
        audio_content = self._extract_audio_content(content)
        
        # 生成TTS音频
        audio_path = await self._generate_tts_audio(audio_content, audio_config)
        
        return [audio_path]
    
    async def _generate_interactive_report(self, content: str) -> List[str]:
        """生成交互式报告"""
        
        # 生成HTML文件
        html_path = await self._generate_html_report(content)
        
        # 生成CSS样式文件
        css_path = await self._generate_css_file()
        
        # 生成JavaScript交互文件
        js_path = await self._generate_js_file()
        
        return [html_path, css_path, js_path]
    
    def _extract_audio_content(self, content: str) -> str:
        """从内容中提取音频部分"""
        # 移除视觉提示和制作指导
        audio_content = content
        
        # 移除视觉相关的部分
        lines = audio_content.split('\n')
        filtered_lines = []
        
        skip_section = False
        for line in lines:
            if any(keyword in line.lower() for keyword in ['视觉', '镜头', '素材', '制作指导']):
                skip_section = True
            elif line.startswith('##') and not any(keyword in line.lower() for keyword in ['视觉', '镜头', '素材', '制作指导']):
                skip_section = False
            
            if not skip_section:
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    async def _generate_tts_audio(self, content: str, audio_config: Dict[str, Any] = None) -> str:
        """生成TTS音频"""
        
        if not audio_config:
            audio_config = {
                "voice_type": "zh_female_roumeinvyou_emo_v2_mars_bigtts",
                "speed": 1.0,
                "emotion": "neutral"
            }
        
        # 使用TTS引擎生成音频
        audio_path = await self.tts_engine.generate_audio(
            text=content,
            voice_type=audio_config.get("voice_type"),
            speed=audio_config.get("speed", 1.0),
            emotion=audio_config.get("emotion", "neutral")
        )
        
        return audio_path
    
    async def _generate_background_images(self, content: str) -> List[str]:
        """生成背景图像"""
        
        # 从内容中提取视觉提示
        visual_cues = self._extract_visual_cues(content)
        
        # 生成背景图像
        background_images = []
        for i, cue in enumerate(visual_cues[:5]):  # 限制数量
            image_path = await self.image_generator.generate_image(
                prompt=cue,
                image_type="background",
                style="professional"
            )
            background_images.append(image_path)
        
        return background_images
    
    def _extract_visual_cues(self, content: str) -> List[str]:
        """提取视觉提示"""
        visual_cues = []
        
        # 查找视觉提示部分
        if "视觉提示" in content:
            visual_section = content.split("视觉提示")[1].split("##")[0]
            lines = visual_section.split('\n')
            for line in lines:
                if line.strip().startswith('- '):
                    visual_cues.append(line.strip()[2:])
        
        # 如果没有找到视觉提示，生成默认的
        if not visual_cues:
            visual_cues = [
                "专业的研究报告背景",
                "数据分析图表",
                "概念示意图",
                "总结图表",
                "专业结论背景"
            ]
        
        return visual_cues
    
    async def _generate_html_report(self, content: str) -> str:
        """生成HTML报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_path = f"output/interactive_report_{timestamp}.html"
        
        # 生成HTML内容
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度研究报告</title>
    <link rel="stylesheet" href="report_styles.css">
</head>
<body>
    <div class="container">
        <div class="content">
            {self._convert_markdown_to_html(content)}
        </div>
    </div>
    <script src="report_interactions.js"></script>
</body>
</html>"""
        
        Path(html_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_path
    
    async def _generate_css_file(self) -> str:
        """生成CSS文件"""
        css_path = "output/report_styles.css"
        
        css_content = """
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1, h2, h3 {
            color: #2c3e50;
            margin-top: 30px;
        }
        
        .interactive-section {
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .interactive-section:hover {
            background-color: #f8f9fa;
        }
        
        .expandable-content {
            display: none;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            margin: 10px 0;
        }
        
        .chart-container {
            margin: 20px 0;
            text-align: center;
        }
        """
        
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        return css_path
    
    async def _generate_js_file(self) -> str:
        """生成JavaScript文件"""
        js_path = "output/report_interactions.js"
        
        js_content = """
        document.addEventListener('DOMContentLoaded', function() {
            // 添加交互式折叠功能
            const interactiveSections = document.querySelectorAll('.interactive-section');
            
            interactiveSections.forEach(section => {
                section.addEventListener('click', function() {
                    const content = this.nextElementSibling;
                    if (content && content.classList.contains('expandable-content')) {
                        content.style.display = content.style.display === 'none' ? 'block' : 'none';
                    }
                });
            });
            
            // 添加平滑滚动
            const anchorLinks = document.querySelectorAll('a[href^="#"]');
            anchorLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
        """
        
        with open(js_path, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        return js_path
    
    def _convert_markdown_to_html(self, content: str) -> str:
        """将Markdown转换为HTML"""
        # 简单的Markdown到HTML转换
        html_content = content
        
        # 转换标题
        html_content = html_content.replace('### ', '<h3>')
        html_content = html_content.replace('## ', '<h2>')
        html_content = html_content.replace('# ', '<h1>')
        
        # 转换段落
        paragraphs = html_content.split('\n\n')
        html_paragraphs = []
        for p in paragraphs:
            if p.strip():
                if not p.startswith('<h'):
                    html_paragraphs.append(f'<p>{p}</p>')
                else:
                    html_paragraphs.append(p)
        
        return '\n'.join(html_paragraphs)
    
    def _assess_media_quality(self, content: str, media_files: List[str]) -> float:
        """评估媒体质量"""
        quality_score = 0.8  # 基础分数
        
        # 内容质量
        if len(content) > 1000:
            quality_score += 0.1
        
        # 文件生成成功率
        if media_files:
            quality_score += 0.1
        
        return min(1.0, quality_score)
    
    def _update_pipeline_metrics(self, output: MediaOutput):
        """更新管道指标"""
        self.pipeline_metrics["total_processed"] += 1
        
        # 更新格式分布
        format_key = output.format.value
        if format_key not in self.pipeline_metrics["format_distribution"]:
            self.pipeline_metrics["format_distribution"][format_key] = 0
        self.pipeline_metrics["format_distribution"][format_key] += 1
        
        # 更新成功率
        if output.quality_score > 0.7:
            success_count = self.pipeline_metrics.get("success_count", 0) + 1
            self.pipeline_metrics["success_count"] = success_count
            self.pipeline_metrics["success_rate"] = success_count / self.pipeline_metrics["total_processed"]
        
        # 更新平均处理时间
        total_time = (
            self.pipeline_metrics["average_processing_time"] * 
            (self.pipeline_metrics["total_processed"] - 1) +
            output.processing_time
        )
        self.pipeline_metrics["average_processing_time"] = total_time / self.pipeline_metrics["total_processed"]
    
    def get_pipeline_metrics(self) -> Dict[str, Any]:
        """获取管道指标"""
        return self.pipeline_metrics.copy()

# 使用示例
if __name__ == "__main__":
    async def test_research_to_media_pipeline():
        """测试研究到媒体管道"""
        
        # 创建管道
        pipeline = ResearchToMediaPipeline()
        
        # 创建研究请求
        research_request = ResearchRequest(
            scenario=ResearchScenario.INDUSTRY_REPORT,
            target="人工智能",
            audience="business",
            output_format="report"
        )
        
        # 创建媒体生成请求
        media_request = MediaGenerationRequest(
            research_request=research_request,
            target_format=MediaFormat.VIDEO,
            style_preferences={
                "tone": "专业而友好",
                "duration": "10分钟",
                "color_theme": "科技蓝"
            }
        )
        
        print("🎬 开始研究到媒体管道...")
        
        # 执行管道
        output = await pipeline.process_research_to_media(media_request)
        
        print(f"✅ 管道完成!")
        print(f"输出格式: {output.format.value}")
        print(f"质量评分: {output.quality_score:.2f}")
        print(f"处理时间: {output.processing_time:.2f}秒")
        print(f"生成文件: {len(output.media_files)}个")
        
        # 显示生成的文件
        for file in output.media_files:
            print(f"  - {file}")
        
        # 显示内容预览
        print(f"\n📄 内容预览:")
        print(output.primary_content[:500] + "...")
    
    # 运行测试
    asyncio.run(test_research_to_media_pipeline())