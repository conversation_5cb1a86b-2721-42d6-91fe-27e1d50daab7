import requests
import json
import re
import os
from typing import Optional, List, Dict
from urllib.parse import urljoin, urlparse
from PIL import Image
import time

class BookCoverCrawler:
    """Web crawler to search and download authentic book covers"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def search_book_cover(self, book_title: str, author: str, output_path: str) -> bool:
        """
        Search for authentic book cover and download it
        Returns: True if successful, False otherwise
        """
        print(f"Searching for book cover: {book_title} by {author}")
        
        # Try multiple sources in order of preference
        sources = [
            self.search_douban,
            self.search_google_books,
            self.search_open_library,
            self.search_goodreads_alternative,
            self.search_generic_image
        ]
        
        for search_method in sources:
            try:
                cover_url = search_method(book_title, author)
                if cover_url and self.download_image(cover_url, output_path):
                    print(f"✅ Successfully downloaded cover from {search_method.__name__}")
                    return True
            except Exception as e:
                print(f"❌ {search_method.__name__} failed: {e}")
                continue
        
        print("❌ All cover search methods failed, creating placeholder")
        return self.create_placeholder_cover(book_title, author, output_path)
    
    def search_douban(self, book_title: str, author: str) -> Optional[str]:
        """Search Douban Books for cover image"""
        try:
            # Douban book search API (public endpoint)
            search_url = "https://www.douban.com/search"
            params = {
                'cat': '1001',  # Books category
                'q': f'{book_title} {author}'
            }
            
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            
            # Parse HTML for book cover images
            import re
            cover_pattern = r'<img[^>]+src="([^"]*book[^"]*\.jpg[^"]*)"[^>]*>'
            matches = re.findall(cover_pattern, response.text)
            
            for match in matches:
                if 'book' in match and ('.jpg' in match or '.jpeg' in match):
                    # Clean up Douban image URL
                    clean_url = match.replace('/s/', '/l/')  # Get larger version
                    return clean_url
                    
        except Exception as e:
            print(f"Douban search error: {e}")
            
        return None
    
    def search_google_books(self, book_title: str, author: str) -> Optional[str]:
        """Search Google Books API for cover image"""
        try:
            api_url = "https://www.googleapis.com/books/v1/volumes"
            params = {
                'q': f'intitle:{book_title} inauthor:{author}',
                'maxResults': 5
            }
            
            response = self.session.get(api_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if 'items' in data:
                for item in data['items']:
                    volume_info = item.get('volumeInfo', {})
                    image_links = volume_info.get('imageLinks', {})
                    
                    # Try different image sizes
                    for size in ['extraLarge', 'large', 'medium', 'thumbnail']:
                        if size in image_links:
                            image_url = image_links[size]
                            # Get higher resolution version
                            image_url = image_url.replace('&zoom=1', '&zoom=3')
                            return image_url
                            
        except Exception as e:
            print(f"Google Books search error: {e}")
            
        return None
    
    def search_open_library(self, book_title: str, author: str) -> Optional[str]:
        """Search Open Library for cover image"""
        try:
            # Search Open Library
            search_url = "https://openlibrary.org/search.json"
            params = {
                'title': book_title,
                'author': author,
                'limit': 5
            }
            
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if 'docs' in data:
                for doc in data['docs']:
                    if 'cover_i' in doc:
                        cover_id = doc['cover_i']
                        # Open Library cover URL format
                        cover_url = f"https://covers.openlibrary.org/b/id/{cover_id}-L.jpg"
                        return cover_url
                        
        except Exception as e:
            print(f"Open Library search error: {e}")
            
        return None
    
    def search_goodreads_alternative(self, book_title: str, author: str) -> Optional[str]:
        """Search alternative sources that might have Goodreads-like data"""
        try:
            # Use a book database API alternative
            search_url = "https://www.googleapis.com/books/v1/volumes"
            params = {
                'q': f'{book_title} {author}',
                'maxResults': 10
            }
            
            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if 'items' in data:
                for item in data['items']:
                    volume_info = item.get('volumeInfo', {})
                    # Check if title and author match closely
                    title_match = book_title.lower() in volume_info.get('title', '').lower()
                    author_match = any(author.lower() in a.lower() 
                                     for a in volume_info.get('authors', []))
                    
                    if title_match and author_match:
                        image_links = volume_info.get('imageLinks', {})
                        if 'thumbnail' in image_links:
                            return image_links['thumbnail'].replace('&zoom=1', '&zoom=3')
                            
        except Exception as e:
            print(f"Alternative search error: {e}")
            
        return None
    
    def search_generic_image(self, book_title: str, author: str) -> Optional[str]:
        """Fallback: Search for book cover using image search"""
        try:
            # This is a fallback method - in production you might want to use
            # a proper image search API like Bing Image Search API
            
            # For now, return None to trigger placeholder creation
            return None
            
        except Exception as e:
            print(f"Generic image search error: {e}")
            
        return None
    
    def download_image(self, url: str, output_path: str) -> bool:
        """Download image from URL and save to output_path"""
        try:
            print(f"Downloading image from: {url}")
            response = self.session.get(url, timeout=15, stream=True)
            response.raise_for_status()
            
            # Check if it's actually an image
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"❌ Invalid content type: {content_type}")
                return False
            
            # Save the image
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Verify the image can be opened
            try:
                with Image.open(output_path) as img:
                    # Ensure minimum size
                    if img.width < 100 or img.height < 100:
                        print(f"❌ Image too small: {img.width}x{img.height}")
                        return False
                    
                    # Convert to RGB if necessary
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                        img.save(output_path, 'JPEG', quality=90)
                    
                    print(f"✅ Downloaded and verified image: {img.width}x{img.height}")
                    return True
                    
            except Exception as e:
                print(f"❌ Image verification failed: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Download failed: {e}")
            return False
    
    def create_placeholder_cover(self, book_title: str, author: str, output_path: str) -> bool:
        """Create a placeholder book cover with title and author"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Create a simple book cover placeholder
            width, height = 400, 600
            img = Image.new('RGB', (width, height), color=(240, 240, 240))
            draw = ImageDraw.Draw(img)
            
            # Try to use a nice font
            try:
                font_title = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 24)
                font_author = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 18)
            except:
                font_title = ImageFont.load_default()
                font_author = ImageFont.load_default()
            
            # Draw border
            draw.rectangle([10, 10, width-10, height-10], outline=(100, 100, 100), width=2)
            
            # Draw title
            title_lines = self.wrap_text(book_title, font_title, width - 40)
            y_offset = height // 3
            for line in title_lines:
                bbox = draw.textbbox((0, 0), line, font=font_title)
                text_width = bbox[2] - bbox[0]
                x = (width - text_width) // 2
                draw.text((x, y_offset), line, font=font_title, fill=(50, 50, 50))
                y_offset += 35
            
            # Draw author
            y_offset += 30
            author_lines = self.wrap_text(f"作者：{author}", font_author, width - 40)
            for line in author_lines:
                bbox = draw.textbbox((0, 0), line, font=font_author)
                text_width = bbox[2] - bbox[0]
                x = (width - text_width) // 2
                draw.text((x, y_offset), line, font=font_author, fill=(80, 80, 80))
                y_offset += 25
            
            # Save placeholder
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            img.save(output_path, 'JPEG', quality=90)
            print(f"✅ Created placeholder cover: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create placeholder: {e}")
            return False
    
    def wrap_text(self, text: str, font, max_width: int) -> List[str]:
        """Wrap text to fit within max_width"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            # Approximate width calculation
            if len(test_line) * 12 <= max_width:  # Rough estimation
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines or [text]  # Return original text if wrapping fails