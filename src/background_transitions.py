import numpy as np
from typing import List, Tuple, Callable
try:
    from moviepy import <PERSON><PERSON><PERSON>, ImageClip, CompositeVideoClip
    from moviepy import vfx
    MOVIEPY_AVAILABLE = True
except ImportError:
    try:
        # Try alternative import structure
        from moviepy.editor import VideoClip, ImageClip, CompositeVideoClip
        from moviepy.video import fx as vfx
        MOVIEPY_AVAILABLE = True
    except ImportError:
        MOVIEPY_AVAILABLE = False

class BackgroundTransitions:
    """Handle smooth transitions between background images"""
    
    def __init__(self):
        self.transition_duration = 1.0  # seconds
        
    def create_transition_clip(self, 
                             image1_path: str, 
                             image2_path: str, 
                             transition_type: str,
                             duration: float,
                             start_time: float,
                             video_size: Tuple[int, int]) -> VideoClip:
        """
        Create a transition clip between two images
        """
        if not MOVIEPY_AVAILABLE:
            raise RuntimeError("MoviePy not available for transitions")
        
        width, height = video_size
        
        # Load images
        img2 = ImageClip(image2_path).resized((width, height))
        
        # For simplicity, just use the image without complex transitions for now
        # This ensures compatibility and prevents errors
        if transition_type == "none" or transition_type == "cut":
            # Simple cut - no transition
            return img2.with_duration(duration).with_start(start_time)
        
        elif transition_type in ["fade", "crossfade"]:
            # Simple fade in
            try:
                img_with_fade = img2.with_duration(duration).with_effects([vfx.FadeIn(min(1.0, duration/3))])
                return img_with_fade.with_start(start_time)
            except:
                # Fallback to simple clip
                return img2.with_duration(duration).with_start(start_time)
        
        elif transition_type == "slide_left":
            # Slide transition with smooth movement
            try:
                fade_duration = min(self.transition_duration, duration/4)
                img_with_effects = img2.with_duration(duration).with_effects([
                    vfx.FadeIn(fade_duration),
                    vfx.Resize(width * 1.1)  # Slight zoom for movement feel
                ])
                return img_with_effects.with_start(start_time)
            except:
                return img2.with_duration(duration).with_start(start_time)
                
        elif transition_type == "zoom_in":
            # Zoom effect with scale animation
            try:
                fade_duration = min(self.transition_duration, duration/4)
                img_with_effects = img2.with_duration(duration).with_effects([
                    vfx.FadeIn(fade_duration),
                    vfx.Resize(1.2).resized((width, height))  # Start bigger then fit
                ])
                return img_with_effects.with_start(start_time)
            except:
                return img2.with_duration(duration).with_start(start_time)
                
        elif transition_type == "zoom_out":
            # Zoom out effect
            try:
                fade_duration = min(self.transition_duration, duration/4)
                img_with_effects = img2.with_duration(duration).with_effects([
                    vfx.FadeIn(fade_duration)
                ])
                return img_with_effects.with_start(start_time)
            except:
                return img2.with_duration(duration).with_start(start_time)
                
        else:
            # Enhanced default transition with fade
            try:
                fade_duration = min(self.transition_duration, duration/3)
                img_with_fade = img2.with_duration(duration).with_effects([
                    vfx.FadeIn(fade_duration)
                ])
                return img_with_fade.with_start(start_time)
            except:
                # If effects fail, return plain image
                return img2.with_duration(duration).with_start(start_time)
    
    def _create_crossfade(self, img1: ImageClip, img2: ImageClip, duration: float, start_time: float) -> VideoClip:
        """Create crossfade transition"""
        transition_dur = min(self.transition_duration, duration / 2)
        
        # First image fades out
        img1_clip = img1.with_duration(transition_dur).with_effects([vfx.FadeOut(transition_dur)])
        
        # Second image fades in and continues
        img2_clip = img2.with_duration(duration).with_effects([vfx.FadeIn(transition_dur)])
        
        # Composite the transition
        if transition_dur > 0:
            transition_clip = CompositeVideoClip([
                img1_clip.with_start(0),
                img2_clip.with_start(0)
            ], size=img2.size)
        else:
            transition_clip = img2_clip
        
        return transition_clip.with_start(start_time)
    
    def _create_slide(self, img1: ImageClip, img2: ImageClip, duration: float, start_time: float, 
                     direction: str, video_size: Tuple[int, int]) -> VideoClip:
        """Create sliding transition"""
        width, height = video_size
        transition_dur = min(self.transition_duration, duration / 2)
        
        def slide_effect(get_frame, t):
            if t < transition_dur:
                # During transition
                progress = t / transition_dur
                if direction == "left":
                    offset_x = int(-width * progress)
                else:  # right
                    offset_x = int(width * progress)
                
                # Get frames
                frame1 = get_frame(0)  # img1 frame
                frame2 = img2.get_frame(0)  # img2 frame
                
                # Create composite frame
                result = np.zeros((height, width, 3), dtype=np.uint8)
                
                # Position img1
                if direction == "left":
                    if offset_x + width > 0:
                        start_x = max(0, offset_x)
                        end_x = min(width, offset_x + width)
                        result[:, start_x:end_x] = frame1[:, start_x - offset_x:end_x - offset_x]
                else:  # right
                    if offset_x < width:
                        start_x = max(0, offset_x)
                        end_x = min(width, offset_x + width)
                        result[:, start_x:end_x] = frame1[:, start_x - offset_x:end_x - offset_x]
                
                # Position img2
                if direction == "left":
                    img2_offset = offset_x + width
                    if img2_offset < width:
                        start_x = max(0, img2_offset)
                        end_x = min(width, img2_offset + width)
                        result[:, start_x:end_x] = frame2[:, start_x - img2_offset:end_x - img2_offset]
                else:  # right
                    img2_offset = offset_x - width
                    if img2_offset + width > 0:
                        start_x = max(0, img2_offset)
                        end_x = min(width, img2_offset + width)
                        result[:, start_x:end_x] = frame2[:, start_x - img2_offset:end_x - img2_offset]
                
                return result
            else:
                # After transition, show img2
                return img2.get_frame(0)
        
        # Create custom clip
        slide_clip = VideoClip(
            make_frame=lambda t: slide_effect(img1.get_frame, t),
            duration=duration
        ).with_fps(24)
        
        return slide_clip.with_start(start_time)
    
    def _create_zoom(self, img1: ImageClip, img2: ImageClip, duration: float, start_time: float,
                    direction: str, video_size: Tuple[int, int]) -> VideoClip:
        """Create zoom transition"""
        width, height = video_size
        transition_dur = min(self.transition_duration, duration / 2)
        
        def zoom_effect(get_frame, t):
            if t < transition_dur:
                progress = t / transition_dur
                
                if direction == "in":
                    # Zoom into img1, then show img2
                    scale = 1.0 + progress * 0.5  # Zoom to 1.5x
                    alpha = 1.0 - progress
                else:
                    # Zoom out from img1, then show img2
                    scale = 1.0 + (1.0 - progress) * 0.5
                    alpha = 1.0 - progress
                
                # Get frames
                frame1 = get_frame(0)
                frame2 = img2.get_frame(0)
                
                # Scale img1
                if scale != 1.0:
                    from PIL import Image
                    pil_img = Image.fromarray(frame1)
                    new_size = (int(width * scale), int(height * scale))
                    scaled_img = pil_img.resize(new_size, Image.Resampling.LANCZOS)
                    
                    # Center crop to original size
                    left = (new_size[0] - width) // 2
                    top = (new_size[1] - height) // 2
                    scaled_img = scaled_img.crop((left, top, left + width, top + height))
                    frame1 = np.array(scaled_img)
                
                # Blend frames
                result = frame1.astype(np.float32) * alpha + frame2.astype(np.float32) * (1 - alpha)
                return result.astype(np.uint8)
            else:
                return img2.get_frame(0)
        
        zoom_clip = VideoClip(
            make_frame=lambda t: zoom_effect(img1.get_frame, t),
            duration=duration
        ).with_fps(24)
        
        return zoom_clip.with_start(start_time)
    
    def _create_pan(self, img1: ImageClip, img2: ImageClip, duration: float, start_time: float,
                   direction: str, video_size: Tuple[int, int]) -> VideoClip:
        """Create panning transition"""
        width, height = video_size
        transition_dur = min(self.transition_duration, duration / 2)
        
        # For panning, we'll create a smooth camera movement effect
        def pan_effect(get_frame, t):
            if t < transition_dur:
                progress = t / transition_dur
                
                # Create a wider canvas for panning
                canvas_width = int(width * 1.3)
                
                frame1 = get_frame(0)
                frame2 = img2.get_frame(0)
                
                # Resize images to canvas width
                from PIL import Image
                pil_img1 = Image.fromarray(frame1).resize((canvas_width, height), Image.Resampling.LANCZOS)
                pil_img2 = Image.fromarray(frame2).resize((canvas_width, height), Image.Resampling.LANCZOS)
                
                frame1_wide = np.array(pil_img1)
                frame2_wide = np.array(pil_img2)
                
                # Calculate pan offset
                max_offset = canvas_width - width
                if direction == "left":
                    offset = int(max_offset * progress)
                else:  # right
                    offset = int(max_offset * (1 - progress))
                
                # Blend the images
                alpha = 1.0 - progress
                blended = frame1_wide.astype(np.float32) * alpha + frame2_wide.astype(np.float32) * (1 - alpha)
                
                # Crop to viewport
                result = blended[:, offset:offset + width].astype(np.uint8)
                return result
            else:
                return img2.get_frame(0)
        
        pan_clip = VideoClip(
            make_frame=lambda t: pan_effect(img1.get_frame, t),
            duration=duration
        ).with_fps(24)
        
        return pan_clip.with_start(start_time)
    
    def create_background_sequence(self, background_segments: List[dict], video_size: Tuple[int, int]) -> List[VideoClip]:
        """
        Create complete background sequence with transitions
        """
        if not background_segments:
            return []
        
        clips = []
        
        print(f"🎬 Creating background sequence with {len(background_segments)} segments")
        
        for i, segment in enumerate(background_segments):
            if not segment.get('image_path') or not segment['image_path']:
                print(f"⚠️ Skipping segment {i}: no image path")
                continue
            
            start_time = segment['start_time']
            duration = segment['duration']
            transition_type = segment.get('transition_type', 'crossfade')
            
            print(f"  📹 Segment {i}: {transition_type} transition at {start_time:.1f}s (duration: {duration:.1f}s)")
            
            try:
                # Create image clip
                clip = ImageClip(segment['image_path']).resized(video_size)
                
                # Apply transition effects
                if i > 0 and transition_type in ["fade", "crossfade"]:
                    # Add fade in effect for non-first segments
                    fade_duration = min(1.0, duration/4)
                    clip = clip.with_duration(duration).with_effects([vfx.FadeIn(fade_duration)])
                    print(f"    ✨ Applied {fade_duration:.1f}s fade-in effect")
                else:
                    # Simple clip without transition
                    clip = clip.with_duration(duration)
                
                # Set the start time
                clip = clip.with_start(start_time)
                clips.append(clip)
                print(f"    ✅ Added clip: {start_time:.1f}s - {start_time + duration:.1f}s")
                
            except Exception as e:
                print(f"    ⚠️ Error creating segment {i}: {e}, using simple clip")
                # Fallback to simple image clip
                clip = ImageClip(segment['image_path']).resized(video_size)
                clip = clip.with_duration(duration).with_start(start_time)
                clips.append(clip)
        
        print(f"✅ Created {len(clips)} background clips")
        return clips