import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # OpenRouter (Legacy - will be replaced by Doubao LLM)
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # Doubao LLM Configuration - Updated to Doubao 1.6 Thinking
    ARK_API_KEY = os.getenv('ARK_API_KEY')
    ARK_BASE_URL = os.getenv('ARK_BASE_URL', 'https://ark.cn-beijing.volces.com/api/v3')
    ARK_MODEL = os.getenv('ARK_MODEL', 'doubao-seed-1-6-thinking-250615')
    
    DOUBAO_API_KEY = os.getenv('DOUBAO_API_KEY')
    # Legacy credentials (kept for image generation)
    DOUBAO_ACCESS_KEY = os.getenv('DOUBAO_ACCESS_KEY', 'YOUR_ACCESS_KEY_HERE')
    DOUBAO_SECRET_KEY = os.getenv('DOUBAO_SECRET_KEY', 'YOUR_SECRET_KEY_HERE')
    # Updated TTS credentials (2024-2025 API)
    DOUBAO_APP_ID = os.getenv('DOUBAO_APP_ID', '2588986021')
    DOUBAO_ACCESS_TOKEN = os.getenv('DOUBAO_ACCESS_TOKEN', 'QJO7AraMTRsLXz-daVlHSI2_8xMjlAxO')
    
    DOUBAO_IMAGE_ENDPOINT = os.getenv('DOUBAO_IMAGE_ENDPOINT', 'https://ark.cn-beijing.volces.com/api/v3')
    DOUBAO_TTS_ENDPOINT = os.getenv('DOUBAO_TTS_ENDPOINT', 'https://openspeech.bytedance.com')
    DOUBAO_MODEL_ID = os.getenv('DOUBAO_MODEL_ID', 'doubao-seedream-3.0-t2i')
    
    EDGE_TTS_VOICE = os.getenv('EDGE_TTS_VOICE', 'zh-CN-XiaoxiaoNeural')
    
    OUTPUT_DIR = os.getenv('OUTPUT_DIR', './outputs')
    TEMP_DIR = os.getenv('TEMP_DIR', './temp')
    
    VIDEO_WIDTH = 1080
    VIDEO_HEIGHT = 1920
    VIDEO_FPS = 30
    
    BGM_VOLUME = 0.2
    OPENING_DURATION = 2
    
    # Emotion synthesis settings
    ENABLE_EMOTION_MARKERS = True
    EMOTION_INTENSITY = 'moderate'  # 'subtle', 'moderate', 'strong'
    
    FONT_PATH = './assets/fonts/NotoSansSC-Regular.ttf'
    BGM_PATH = './assets/bgm/gentle_bgm.mp3'