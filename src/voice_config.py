import asyncio
import edge_tts
import os
import tempfile
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class Voice:
    """Voice configuration with metadata"""
    id: str
    name: str
    gender: str
    language: str
    locale: str
    description: str
    sample_text: str = "朋友们，今天和大家聊聊这本书。读完之后，我想说，真正的好书，就像一位智慧的老友。"

class VoiceManager:
    """Manages available TTS voices and provides preview functionality"""
    
    # Get voices from Doubao TTS instead of Edge TTS
    @classmethod
    def _get_doubao_voices(cls):
        """Get verified Doubao TTS voices"""
        from .doubao_tts import DoubaoTTS
        doubao = DoubaoTTS()
        return doubao.get_available_voices()
    
    # Fallback Edge TTS voices (deprecated but kept for compatibility)
    LEGACY_VOICES = {
        "zh-CN-XiaoxiaoNeural": Voice(
            id="zh-CN-XiaoxiaoNeural",
            name="晓晓 (女声)",
            gender="女",
            language="中文",
            locale="zh-CN",
            description="温柔甜美的女声，适合讲故事",
            sample_text="大家好，我是晓晓。今天和大家分享这本好书，希望能够给大家带来启发。"
        ),
        "zh-CN-YunyangNeural": Voice(
            id="zh-CN-YunyangNeural", 
            name="云扬 (男声)",
            gender="男",
            language="中文",
            locale="zh-CN",
            description="成熟稳重的男声，适合商务和新闻",
            sample_text="朋友们好，我是云扬。让我们一起走进书中的世界，感受文字的力量。"
        ),
        # "zh-CN-XiaohanNeural": Voice(
        #     id="zh-CN-XiaohanNeural",
        #     name="晓涵 (女声)",
        #     gender="女", 
        #     language="中文",
        #     locale="zh-CN",
        #     description="清新自然的女声，适合教育和文化内容",
        #     sample_text="大家好，我是晓涵。读书是一场心灵的旅行，让我们一起享受这美好的时光。"
        # ),  # Temporarily disabled due to availability issues
        "zh-CN-YunxiNeural": Voice(
            id="zh-CN-YunxiNeural",
            name="云希 (男声)",
            gender="男",
            language="中文", 
            locale="zh-CN",
            description="年轻活力的男声，适合时尚和娱乐内容",
            sample_text="嗨，大家好，我是云希。这本书真的很精彩，相信你们也会喜欢的。"
        ),
        "zh-CN-XiaozhenNeural": Voice(
            id="zh-CN-XiaozhenNeural",
            name="晓甄 (女声)",
            gender="女",
            language="中文",
            locale="zh-CN", 
            description="知性优雅的女声，适合专业和文学内容",
            sample_text="各位读者朋友，我是晓甄。文学的魅力在于能够触动我们内心最深处的情感。"
        ),
        "zh-CN-YunyeNeural": Voice(
            id="zh-CN-YunyeNeural",
            name="云野 (男声)",
            gender="男",
            language="中文",
            locale="zh-CN",
            description="磁性深沉的男声，适合情感和人文内容",
            sample_text="朋友们，我是云野。每一本好书都是一次心灵的洗礼，值得我们细细品味。"
        ),
        # "zh-CN-XiaomengNeural": Voice(
        #     id="zh-CN-XiaomengNeural",
        #     name="晓梦 (女声)",
        #     gender="女",
        #     language="中文",
        #     locale="zh-CN",
        #     description="梦幻轻柔的女声，适合诗歌和艺术内容",
        #     sample_text="亲爱的朋友们，我是晓梦。在书的世界里，我们可以找到最美的梦想。"
        # ),  # Temporarily disabled due to availability issues
        "zh-CN-YunfengNeural": Voice(
            id="zh-CN-YunfengNeural",
            name="云峰 (男声)",
            gender="男",
            language="中文",
            locale="zh-CN",
            description="浑厚有力的男声，适合历史和哲学内容", 
            sample_text="各位朋友，我是云峰。历史和哲学的智慧，需要我们用心去感悟。"
        )
    }
    
    @classmethod
    def get_available_voices(cls) -> Dict[str, Voice]:
        """Get all available voices from Doubao TTS"""
        try:
            doubao_voices = cls._get_doubao_voices()
            # Convert Doubao voice format to Voice objects
            voices = {}
            for voice_id, voice_info in doubao_voices.items():
                voices[voice_id] = Voice(
                    id=voice_id,
                    name=voice_info.get('name', voice_id),
                    gender=voice_info.get('gender', 'unknown'),
                    language="中文",
                    locale="zh-CN",
                    description=voice_info.get('description', ''),
                    sample_text="朋友们，今天和大家聊聊这本书。"
                )
            return voices
        except Exception as e:
            print(f"⚠️ Failed to get Doubao voices: {e}")
            return cls.LEGACY_VOICES.copy()
    
    @classmethod
    def get_voice_list(cls) -> List[Dict]:
        """Get simplified voice list for API responses"""
        available_voices = cls.get_available_voices()
        return [
            {
                "id": voice.id,
                "name": voice.name,
                "gender": voice.gender,
                "description": voice.description,
                "provider": "豆包TTS" if "doubao" in voice.id.lower() else "Edge TTS"
            }
            for voice in available_voices.values()
        ]
    
    @classmethod
    def get_voice(cls, voice_id: str) -> Optional[Voice]:
        """Get voice by ID"""
        available_voices = cls.get_available_voices()
        return available_voices.get(voice_id)
    
    @classmethod
    async def generate_voice_preview(cls, voice_id: str, output_path: str) -> bool:
        """Generate a preview audio sample for the specified voice"""
        try:
            voice = cls.get_voice(voice_id)
            if not voice:
                print(f"Voice {voice_id} not found")
                return False
            
            # Generate preview using Edge TTS
            communicate = edge_tts.Communicate(voice.sample_text, voice_id)
            await communicate.save(output_path)
            
            print(f"✅ Voice preview generated: {voice.name}")
            return True
            
        except Exception as e:
            print(f"❌ Voice preview generation failed: {e}")
            return False
    
    @classmethod
    def generate_voice_preview_sync(cls, voice_id: str, output_path: str) -> bool:
        """Synchronous wrapper for voice preview generation"""
        return asyncio.run(cls.generate_voice_preview(voice_id, output_path))
    
    @classmethod
    def get_voice_categories(cls) -> Dict[str, List[str]]:
        """Group voices by characteristics for easier selection"""
        categories = {
            "女声温柔": [],
            "女声知性": [],
            "男声稳重": [],
            "男声活力": []
        }
        
        available_voices = cls.get_available_voices()
        for voice_id, voice in available_voices.items():
            if voice.gender == "女":
                if "温柔" in voice.description or "甜美" in voice.description:
                    categories["女声温柔"].append(voice_id)
                else:
                    categories["女声知性"].append(voice_id)
            else:  # 男声
                if "年轻" in voice.description or "活力" in voice.description:
                    categories["男声活力"].append(voice_id)
                else:
                    categories["男声稳重"].append(voice_id)
        
        return categories
    
    @classmethod
    def validate_voice_id(cls, voice_id: str) -> bool:
        """Validate if voice ID exists"""
        available_voices = cls.get_available_voices()
        return voice_id in available_voices
    
    @classmethod
    def get_default_voice(cls) -> str:
        """Get default voice ID - prioritize verified Doubao voices"""
        try:
            doubao_voices = cls._get_doubao_voices()
            if doubao_voices:
                # Return first verified voice (should be zh_female_roumeinvyou_emo_v2_mars_bigtts)
                return list(doubao_voices.keys())[0]
        except Exception:
            pass
        # Fallback to legacy voice
        return "zh-CN-XiaoxiaoNeural"
    
    @classmethod
    def get_recommended_voices_for_content(cls, content_type: str) -> List[str]:
        """Get recommended voices based on content type"""
        recommendations = {
            "文学": ["zh-CN-XiaozhenNeural", "zh-CN-YunyeNeural", "zh-CN-XiaoxiaoNeural"],
            "历史": ["zh-CN-YunfengNeural", "zh-CN-YunyangNeural", "zh-CN-XiaozhenNeural"],
            "哲学": ["zh-CN-YunfengNeural", "zh-CN-YunyeNeural", "zh-CN-YunyangNeural"],
            "科普": ["zh-CN-YunyangNeural", "zh-CN-XiaoxiaoNeural", "zh-CN-XiaozhenNeural"],
            "小说": ["zh-CN-XiaoxiaoNeural", "zh-CN-YunxiNeural", "zh-CN-XiaozhenNeural"],
            "传记": ["zh-CN-YunyeNeural", "zh-CN-XiaozhenNeural", "zh-CN-YunfengNeural"]
        }
        
        # Filter out any disabled voices and return only available ones
        available_voices = cls.get_available_voices()
        valid_recommendations = [voice for voice in recommendations.get(content_type, [cls.get_default_voice()]) 
                                if voice in available_voices]
        
        return valid_recommendations if valid_recommendations else [cls.get_default_voice()]
    
    @classmethod
    async def validate_voice(cls, voice_id: str) -> bool:
        """Validate if a voice is working"""
        try:
            import edge_tts
            test_text = "测试"
            communicate = edge_tts.Communicate(test_text, voice_id)
            
            # Try to get some audio data
            audio_data = b''
            async for chunk in communicate.stream():
                if chunk['type'] == 'audio':
                    audio_data += chunk['data']
                    break  # Just need to confirm we get some data
            
            return len(audio_data) > 0
        except Exception:
            return False
    
    @classmethod
    def validate_voice_sync(cls, voice_id: str) -> bool:
        """Synchronous wrapper for voice validation"""
        import asyncio
        return asyncio.run(cls.validate_voice(voice_id))
    
    @classmethod
    def get_working_voices(cls) -> List[str]:
        """Get list of currently working voice IDs"""
        # For now, return the known working voices from our testing
        # In production, you might want to cache this and refresh periodically
        working_voices = [
            "zh-CN-XiaoxiaoNeural",
            "zh-CN-YunyangNeural", 
            "zh-CN-YunxiNeural",
            "zh-CN-XiaozhenNeural",
            "zh-CN-YunyeNeural",
            "zh-CN-YunfengNeural"
        ]
        
        # Filter to only include voices that are in our configuration
        available_voices = cls.get_available_voices()
        return [voice for voice in working_voices if voice in available_voices]