import requests
import json
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup
import re
from urllib.parse import quote
from .book_info_crawler import BookInfo

@dataclass
class BookContent:
    """Detailed book content analysis"""
    plot_summary: Optional[str] = None
    main_characters: List[str] = None
    key_themes: List[str] = None
    notable_quotes: List[str] = None
    chapter_summaries: List[str] = None
    critical_analysis: List[str] = None
    reader_insights: List[str] = None
    cultural_context: Optional[str] = None
    literary_significance: Optional[str] = None
    
    def __post_init__(self):
        if self.main_characters is None:
            self.main_characters = []
        if self.key_themes is None:
            self.key_themes = []
        if self.notable_quotes is None:
            self.notable_quotes = []
        if self.chapter_summaries is None:
            self.chapter_summaries = []
        if self.critical_analysis is None:
            self.critical_analysis = []
        if self.reader_insights is None:
            self.reader_insights = []

class BookContentAnalyzer:
    """Analyzes book content to extract plot, characters, themes, and meaningful details"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.last_request_time = 0
        self.min_request_interval = 1.5
    
    def _rate_limit(self):
        """Rate limiting for web requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()
    
    def analyze_book_content(self, book_info: BookInfo) -> BookContent:
        """Comprehensive analysis of book content from multiple sources"""
        print(f"📖 Analyzing content for '{book_info.title}' by {book_info.author}...")
        
        content = BookContent()
        
        # Source 1: Douban detailed analysis
        douban_content = self._analyze_from_douban(book_info.title, book_info.author)
        if douban_content:
            content = self._merge_content(content, douban_content)
        
        # Source 2: Zhihu discussions and analyses
        zhihu_content = self._analyze_from_zhihu(book_info.title, book_info.author)
        if zhihu_content:
            content = self._merge_content(content, zhihu_content)
        
        # Source 3: Academic and literary analysis sites
        academic_content = self._analyze_from_academic_sources(book_info.title, book_info.author)
        if academic_content:
            content = self._merge_content(content, academic_content)
        
        # Source 4: Wiki and encyclopedia entries
        wiki_content = self._analyze_from_wiki(book_info.title, book_info.author)
        if wiki_content:
            content = self._merge_content(content, wiki_content)
        
        # Enhance with AI analysis if we have basic info
        if book_info.description and len(book_info.description) > 100:
            ai_content = self._ai_enhance_content(book_info, content)
            if ai_content:
                content = self._merge_content(content, ai_content)
        
        print(f"📋 Content analysis complete:")
        print(f"   📜 Plot summary: {'✅' if content.plot_summary else '❌'}")
        print(f"   👥 Characters: {len(content.main_characters)} found")
        print(f"   🎭 Themes: {len(content.key_themes)} identified")
        print(f"   💬 Quotes: {len(content.notable_quotes)} collected")
        print(f"   🔍 Analysis: {len(content.critical_analysis)} insights")
        
        return content
    
    def _analyze_from_douban(self, title: str, author: str) -> Optional[BookContent]:
        """Extract detailed content from Douban book discussions"""
        try:
            self._rate_limit()
            
            # Search for book page
            search_query = f"{title} {author}".strip()
            search_url = f"https://www.douban.com/search?q={quote(search_query)}&cat=1001"
            
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find the book URL
            book_url = None
            results = soup.find_all('div', class_='result')
            for result in results[:3]:
                link = result.find('h3', class_='title').find('a') if result.find('h3', class_='title') else None
                if link and '/subject/' in link.get('href', ''):
                    book_url = link.get('href')
                    break
            
            if not book_url:
                return None
            
            # Get detailed book page
            self._rate_limit()
            book_response = self.session.get(book_url, timeout=10)
            book_response.raise_for_status()
            
            book_soup = BeautifulSoup(book_response.content, 'html.parser')
            
            content = BookContent()
            
            # Extract plot summary from description
            intro_elem = book_soup.find('div', class_='intro')
            if intro_elem:
                full_text = intro_elem.get_text().strip()
                if len(full_text) > 50:
                    content.plot_summary = full_text
            
            # Extract themes from tags
            tag_elems = book_soup.find_all('a', href=re.compile(r'/tag/'))
            themes = []
            for tag in tag_elems[:8]:
                tag_text = tag.get_text().strip()
                if len(tag_text) < 20 and tag_text not in themes:
                    themes.append(tag_text)
            content.key_themes = themes
            
            # Extract quotes and insights from reviews
            reviews = book_soup.find_all('div', class_='review-short')
            insights = []
            quotes = []
            
            for review in reviews[:10]:
                try:
                    review_text = review.find('div', class_='short-content')
                    if review_text:
                        text = review_text.get_text().strip()
                        
                        # Look for quotes (text in quotes)
                        quote_matches = re.findall(r'["""]([^"""]{20,100})["""]', text)
                        quotes.extend(quote_matches[:2])
                        
                        # Extract meaningful insights
                        if len(text) > 100 and len(text) < 500:
                            # Filter for analytical content
                            if any(keyword in text for keyword in ['主题', '象征', '隐喻', '深层', '反映', '揭示', '探讨']):
                                insights.append(text[:200])
                except Exception:
                    continue
            
            content.notable_quotes = quotes[:5]
            content.reader_insights = insights[:5]
            
            return content
            
        except Exception as e:
            print(f"❌ Douban analysis error: {e}")
            return None
    
    def _analyze_from_zhihu(self, title: str, author: str) -> Optional[BookContent]:
        """Extract analysis from Zhihu discussions"""
        try:
            self._rate_limit()
            
            # Search Zhihu for book discussions
            search_query = f"{title} {author} 书评 分析"
            search_url = f"https://www.zhihu.com/search?type=content&q={quote(search_query)}"
            
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            # Note: Zhihu has anti-crawling measures, so this is a basic implementation
            # In production, you might want to use Zhihu's API if available
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            content = BookContent()
            
            # Look for analysis snippets in search results
            analysis_snippets = []
            result_items = soup.find_all('div', class_='SearchResult-Card')
            
            for item in result_items[:5]:
                try:
                    text_elem = item.find('span', class_='SearchResult-fullText')
                    if text_elem:
                        text = text_elem.get_text().strip()
                        if len(text) > 50 and any(keyword in text for keyword in ['分析', '主题', '人物', '情节']):
                            analysis_snippets.append(text[:300])
                except Exception:
                    continue
            
            content.critical_analysis = analysis_snippets[:3]
            
            return content if analysis_snippets else None
            
        except Exception as e:
            print(f"❌ Zhihu analysis error: {e}")
            return None
    
    def _analyze_from_academic_sources(self, title: str, author: str) -> Optional[BookContent]:
        """Extract from academic and literary analysis sites"""
        try:
            # Search academic databases and literature sites
            search_urls = [
                f"http://www.literature.org.cn/search?q={quote(title + ' ' + author)}",
                f"https://baike.baidu.com/search?word={quote(title)}",
            ]
            
            content = BookContent()
            literary_analysis = []
            
            for url in search_urls:
                try:
                    self._rate_limit()
                    response = self.session.get(url, timeout=8)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Extract academic analysis
                        paragraphs = soup.find_all('p')
                        for p in paragraphs:
                            text = p.get_text().strip()
                            if len(text) > 100 and any(keyword in text for keyword in 
                                ['文学意义', '艺术特色', '思想内容', '创作背景', '影响']):
                                literary_analysis.append(text[:250])
                                if len(literary_analysis) >= 3:
                                    break
                
                except Exception:
                    continue
            
            content.literary_significance = '\n'.join(literary_analysis[:2]) if literary_analysis else None
            
            return content if literary_analysis else None
            
        except Exception as e:
            print(f"❌ Academic sources error: {e}")
            return None
    
    def _analyze_from_wiki(self, title: str, author: str) -> Optional[BookContent]:
        """Extract content from Wikipedia and Baike"""
        try:
            self._rate_limit()
            
            # Try Wikipedia first
            wiki_urls = [
                f"https://zh.wikipedia.org/wiki/{quote(title)}",
                f"https://baike.baidu.com/item/{quote(title)}",
            ]
            
            content = BookContent()
            
            for url in wiki_urls:
                try:
                    response = self.session.get(url, timeout=8)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Extract plot summary
                        plot_sections = soup.find_all(['p', 'div'], string=re.compile(r'(情节|剧情|内容|故事)'))
                        for section in plot_sections:
                            parent = section.find_parent()
                            if parent:
                                text = parent.get_text().strip()
                                if len(text) > 100:
                                    content.plot_summary = text[:500]
                                    break
                        
                        # Extract character information
                        char_sections = soup.find_all(['p', 'div'], string=re.compile(r'(人物|角色|主人公)'))
                        characters = []
                        for section in char_sections:
                            parent = section.find_parent()
                            if parent:
                                text = parent.get_text()
                                # Extract character names (simplified)
                                char_matches = re.findall(r'([A-Za-z\u4e00-\u9fff]{2,10})', text)
                                characters.extend(char_matches[:5])
                        
                        content.main_characters = list(set(characters))[:8]
                        
                        if content.plot_summary or content.main_characters:
                            return content
                
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            print(f"❌ Wiki analysis error: {e}")
            return None
    
    def _ai_enhance_content(self, book_info: BookInfo, existing_content: BookContent) -> Optional[BookContent]:
        """Use AI to enhance and analyze existing content"""
        try:
            from .config import Config
            
            # Build comprehensive prompt with all available information
            info_text = f"书名：{book_info.title}\n作者：{book_info.author}\n"
            
            if book_info.description:
                info_text += f"简介：{book_info.description}\n"
            
            if book_info.genres:
                info_text += f"类型：{', '.join(book_info.genres)}\n"
            
            if existing_content.plot_summary:
                info_text += f"情节概要：{existing_content.plot_summary}\n"
            
            if existing_content.key_themes:
                info_text += f"主要主题：{', '.join(existing_content.key_themes)}\n"
            
            prompt = f"""
            基于以下书籍信息，请提供详细的内容分析：

            {info_text}

            请以JSON格式返回详细分析，包含：
            1. plot_summary: 完整的情节概要（如果现有概要不够详细）
            2. main_characters: 主要人物列表及其特点
            3. key_themes: 核心主题和思想内容
            4. notable_quotes: 重要段落或金句（如果知道的话）
            5. critical_analysis: 文学价值和深层分析
            6. cultural_context: 文化背景和历史意义

            要求：
            - 基于已知信息进行合理推断和分析
            - 突出作品的独特价值和深刻内涵
            - 提供有助于理解作品的具体细节
            - 如果某些信息不确定，可以标注"推测"

            返回格式：
            {{
                "plot_summary": "详细情节概要",
                "main_characters": ["人物1", "人物2"],
                "key_themes": ["主题1", "主题2"],
                "notable_quotes": ["重要段落1", "重要段落2"],
                "critical_analysis": ["分析1", "分析2"],
                "cultural_context": "文化背景分析"
            }}
            """
            
            # This would call an AI service - for now, return enhanced analysis based on available data
            enhanced_content = BookContent()
            
            # Enhance themes based on book description and genre
            if book_info.description and book_info.genres:
                enhanced_themes = self._extract_themes_from_description(book_info.description, book_info.genres)
                enhanced_content.key_themes = enhanced_themes
            
            # Generate character analysis based on description
            if book_info.description:
                characters = self._extract_characters_from_description(book_info.description)
                enhanced_content.main_characters = characters
            
            # Create cultural context
            if book_info.publication_year and book_info.author:
                context = self._generate_cultural_context(book_info.author, book_info.publication_year, book_info.title)
                enhanced_content.cultural_context = context
            
            return enhanced_content
            
        except Exception as e:
            print(f"❌ AI enhancement error: {e}")
            return None
    
    def _extract_themes_from_description(self, description: str, genres: List[str]) -> List[str]:
        """Extract themes from book description"""
        themes = set()
        
        # Theme keywords mapping
        theme_patterns = {
            '人性探索': ['人性', '人心', '道德', '善恶', '伦理'],
            '爱情与情感': ['爱情', '恋爱', '情感', '浪漫', '情人', '婚姻'],
            '成长与青春': ['成长', '青春', '童年', '成熟', '蜕变', '少年'],
            '家庭与亲情': ['家庭', '亲情', '父母', '家族', '血缘', '传承'],
            '社会批判': ['社会', '现实', '体制', '制度', '阶级', '权力'],
            '历史与传统': ['历史', '过去', '传统', '文化', '时代', '古代'],
            '哲学思辨': ['哲学', '思考', '存在', '意义', '真理', '智慧'],
            '战争与和平': ['战争', '冲突', '斗争', '革命', '和平', '政治'],
            '科技与未来': ['科技', '未来', '科学', '技术', '现代化'],
            '死亡与永恒': ['死亡', '生死', '永恒', '命运', '轮回'],
            '自由与束缚': ['自由', '束缚', '压迫', '解放', '独立'],
            '孤独与救赎': ['孤独', '救赎', '拯救', '希望', '绝望']
        }
        
        text_to_check = (description + ' ' + ' '.join(genres)).lower()
        
        for theme, keywords in theme_patterns.items():
            if any(keyword in text_to_check for keyword in keywords):
                themes.add(theme)
        
        return list(themes)[:6]
    
    def _extract_characters_from_description(self, description: str) -> List[str]:
        """Extract character names and types from description"""
        characters = []
        
        # Look for character-related terms
        char_patterns = [
            r'主人公([^，。！？\s]{1,6})',
            r'主角([^，。！？\s]{1,6})',
            r'([^，。！？\s]{2,6})(是|为|成为).{0,10}(主人公|主角|英雄|女主)',
        ]
        
        for pattern in char_patterns:
            matches = re.findall(pattern, description)
            for match in matches:
                if isinstance(match, tuple):
                    char_name = match[0]
                else:
                    char_name = match
                
                if len(char_name) >= 2 and char_name not in characters:
                    characters.append(char_name)
        
        # Add character archetypes if no specific names found
        if not characters:
            archetypes = []
            if any(word in description for word in ['少年', '青年', '男孩']):
                archetypes.append('少年主人公')
            if any(word in description for word in ['少女', '女子', '女孩']):
                archetypes.append('女主人公')
            if any(word in description for word in ['父亲', '母亲', '老人']):
                archetypes.append('长辈角色')
            
            characters.extend(archetypes)
        
        return characters[:5]
    
    def _generate_cultural_context(self, author: str, publication_year: Optional[int], title: str) -> str:
        """Generate cultural and historical context"""
        context_parts = []
        
        # Author background
        if '马尔克斯' in author:
            context_parts.append('加西亚·马尔克斯是拉丁美洲魔幻现实主义文学的代表人物')
        elif '卡夫卡' in author:
            context_parts.append('卡夫卡是20世纪现代主义文学的重要代表')
        elif '托尔斯泰' in author:
            context_parts.append('托尔斯泰是19世纪俄国批判现实主义文学的巨匠')
        
        # Time period context
        if publication_year:
            if publication_year < 1900:
                context_parts.append(f'{publication_year}年代的社会背景对作品产生了深刻影响')
            elif publication_year < 1950:
                context_parts.append(f'作品创作于{publication_year}年，反映了当时的社会变革')
            else:
                context_parts.append(f'{publication_year}年代的文化环境为作品提供了创作土壤')
        
        # Literary significance
        if any(keyword in title for keyword in ['百年', '战争', '和平', '罪', '罚']):
            context_parts.append('这部作品在世界文学史上具有重要地位')
        
        return '。'.join(context_parts) + '。' if context_parts else None
    
    def _merge_content(self, base: BookContent, new: BookContent) -> BookContent:
        """Merge content from multiple sources"""
        # Use the most comprehensive version of each field
        merged = BookContent()
        
        # Plot summary - use the longer, more detailed one
        if new.plot_summary and (not base.plot_summary or len(new.plot_summary) > len(base.plot_summary)):
            merged.plot_summary = new.plot_summary
        else:
            merged.plot_summary = base.plot_summary
        
        # Merge lists, avoiding duplicates
        merged.main_characters = list(set(base.main_characters + new.main_characters))[:10]
        merged.key_themes = list(set(base.key_themes + new.key_themes))[:8]
        merged.notable_quotes = list(set(base.notable_quotes + new.notable_quotes))[:6]
        merged.critical_analysis = list(set(base.critical_analysis + new.critical_analysis))[:5]
        merged.reader_insights = list(set(base.reader_insights + new.reader_insights))[:5]
        
        # Cultural context and literary significance
        merged.cultural_context = new.cultural_context or base.cultural_context
        merged.literary_significance = new.literary_significance or base.literary_significance
        
        return merged