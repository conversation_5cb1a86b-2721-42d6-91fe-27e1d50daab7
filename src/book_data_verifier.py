import re
import difflib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from .book_info_crawler import BookInfo
import logging

@dataclass
class VerificationResult:
    """Result of book data verification"""
    is_verified: bool
    confidence_score: float
    verification_notes: List[str]
    missing_fields: List[str]
    conflicting_data: List[Dict]

class BookDataVerifier:
    """Verify and validate book data quality and consistency"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Essential fields for book reviews
        self.essential_fields = [
            'title', 'author', 'description', 'genres'
        ]
        
        # Recommended fields for high-quality reviews
        self.recommended_fields = [
            'publication_year', 'publisher', 'average_rating', 
            'themes', 'reviews'
        ]
    
    def verify_book_data(self, book_info: BookInfo) -> VerificationResult:
        """Comprehensive verification of book data quality"""
        verification_notes = []
        missing_fields = []
        conflicting_data = []
        confidence_factors = []
        
        # Check essential fields
        for field in self.essential_fields:
            value = getattr(book_info, field, None)
            if not value or (isinstance(value, list) and len(value) == 0):
                missing_fields.append(field)
                verification_notes.append(f"Missing essential field: {field}")
            else:
                confidence_factors.append(0.25)  # Each essential field adds 25%
        
        # Check recommended fields
        for field in self.recommended_fields:
            value = getattr(book_info, field, None)
            if not value or (isinstance(value, list) and len(value) == 0):
                verification_notes.append(f"Missing recommended field: {field}")
            else:
                confidence_factors.append(0.1)  # Each recommended field adds 10%
        
        # Validate data quality
        quality_checks = [
            self._validate_title(book_info.title),
            self._validate_author(book_info.author),
            self._validate_publication_year(book_info.publication_year),
            self._validate_rating(book_info.average_rating),
            self._validate_description(book_info.description),
            self._validate_isbn(book_info.isbn, book_info.isbn13)
        ]
        
        for is_valid, note in quality_checks:
            if is_valid:
                confidence_factors.append(0.05)  # Each validation adds 5%
            if note:
                verification_notes.append(note)
        
        # Calculate confidence score
        base_confidence = min(sum(confidence_factors), 1.0)
        
        # Penalty for missing essential fields
        missing_essential_penalty = len(missing_fields) * 0.2
        final_confidence = max(0.0, base_confidence - missing_essential_penalty)
        
        # Determine if verified
        is_verified = (
            len(missing_fields) == 0 and 
            final_confidence >= 0.7 and
            book_info.description and
            len(book_info.description) > 50
        )
        
        return VerificationResult(
            is_verified=is_verified,
            confidence_score=final_confidence,
            verification_notes=verification_notes,
            missing_fields=missing_fields,
            conflicting_data=conflicting_data
        )
    
    def _validate_title(self, title: str) -> Tuple[bool, Optional[str]]:
        """Validate book title"""
        if not title:
            return False, "Title is empty"
        
        if len(title.strip()) < 2:
            return False, "Title is too short"
        
        if len(title) > 200:
            return False, "Title is unusually long"
        
        # Check for suspicious patterns
        if re.search(r'^\d+$', title):
            return False, "Title appears to be just numbers"
        
        return True, None
    
    def _validate_author(self, author: str) -> Tuple[bool, Optional[str]]:
        """Validate author name"""
        if not author:
            return False, "Author is empty"
        
        if len(author.strip()) < 2:
            return False, "Author name is too short"
        
        if len(author) > 100:
            return False, "Author name is unusually long"
        
        return True, None
    
    def _validate_publication_year(self, year: Optional[int]) -> Tuple[bool, Optional[str]]:
        """Validate publication year"""
        if year is None:
            return False, "Publication year is missing"
        
        if not isinstance(year, int):
            return False, "Publication year is not a number"
        
        if year < 1000 or year > 2030:
            return False, f"Publication year {year} is out of reasonable range"
        
        return True, None
    
    def _validate_rating(self, rating: Optional[float]) -> Tuple[bool, Optional[str]]:
        """Validate average rating"""
        if rating is None:
            return False, "Rating is missing"
        
        if not isinstance(rating, (int, float)):
            return False, "Rating is not a number"
        
        if rating < 0 or rating > 10:
            return False, f"Rating {rating} is out of valid range (0-10)"
        
        return True, None
    
    def _validate_description(self, description: Optional[str]) -> Tuple[bool, Optional[str]]:
        """Validate book description"""
        if not description:
            return False, "Description is missing"
        
        if len(description.strip()) < 50:
            return False, "Description is too short for meaningful review"
        
        if len(description) > 5000:
            return False, "Description is unusually long"
        
        # Check for meaningful content
        if re.search(r'^[\s\n\r]*$', description):
            return False, "Description contains only whitespace"
        
        return True, None
    
    def _validate_isbn(self, isbn: Optional[str], isbn13: Optional[str]) -> Tuple[bool, Optional[str]]:
        """Validate ISBN numbers"""
        if not isbn and not isbn13:
            return False, "No ISBN provided"
        
        # Validate ISBN-10
        if isbn:
            isbn_clean = re.sub(r'[^\dX]', '', isbn.upper())
            if len(isbn_clean) != 10:
                return False, f"ISBN-10 has incorrect length: {len(isbn_clean)}"
        
        # Validate ISBN-13
        if isbn13:
            isbn13_clean = re.sub(r'[^\d]', '', isbn13)
            if len(isbn13_clean) != 13:
                return False, f"ISBN-13 has incorrect length: {len(isbn13_clean)}"
        
        return True, None
    
    def enhance_book_data(self, book_info: BookInfo) -> BookInfo:
        """Enhance book data with inferred information"""
        enhanced_info = book_info
        
        # Infer themes from description and genres
        if book_info.description and not book_info.themes:
            enhanced_info.themes = self._infer_themes(book_info.description, book_info.genres)
        
        # Clean and standardize genres
        if book_info.genres:
            enhanced_info.genres = self._clean_genres(book_info.genres)
        
        # Extract key quotes from reviews
        if book_info.reviews:
            enhanced_info.reviews = self._enhance_reviews(book_info.reviews)
        
        return enhanced_info
    
    def _infer_themes(self, description: str, genres: List[str]) -> List[str]:
        """Infer themes from description and genres"""
        themes = set()
        
        # Theme inference patterns
        theme_patterns = {
            '人性': ['人性', '人心', '善恶', '道德', '伦理'],
            '成长': ['成长', '青春', '童年', '成熟', '蜕变'],
            '爱情': ['爱情', '恋爱', '情感', '浪漫', '情人'],
            '家庭': ['家庭', '亲情', '父母', '家族', '血缘'],
            '社会': ['社会', '现实', '体制', '制度', '阶级'],
            '历史': ['历史', '过去', '传统', '文化', '时代'],
            '哲学': ['哲学', '思考', '存在', '意义', '真理'],
            '战争': ['战争', '冲突', '斗争', '革命', '和平'],
            '科幻': ['科技', '未来', '科学', '太空', '机器'],
            '悬疑': ['悬疑', '推理', '谜团', '秘密', '真相']
        }
        
        text_to_check = (description + ' ' + ' '.join(genres)).lower()
        
        for theme, keywords in theme_patterns.items():
            if any(keyword in text_to_check for keyword in keywords):
                themes.add(theme)
        
        return list(themes)[:6]  # Limit to 6 themes
    
    def _clean_genres(self, genres: List[str]) -> List[str]:
        """Clean and standardize genre list"""
        cleaned_genres = []
        seen = set()
        
        # Genre mapping for standardization
        genre_mapping = {
            '小说': '小说',
            'fiction': '小说',
            '文学': '文学',
            'literature': '文学',
            '历史': '历史',
            'history': '历史',
            '传记': '传记',
            'biography': '传记',
            '哲学': '哲学',
            'philosophy': '哲学',
            '科幻': '科幻',
            'science fiction': '科幻',
            '悬疑': '悬疑',
            'mystery': '悬疑',
            '推理': '推理',
            'detective': '推理'
        }
        
        for genre in genres:
            if not genre or len(genre.strip()) == 0:
                continue
            
            genre_clean = genre.strip().lower()
            
            # Map to standard genre if possible
            standard_genre = genre_mapping.get(genre_clean, genre.strip())
            
            # Avoid duplicates
            if standard_genre.lower() not in seen and len(standard_genre) <= 20:
                cleaned_genres.append(standard_genre)
                seen.add(standard_genre.lower())
        
        return cleaned_genres[:10]  # Limit to 10 genres
    
    def _enhance_reviews(self, reviews: List[Dict]) -> List[Dict]:
        """Enhance review data with sentiment and key phrases"""
        enhanced_reviews = []
        
        for review in reviews[:5]:  # Limit to 5 reviews
            if not review.get('text'):
                continue
            
            review_text = review['text']
            
            # Extract sentiment keywords
            positive_keywords = ['好', '棒', '精彩', '优秀', '推荐', '喜欢', '感动', '深刻']
            negative_keywords = ['差', '无聊', '失望', '糟糕', '不推荐', '讨厌', '浅薄']
            
            positive_count = sum(1 for word in positive_keywords if word in review_text)
            negative_count = sum(1 for word in negative_keywords if word in review_text)
            
            sentiment = 'positive' if positive_count > negative_count else 'negative' if negative_count > positive_count else 'neutral'
            
            enhanced_review = {
                'text': review_text,
                'source': review.get('source', 'unknown'),
                'sentiment': sentiment,
                'length': len(review_text)
            }
            
            enhanced_reviews.append(enhanced_review)
        
        return enhanced_reviews
    
    def generate_data_quality_report(self, book_info: BookInfo, verification_result: VerificationResult) -> str:
        """Generate a human-readable data quality report"""
        report_lines = []
        
        report_lines.append(f"📊 数据质量报告: {book_info.title}")
        report_lines.append(f"作者: {book_info.author}")
        report_lines.append(f"验证状态: {'✅ 已验证' if verification_result.is_verified else '❌ 未验证'}")
        report_lines.append(f"置信度: {verification_result.confidence_score:.2%}")
        report_lines.append("")
        
        # Data completeness
        total_fields = len(self.essential_fields) + len(self.recommended_fields)
        available_fields = total_fields - len(verification_result.missing_fields)
        completeness = available_fields / total_fields
        
        report_lines.append(f"📋 数据完整性: {completeness:.2%} ({available_fields}/{total_fields} 字段)")
        
        if verification_result.missing_fields:
            report_lines.append("缺失字段:")
            for field in verification_result.missing_fields:
                report_lines.append(f"  • {field}")
        
        # Available data summary
        report_lines.append("")
        report_lines.append("📚 可用数据:")
        report_lines.append(f"  • 描述长度: {len(book_info.description) if book_info.description else 0} 字符")
        report_lines.append(f"  • 类型标签: {len(book_info.genres)} 个")
        report_lines.append(f"  • 主题标签: {len(book_info.themes)} 个")
        report_lines.append(f"  • 评论数量: {len(book_info.reviews)} 条")
        
        if book_info.average_rating:
            report_lines.append(f"  • 平均评分: {book_info.average_rating:.1f}")
        
        # Verification notes
        if verification_result.verification_notes:
            report_lines.append("")
            report_lines.append("⚠️ 验证注意事项:")
            for note in verification_result.verification_notes:
                report_lines.append(f"  • {note}")
        
        return "\n".join(report_lines)