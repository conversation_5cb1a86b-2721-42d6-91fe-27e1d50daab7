import requests
import json
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup
import re
from urllib.parse import quote, urljoin
import logging

@dataclass
class BookInfo:
    """Comprehensive book information structure"""
    title: str
    author: str
    isbn: Optional[str] = None
    isbn13: Optional[str] = None
    publication_year: Optional[int] = None
    publisher: Optional[str] = None
    page_count: Optional[int] = None
    language: str = "zh"
    genres: List[str] = None
    description: Optional[str] = None
    summary: Optional[str] = None
    average_rating: Optional[float] = None
    ratings_count: Optional[int] = None
    goodreads_url: Optional[str] = None
    douban_url: Optional[str] = None
    amazon_url: Optional[str] = None
    reviews: List[Dict] = None
    awards: List[str] = None
    series: Optional[str] = None
    themes: List[str] = None
    
    def __post_init__(self):
        if self.genres is None:
            self.genres = []
        if self.reviews is None:
            self.reviews = []
        if self.awards is None:
            self.awards = []
        if self.themes is None:
            self.themes = []

class BookInfoCrawler:
    """Comprehensive book information crawler from multiple sources"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds
    
    def _rate_limit(self):
        """Implement rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        self.last_request_time = time.time()
    
    def search_douban(self, title: str, author: str) -> Optional[Dict]:
        """Search Douban for book information"""
        try:
            self._rate_limit()
            
            # Search URL
            search_query = f"{title} {author}".strip()
            search_url = f"https://www.douban.com/search?q={quote(search_query)}&cat=1001"
            
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find book results
            results = soup.find_all('div', class_='result')
            
            for result in results[:3]:  # Check top 3 results
                try:
                    title_link = result.find('h3').find('a')
                    if not title_link:
                        continue
                    
                    book_url = title_link.get('href')
                    if '/book/' not in book_url:
                        continue
                    
                    # Get detailed book info
                    book_info = self._get_douban_book_details(book_url)
                    if book_info and self._is_matching_book(book_info, title, author):
                        return book_info
                        
                except Exception as e:
                    self.logger.warning(f"Error processing Douban result: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Douban search error: {e}")
            return None
    
    def _get_douban_book_details(self, book_url: str) -> Optional[Dict]:
        """Get detailed book information from Douban book page"""
        try:
            self._rate_limit()
            response = self.session.get(book_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            book_info = {
                'source': 'douban',
                'url': book_url
            }
            
            # Title
            title_elem = soup.find('h1')
            if title_elem:
                book_info['title'] = title_elem.get_text().strip()
            
            # Basic info
            info_elem = soup.find('div', id='info')
            if info_elem:
                info_text = info_elem.get_text()
                
                # Author
                author_match = re.search(r'作者:\s*(.+?)(?:\n|$)', info_text)
                if author_match:
                    book_info['author'] = author_match.group(1).strip()
                
                # Publisher
                publisher_match = re.search(r'出版社:\s*(.+?)(?:\n|$)', info_text)
                if publisher_match:
                    book_info['publisher'] = publisher_match.group(1).strip()
                
                # Publication year
                year_match = re.search(r'出版年:\s*(.+?)(?:\n|$)', info_text)
                if year_match:
                    year_str = year_match.group(1).strip()
                    year_num = re.search(r'(\d{4})', year_str)
                    if year_num:
                        book_info['publication_year'] = int(year_num.group(1))
                
                # Pages
                pages_match = re.search(r'页数:\s*(\d+)', info_text)
                if pages_match:
                    book_info['page_count'] = int(pages_match.group(1))
                
                # ISBN
                isbn_match = re.search(r'ISBN:\s*(.+?)(?:\n|$)', info_text)
                if isbn_match:
                    isbn = isbn_match.group(1).strip()
                    if len(isbn) == 13:
                        book_info['isbn13'] = isbn
                    else:
                        book_info['isbn'] = isbn
            
            # Rating
            rating_elem = soup.find('strong', class_='ll rating_num')
            if rating_elem:
                try:
                    book_info['average_rating'] = float(rating_elem.get_text().strip())
                except ValueError:
                    pass
            
            # Ratings count
            rating_count_elem = soup.find('span', property='v:votes')
            if rating_count_elem:
                try:
                    book_info['ratings_count'] = int(rating_count_elem.get_text().strip())
                except ValueError:
                    pass
            
            # Description/Summary
            intro_elem = soup.find('div', class_='intro')
            if intro_elem:
                description = intro_elem.get_text().strip()
                book_info['description'] = description
            
            # Tags/Genres
            tags = []
            tag_elems = soup.find_all('a', href=re.compile(r'/tag/'))
            for tag_elem in tag_elems[:10]:  # Limit to 10 tags
                tag_text = tag_elem.get_text().strip()
                if tag_text and len(tag_text) < 20:  # Reasonable tag length
                    tags.append(tag_text)
            book_info['genres'] = tags
            
            # Reviews (sample)
            reviews = []
            review_elems = soup.find_all('div', class_='review-short')
            for review_elem in review_elems[:5]:  # Get top 5 reviews
                try:
                    review_text_elem = review_elem.find('div', class_='short-content')
                    if review_text_elem:
                        review_text = review_text_elem.get_text().strip()
                        if len(review_text) > 50:  # Meaningful review
                            reviews.append({
                                'text': review_text[:500],  # Limit length
                                'source': 'douban'
                            })
                except Exception:
                    continue
            book_info['reviews'] = reviews
            
            return book_info
            
        except Exception as e:
            self.logger.error(f"Error getting Douban book details: {e}")
            return None
    
    def search_google_books(self, title: str, author: str) -> Optional[Dict]:
        """Search Google Books API for book information"""
        try:
            self._rate_limit()
            
            # Construct search query
            query = f'intitle:"{title}" inauthor:"{author}"'
            api_url = f"https://www.googleapis.com/books/v1/volumes?q={quote(query)}&maxResults=5"
            
            response = self.session.get(api_url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'items' not in data:
                return None
            
            # Process each book result
            for item in data['items']:
                try:
                    volume_info = item.get('volumeInfo', {})
                    
                    book_info = {
                        'source': 'google_books',
                        'title': volume_info.get('title', ''),
                        'author': ', '.join(volume_info.get('authors', [])),
                        'publisher': volume_info.get('publisher'),
                        'publication_year': None,
                        'page_count': volume_info.get('pageCount'),
                        'language': volume_info.get('language', 'zh'),
                        'description': volume_info.get('description'),
                        'average_rating': volume_info.get('averageRating'),
                        'ratings_count': volume_info.get('ratingsCount'),
                        'genres': volume_info.get('categories', [])
                    }
                    
                    # Parse publication date
                    pub_date = volume_info.get('publishedDate')
                    if pub_date:
                        year_match = re.search(r'(\d{4})', pub_date)
                        if year_match:
                            book_info['publication_year'] = int(year_match.group(1))
                    
                    # ISBN
                    identifiers = volume_info.get('industryIdentifiers', [])
                    for identifier in identifiers:
                        if identifier.get('type') == 'ISBN_13':
                            book_info['isbn13'] = identifier.get('identifier')
                        elif identifier.get('type') == 'ISBN_10':
                            book_info['isbn'] = identifier.get('identifier')
                    
                    if self._is_matching_book(book_info, title, author):
                        return book_info
                        
                except Exception as e:
                    self.logger.warning(f"Error processing Google Books result: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Google Books search error: {e}")
            return None
    
    def search_open_library(self, title: str, author: str) -> Optional[Dict]:
        """Search Open Library for book information"""
        try:
            self._rate_limit()
            
            # Search API
            search_query = f"{title} {author}".strip()
            api_url = f"https://openlibrary.org/search.json?title={quote(title)}&author={quote(author)}&limit=5"
            
            response = self.session.get(api_url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'docs' not in data:
                return None
            
            for doc in data['docs']:
                try:
                    book_info = {
                        'source': 'open_library',
                        'title': doc.get('title', ''),
                        'author': ', '.join(doc.get('author_name', [])),
                        'publisher': ', '.join(doc.get('publisher', [])) if doc.get('publisher') else None,
                        'publication_year': doc.get('first_publish_year'),
                        'page_count': doc.get('number_of_pages_median'),
                        'language': doc.get('language', ['zh'])[0] if doc.get('language') else 'zh',
                        'genres': doc.get('subject', [])[:10],  # Limit subjects
                        'average_rating': doc.get('ratings_average'),
                        'ratings_count': doc.get('ratings_count')
                    }
                    
                    # ISBN
                    if doc.get('isbn'):
                        for isbn in doc['isbn']:
                            if len(isbn) == 13:
                                book_info['isbn13'] = isbn
                                break
                            elif len(isbn) == 10:
                                book_info['isbn'] = isbn
                    
                    if self._is_matching_book(book_info, title, author):
                        return book_info
                        
                except Exception as e:
                    self.logger.warning(f"Error processing Open Library result: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Open Library search error: {e}")
            return None
    
    def _is_matching_book(self, book_info: Dict, target_title: str, target_author: str) -> bool:
        """Check if the found book matches the target book"""
        if not book_info.get('title') or not book_info.get('author'):
            return False
        
        # Normalize strings for comparison
        def normalize(text: str) -> str:
            return re.sub(r'[^\w\s]', '', text.lower()).strip()
        
        book_title = normalize(book_info['title'])
        book_author = normalize(book_info['author'])
        target_title_norm = normalize(target_title)
        target_author_norm = normalize(target_author)
        
        # Check title similarity (allow partial matches)
        title_match = (
            target_title_norm in book_title or 
            book_title in target_title_norm or
            self._similarity_score(book_title, target_title_norm) > 0.8
        )
        
        # Check author similarity
        author_match = (
            target_author_norm in book_author or 
            book_author in target_author_norm or
            self._similarity_score(book_author, target_author_norm) > 0.8
        )
        
        return title_match and author_match
    
    def _similarity_score(self, s1: str, s2: str) -> float:
        """Calculate similarity score between two strings"""
        if not s1 or not s2:
            return 0.0
        
        # Simple character-based similarity
        s1_chars = set(s1)
        s2_chars = set(s2)
        intersection = len(s1_chars.intersection(s2_chars))
        union = len(s1_chars.union(s2_chars))
        
        return intersection / union if union > 0 else 0.0
    
    def gather_comprehensive_info(self, title: str, author: str) -> BookInfo:
        """Gather book information from all sources and consolidate"""
        print(f"🔍 Gathering comprehensive information for '{title}' by {author}...")
        
        # Search all sources
        sources_data = {}
        
        # Douban (Chinese books focus)
        print("📚 Searching Douban...")
        douban_data = self.search_douban(title, author)
        if douban_data:
            sources_data['douban'] = douban_data
            print(f"✅ Found on Douban: {douban_data.get('title', 'N/A')}")
        
        # Google Books
        print("🌐 Searching Google Books...")
        google_data = self.search_google_books(title, author)
        if google_data:
            sources_data['google_books'] = google_data
            print(f"✅ Found on Google Books: {google_data.get('title', 'N/A')}")
        
        # Open Library
        print("📖 Searching Open Library...")
        openlibrary_data = self.search_open_library(title, author)
        if openlibrary_data:
            sources_data['open_library'] = openlibrary_data
            print(f"✅ Found on Open Library: {openlibrary_data.get('title', 'N/A')}")
        
        # Consolidate data
        consolidated_info = self._consolidate_book_data(sources_data, title, author)
        
        print(f"📋 Consolidated {len(sources_data)} sources of data")
        return consolidated_info
    
    def _consolidate_book_data(self, sources_data: Dict, original_title: str, original_author: str) -> BookInfo:
        """Consolidate book data from multiple sources"""
        
        # Start with original input
        consolidated = BookInfo(
            title=original_title,
            author=original_author
        )
        
        if not sources_data:
            return consolidated
        
        # Priority order: Douban (Chinese focus) > Google Books > Open Library
        priority_sources = ['douban', 'google_books', 'open_library']
        
        all_reviews = []
        all_genres = set()
        all_themes = set()
        ratings = []
        
        for source in priority_sources:
            if source not in sources_data:
                continue
                
            data = sources_data[source]
            
            # Use first valid value for each field
            if not consolidated.isbn and data.get('isbn'):
                consolidated.isbn = data['isbn']
            if not consolidated.isbn13 and data.get('isbn13'):
                consolidated.isbn13 = data['isbn13']
            if not consolidated.publication_year and data.get('publication_year'):
                consolidated.publication_year = data['publication_year']
            if not consolidated.publisher and data.get('publisher'):
                consolidated.publisher = data['publisher']
            if not consolidated.page_count and data.get('page_count'):
                consolidated.page_count = data['page_count']
            if not consolidated.description and data.get('description'):
                consolidated.description = data['description']
            
            # Collect reviews
            if data.get('reviews'):
                all_reviews.extend(data['reviews'])
            
            # Collect genres/themes
            if data.get('genres'):
                all_genres.update(data['genres'])
            
            # Collect ratings
            if data.get('average_rating'):
                ratings.append(data['average_rating'])
            
            # Set source URLs
            if source == 'douban' and data.get('url'):
                consolidated.douban_url = data['url']
            elif source == 'google_books' and data.get('url'):
                consolidated.amazon_url = data['url']  # Or keep as google books url
        
        # Calculate average rating
        if ratings:
            consolidated.average_rating = sum(ratings) / len(ratings)
        
        # Set consolidated lists
        consolidated.reviews = all_reviews[:10]  # Limit to 10 reviews
        consolidated.genres = list(all_genres)[:15]  # Limit to 15 genres
        consolidated.themes = self._extract_themes(consolidated.description, consolidated.genres)
        
        return consolidated
    
    def _extract_themes(self, description: str, genres: List[str]) -> List[str]:
        """Extract main themes from description and genres"""
        themes = set()
        
        # Common themes mapping
        theme_keywords = {
            '爱情': ['爱情', '恋爱', '浪漫', '情感', '爱人'],
            '成长': ['成长', '青春', '童年', '青年', '少年'],
            '家庭': ['家庭', '亲情', '父母', '母亲', '父亲', '兄弟', '姐妹'],
            '友谊': ['友谊', '朋友', '友情', '伙伴'],
            '战争': ['战争', '战斗', '军事', '士兵', '革命'],
            '历史': ['历史', '古代', '传统', '文化', '朝代'],
            '科幻': ['科幻', '未来', '科技', '太空', '机器人'],
            '悬疑': ['悬疑', '推理', '犯罪', '侦探', '谋杀'],
            '哲学': ['哲学', '思考', '人生', '存在', '意义'],
            '社会': ['社会', '政治', '经济', '现实', '体制']
        }
        
        # Check description
        if description:
            desc_lower = description.lower()
            for theme, keywords in theme_keywords.items():
                if any(keyword in desc_lower for keyword in keywords):
                    themes.add(theme)
        
        # Check genres
        for genre in genres:
            genre_lower = genre.lower()
            for theme, keywords in theme_keywords.items():
                if any(keyword in genre_lower for keyword in keywords):
                    themes.add(theme)
        
        return list(themes)[:8]  # Limit to 8 themes