#!/usr/bin/env python3
"""
Test script to verify all 4 fixes are working correctly:
1. Subtitle-audio alignment
2. Subtitle line wrapping for different aspect ratios
3. Theme-related background images based on book title
4. Animation effects for background transitions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api import app
import requests
import json
import time

def test_little_prince_video():
    """Test with 小王子 (Little Prince) to verify theme-based backgrounds"""
    
    config = {
        "book_review": {
            "title": "《小王子》- 安托万·德·圣埃克苏佩里",
            "author": "安托万·德·圣埃克苏佩里",
            "content": """《小王子》是一部充满哲学意味的童话故事。小王子来自一个小小的星球，那里只有他一个人，还有一朵玫瑰花。

他离开自己的星球，开始了宇宙旅行。在旅途中，他遇到了各种各样的大人：国王、虚荣的人、酒鬼、商人、地理学家。每个人都让他感到困惑和失望。

最后，小王子来到了地球。在沙漠中，他遇到了一只狐狸。狐狸告诉他："只有用心才能看得清楚，真正重要的东西用眼睛是看不见的。"

这本书告诉我们，保持童心是多么重要。大人们总是忙于数字和事实，却忘记了生活中真正美好的东西：友谊、爱情和想象力。"""
        },
        "style": "gentle",
        "voice": "zh_female_roumeinvyou_emo_v2_mars_bigtts",
        "bgm_path": "./assets/bgm/gentle_bgm.mp3",
        "enable_dynamic_backgrounds": True,
        "transition_interval": [8, 12]  # Shorter for testing
    }
    
    print("🌟 Testing The Little Prince video generation...")
    print("This will test:")
    print("1. ✅ Subtitle-audio alignment (no gaps between segments)")
    print("2. ✅ Subtitle wrapping (max 12 chars for vertical video)")
    print("3. ✅ Little Prince themed backgrounds (stars, planets, roses, fox)")
    print("4. ✅ Smooth animation transitions every 8-12 seconds")
    
    return test_video_generation(config, "Little Prince")

def test_living_video():
    """Test with 活着 to verify Chinese rural themed backgrounds"""
    
    config = {
        "book_review": {
            "title": "《活着》- 余华",
            "author": "余华",
            "content": """《活着》讲述了福贵的人生故事。他从一个富家少爷，变成了一个贫苦农民。

在那个动荡的年代，福贵失去了所有的亲人。儿子有庆为了救县长夫人献血而死，女儿凤霞难产而死，妻子家珍病死，女婿二喜被石板压死，外孙苦根吃豆子撑死。

最后，只剩下福贵和一头老牛相依为命。他们在田野里耕作，在夕阳下回家。

这是一个关于苦难和坚韧的故事。余华用朴实的语言，展现了中国农民的生存智慧。活着本身就是希望。"""
        },
        "style": "emotional",
        "voice": "zh_male_beijingxiaoye_emo_v2_mars_bigtts",
        "bgm_path": "./assets/bgm/peaceful_bgm.mp3",
        "enable_dynamic_backgrounds": True,
        "transition_interval": [10, 15]
    }
    
    print("🌾 Testing 《活着》video generation...")
    print("This will test:")
    print("1. ✅ Chinese rural landscapes and historical settings")
    print("2. ✅ Emotional voice synthesis")
    print("3. ✅ Background transitions with rural themes")
    
    return test_video_generation(config, "活着")

def test_video_generation(config, title):
    """Generate a test video and check results"""
    
    url = "http://localhost:8888/api/generate"
    
    try:
        print(f"\n🚀 Sending generation request for {title}...")
        response = requests.post(url, json=config)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ Task created: {task_id}")
            
            # Poll for completion
            status_url = f"http://localhost:8888/api/status/{task_id}"
            while True:
                time.sleep(3)
                status_response = requests.get(status_url)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    current_status = status_data.get('status')
                    progress = status_data.get('progress', 0)
                    current_step = status_data.get('current_step', '')
                    
                    print(f"\r📊 Status: {current_status} | Progress: {progress}% | Step: {current_step}", end='', flush=True)
                    
                    if current_status == 'completed':
                        print(f"\n\n🎉 {title} video completed!")
                        
                        # Check results
                        print("\n📋 Checking fixes:")
                        
                        # Check metadata
                        metadata = status_data.get('metadata', {})
                        
                        # 1. Check subtitle alignment
                        alignment_data = metadata.get('alignment_data', [])
                        if alignment_data:
                            print("✅ 1. Subtitle-audio alignment: Found alignment data")
                            # Check for gaps
                            gaps = []
                            for i in range(1, len(alignment_data)):
                                gap = alignment_data[i]['start'] - alignment_data[i-1]['end']
                                if gap > 100:  # More than 100ms gap
                                    gaps.append(gap)
                            if gaps:
                                print(f"⚠️  Found {len(gaps)} gaps > 100ms: {gaps[:3]}...")
                            else:
                                print("✅ No significant gaps found")
                        
                        # 2. Check subtitle length
                        if alignment_data:
                            long_subtitles = [seg for seg in alignment_data if len(seg.get('text', '')) > 12]
                            if long_subtitles:
                                print(f"⚠️  2. Found {len(long_subtitles)} subtitles > 12 chars")
                            else:
                                print("✅ 2. All subtitles ≤ 12 characters")
                        
                        # 3. Check dynamic backgrounds
                        backgrounds = metadata.get('dynamic_backgrounds', [])
                        if backgrounds:
                            print(f"✅ 3. Generated {len(backgrounds)} dynamic backgrounds")
                            for bg in backgrounds[:3]:
                                print(f"     - {bg.get('prompt', 'No prompt')[:60]}...")
                        else:
                            print("❌ 3. No dynamic backgrounds found")
                        
                        # 4. Check transitions
                        transitions = [bg for bg in backgrounds if bg.get('transition_type') != 'none']
                        if transitions:
                            transition_types = set(bg.get('transition_type') for bg in transitions)
                            print(f"✅ 4. Found transitions: {', '.join(transition_types)}")
                        else:
                            print("⚠️  4. No animated transitions found")
                        
                        print(f"\n📁 Video saved: {status_data.get('output_path', 'Unknown')}")
                        return True
                        
                    elif current_status == 'failed':
                        print(f"\n\n❌ {title} generation failed: {status_data.get('error', 'Unknown error')}")
                        return False
                else:
                    print(f"\n❌ Error checking status: {status_response.status_code}")
                    return False
        else:
            print(f"❌ Error starting generation: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

def main():
    print("=== Testing All 4 Fixes ===")
    print("Make sure the server is running on port 8888!")
    print("-" * 50)
    
    # Test different scenarios
    tests = [
        ("Little Prince Theme Test", test_little_prince_video),
        ("Living (活着) Theme Test", test_living_video)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results[test_name] = success
        except KeyboardInterrupt:
            print(f"\n⏹️  Test interrupted by user")
            break
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 All fixes are working correctly!")
    else:
        print("⚠️  Some issues need attention.")

if __name__ == "__main__":
    main()