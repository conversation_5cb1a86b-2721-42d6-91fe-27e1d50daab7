#!/usr/bin/env python3
"""
直接启动并运行深度研究系统
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

print('🎯 深度研究系统 - 直接启动')
print('=' * 40)

# 导入组件
from universal_research_template import (
    UniversalResearchPromptTemplate, 
    ResearchRequest, 
    ResearchScenario
)

# 创建模板系统
template_system = UniversalResearchPromptTemplate()

print('✅ 系统初始化完成')
print(f'📋 支持场景: {len(template_system.list_available_scenarios())}个')

# 演示您的原始书籍分析场景
print('\n📖 演示书籍分析 (您的原始设计):')
book_request = ResearchRequest(
    scenario=ResearchScenario.BOOK_ANALYSIS,
    target="Structures: Or Why Things Don't Fall Down by J.<PERSON><PERSON> Gordon",
    audience='general'
)

book_prompt = template_system.generate_research_prompt(book_request)
print(f'目标: {book_request.target}')
print(f'提示词长度: {len(book_prompt)}字符')
print('\n🎯 生成的完整提示词:')
print('=' * 60)
print(book_prompt)
print('=' * 60)

print('\n📊 演示行业报告场景:')
industry_request = ResearchRequest(
    scenario=ResearchScenario.INDUSTRY_REPORT,
    target="人工智能",
    audience='business'
)

industry_prompt = template_system.generate_research_prompt(industry_request)
print(f'目标: {industry_request.target}')
print(f'提示词长度: {len(industry_prompt)}字符')
print('\n前300字符预览:')
print(industry_prompt[:300] + '...')

print('\n🚀 系统已成功运行！')
print('✅ 您的深度研究Agent现在完全独立可用')