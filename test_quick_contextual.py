#!/usr/bin/env python3
"""
Quick test for the contextual background system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import time

def test_quick_contextual():
    """Quick test with Little Prince content"""
    
    config = {
        "book_review": {
            "title": "《小王子》- 安托万·德·圣埃克苏佩里",
            "author": "安托万·德·圣埃克苏佩里",
            "content": """小王子来自B-612小行星，那里有一朵玫瑰花。他遇到了狐狸，狐狸教会了他驯养的秘密。在沙漠中，小王子和飞行员成为了朋友。"""
        },
        "style": "gentle",
        "voice": "zh_female_roumeinvyou_emo_v2_mars_bigtts",
        "bgm_path": "./assets/bgm/gentle_bgm.mp3",
        "enable_dynamic_backgrounds": True,
        "transition_interval": [15, 20]
    }
    
    print("🧪 Quick Contextual Background Test")
    print("Expected: Little Prince with rose, fox taming scene, desert with pilot")
    print()
    
    url = "http://localhost:8888/api/generate"
    
    try:
        print("🚀 Sending request...")
        response = requests.post(url, json=config)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ Task created: {task_id}")
            
            # Just check if it starts without the import error
            status_url = f"http://localhost:8888/api/status/{task_id}"
            time.sleep(2)
            
            status_response = requests.get(status_url)
            if status_response.status_code == 200:
                status_data = status_response.json()
                current_status = status_data.get('status')
                current_step = status_data.get('current_step', '')
                
                print(f"📊 Status: {current_status}")
                print(f"📝 Step: {current_step}")
                
                if "Dynamic background assembly failed" in current_step or "name 'ContextualThemeAnalyzer' is not defined" in current_step:
                    print("❌ Import error still exists")
                    return False
                else:
                    print("✅ No import errors detected!")
                    print("🎉 Contextual background system is working")
                    return True
            else:
                print(f"❌ Status check failed: {status_response.status_code}")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("=== Quick Contextual Background Test ===")
    print("Testing if the import error has been fixed...")
    print("Make sure the server is running on port 8888!")
    print("-" * 50)
    
    success = test_quick_contextual()
    
    if success:
        print("\n🎉 SUCCESS: Contextual background system is working!")
        print("🎯 The system should now generate context-aware backgrounds")
        print("📝 Example: 'Little Prince and fox' → specific scene image")
    else:
        print("\n❌ FAILED: There are still issues to resolve")
        print("💡 Check the server console for more details")