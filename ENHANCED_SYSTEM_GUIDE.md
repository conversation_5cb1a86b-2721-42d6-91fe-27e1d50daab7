# 增强版多智能体协作系统使用指南

## 🎯 系统概述

增强版多智能体协作系统是一个完整的AI工作流管理平台，集成了8个专业智能体，支持复杂任务的自动化处理和协作执行。

## 🚀 快速启动

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量（可选）
export DOUBAO_APP_ID="your_app_id"
export DOUBAO_ACCESS_TOKEN="your_token"
export OPENROUTER_API_KEY="your_key"
```

### 2. 启动系统
```bash
python start_enhanced_system.py
```

### 3. 访问界面
- 主界面: http://localhost:8081
- 基础版: http://localhost:8081/basic
- API文档: http://localhost:8081/api/agents

## 🤖 智能体功能详解

### 1. Audio Agent (音频智能体)
**功能**：
- 多情感语音合成
- 音频时间轴管理
- 声音一致性控制
- 背景音乐集成

**API端点**：
- `GET /api/agents/audio/config` - 获取配置
- `POST /api/agents/audio/execute` - 执行任务

### 2. Image Context Agent (图像上下文智能体)
**功能**：
- 上下文图像生成
- 动态背景序列
- 视觉叙事规划
- 图像转场效果

**配置参数**：
```json
{
  "image_style": "professional",
  "transition_duration": 2.0,
  "background_count": 5
}
```

### 3. MCP Agent (多提供商管理智能体)
**功能**：
- 多AI提供商管理
- 智能路由选择
- 成本优化
- 故障回退机制

**支持的提供商类型**：
- text_generation (文本生成)
- image_generation (图像生成)
- speech_synthesis (语音合成)
- embedding (向量嵌入)
- translation (翻译)

### 4. Search Agent (搜索智能体)
**功能**：
- 概念语义扩展
- 智能搜索增强
- 用户意图推理
- 相关主题发现

### 5. Video Agent (视频智能体)
**功能**：
- 专业视频组装
- 动态效果处理
- 多格式输出
- 质量报告生成

### 6. Workflow Agent (工作流智能体)
**功能**：
- 工作流定义和执行
- 智能体依赖管理
- 错误处理和重试
- 进度监控

### 7. HITL Agent (人机交互智能体)
**功能**：
- 人机交互管理
- 用户选择收集
- 偏好学习
- 决策支持

### 8. Scripting Agent (脚本智能体)
**功能**：
- 多风格脚本生成
- 多语言支持
- 受众适配
- 内容结构优化

## 🔄 工作流管理

### 预定义工作流模板

1. **book_review_video** - 书评视频生成
2. **podcast_production** - 播客制作
3. **content_analysis** - 内容分析
4. **multi_modal_content** - 多模态内容生成

### 自定义工作流

通过Web界面的工作流编排器，您可以：
- 拖拽智能体创建工作流
- 设置智能体间的依赖关系
- 配置输入输出映射
- 设置重试和超时策略

### 工作流执行

```javascript
// 执行预定义工作流
const response = await fetch('/api/workflow/execute', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        workflow_name: 'book_review_video',
        initial_data: {
            user_input: '分析《小王子》这本书',
            target_audience: 'general'
        }
    })
});
```

## ☁️ 提供商管理

### 添加新提供商

通过Web界面或API添加AI提供商：

```javascript
const providerConfig = {
    provider_id: 'custom_llm',
    provider_name: 'Custom LLM Service',
    provider_type: 'text_generation',
    api_url: 'https://api.example.com/v1/chat',
    api_key: 'your_api_key',
    model_name: 'custom-model-v1',
    max_requests_per_minute: 60,
    cost_per_request: 0.01
};

fetch('/api/mcp/providers', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify(providerConfig)
});
```

### 提供商回退机制

系统支持智能回退：
- 主提供商失败时自动切换到备用提供商
- 基于成本和性能的智能路由
- 实时监控和故障检测

## 📊 监控和分析

### 实时监控面板

- 活跃智能体数量
- 运行中的任务
- 完成任务统计
- 系统错误率

### 性能指标

- 智能体响应时间
- 任务成功率
- 资源使用情况
- 成本分析

## 🔧 配置管理

### 智能体配置

每个智能体都支持动态配置：

```json
{
  "timeout_seconds": 300,
  "retry_count": 3,
  "priority": "MEDIUM",
  "custom_parameters": {
    "model_temperature": 0.7,
    "max_tokens": 2000
  }
}
```

### 系统配置

在 `enhanced_app.py` 中修改全局配置：

```python
# 智能体管理器配置
agent_manager = AgentManager()

# 任务超时设置
TASK_TIMEOUT = 600  # 10分钟

# 监控间隔
MONITORING_INTERVAL = 5  # 5秒
```

## 🛠️ 开发和扩展

### 添加新智能体

1. 继承 `BaseAgent` 类
2. 实现必需的抽象方法
3. 在 `AgentManager` 中注册
4. 更新Web界面

```python
class CustomAgent(BaseAgent):
    @property
    def agent_type(self) -> str:
        return "custom"
    
    @property
    def required_inputs(self) -> List[str]:
        return ["input_data"]
    
    @property
    def output_schema(self) -> Dict[str, type]:
        return {"result": dict}
    
    async def execute(self, task: AgentTask) -> AgentResult:
        # 实现智能体逻辑
        pass
```

### API扩展

添加新的API端点：

```python
@app.route('/api/custom/endpoint', methods=['POST'])
def custom_endpoint():
    try:
        # 处理逻辑
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
```

## 🔒 安全和最佳实践

### API密钥管理
- 使用环境变量存储敏感信息
- 定期轮换API密钥
- 监控API使用情况

### 错误处理
- 所有智能体都有完善的错误处理
- 支持自动重试和回退
- 详细的错误日志记录

### 性能优化
- 异步处理提高并发性能
- 智能缓存减少重复计算
- 资源池管理优化内存使用

## 📞 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看系统日志：`logs/` 目录
2. 检查智能体状态：监控面板
3. 查看API响应：浏览器开发者工具
4. 提交问题报告：包含详细的错误信息和复现步骤

---

**版本**: v2.0.0  
**更新日期**: 2024-07-19  
**维护者**: Enhanced AI System Team
