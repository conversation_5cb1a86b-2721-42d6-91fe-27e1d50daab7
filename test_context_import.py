#!/usr/bin/env python3
"""
Quick test to verify the contextual background system imports and works correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all imports work correctly"""
    print("🔍 Testing imports...")
    
    try:
        from src.context_analyzer import ContextualThemeAnalyzer, IntelligentBackgroundPlanner
        print("✅ ContextualThemeAnalyzer and IntelligentBackgroundPlanner imported successfully")
        
        from src.dynamic_background_generator import DynamicBackgroundGenerator
        print("✅ DynamicBackgroundGenerator imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_contextual_analyzer():
    """Test the contextual analyzer functionality"""
    print("\n🧠 Testing contextual analyzer...")
    
    try:
        from src.context_analyzer import ContextualThemeAnalyzer
        
        analyzer = ContextualThemeAnalyzer()
        
        # Test Little Prince context
        test_text = "小王子遇到了狐狸，狐狸教会了小王子什么是驯养。在沙漠中，他们建立了深厚的友谊。"
        book_title = "《小王子》"
        
        result = analyzer.analyze_content_context(test_text, book_title)
        
        print(f"📝 Test text: {test_text}")
        print(f"📚 Book: {book_title}")
        print(f"🎬 Generated scene: {result['scene_description']}")
        print(f"🎭 Characters: {[c['name'] for c in result.get('characters', [])]}")
        print(f"📍 Locations: {[l['name'] for l in result.get('locations', [])]}")
        print(f"🎯 Importance: {result.get('importance', 0):.2f}")
        
        return True
    except Exception as e:
        print(f"❌ Contextual analyzer error: {e}")
        return False

def test_background_planner():
    """Test the intelligent background planner"""
    print("\n⏱️ Testing background planner...")
    
    try:
        from src.context_analyzer import IntelligentBackgroundPlanner
        
        planner = IntelligentBackgroundPlanner()
        
        # Create sample alignment data
        alignment_data = [
            {"text": "小王子来自B-612小行星", "start": 0, "end": 3000},
            {"text": "他遇到了一只狐狸", "start": 3000, "end": 6000},
            {"text": "狐狸教会了他驯养的含义", "start": 6000, "end": 9000},
            {"text": "最后小王子要回到自己的星球", "start": 9000, "end": 12000}
        ]
        
        book_metadata = {"title": "《小王子》", "author": "安托万·德·圣埃克苏佩里"}
        
        segments = planner.plan_contextual_backgrounds(alignment_data, book_metadata)
        
        print(f"📊 Generated {len(segments)} background segments:")
        for i, segment in enumerate(segments):
            print(f"  {i+1}. {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")
            print(f"     Importance: {segment['importance']:.2f}")
            print(f"     Characters: {[c['name'] for c in segment.get('characters', [])]}")
        
        return True
    except Exception as e:
        print(f"❌ Background planner error: {e}")
        return False

def test_dynamic_generator():
    """Test the dynamic background generator with contextual analysis"""
    print("\n🎨 Testing dynamic background generator...")
    
    try:
        from src.dynamic_background_generator import DynamicBackgroundGenerator
        
        book_metadata = {"title": "《小王子》", "author": "安托万·德·圣埃克苏佩里"}
        generator = DynamicBackgroundGenerator(book_metadata=book_metadata)
        
        # Test alignment data
        alignment_data = [
            {"text": "小王子和狐狸在沙漠中相遇", "start": 0, "end": 5000},
            {"text": "他们一起看日落", "start": 5000, "end": 10000}
        ]
        
        segments = generator.plan_background_sequence(alignment_data, 15.0)
        
        print(f"🎬 Generated {len(segments)} contextual segments:")
        for segment in segments:
            print(f"  📸 {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")
            print(f"     Prompt: {segment['image_prompt'][:80]}...")
        
        return True
    except Exception as e:
        print(f"❌ Dynamic generator error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== Testing Contextual Background System ===")
    
    tests = [
        ("Import Test", test_imports),
        ("Contextual Analyzer Test", test_contextual_analyzer),
        ("Background Planner Test", test_background_planner),
        ("Dynamic Generator Test", test_dynamic_generator)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print(f"{'='*50}")
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("\n🎉 All tests passed! Contextual background system is ready.")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()