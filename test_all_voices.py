#!/usr/bin/env python3
"""
Test script to discover all available Doubao TTS voices for current app_id
"""

import asyncio
from src.doubao_tts import DoubaoTTS
import time

# Comprehensive list of potential voice types based on documentation patterns
POTENTIAL_VOICES = [
    # Multi-emotion voices (多情感音色)
    "zh_male_beijingxiaoye_emo_v2_mars_bigtts",        # 北京小爷（多情感）
    "zh_female_roumeinvyou_emo_v2_mars_bigtts",        # 柔美女友（多情感）
    "zh_male_yangguangqingnian_emo_v2_mars_bigtts",    # 阳光青年（多情感）
    "zh_female_meilinvyou_emo_v2_mars_bigtts",         # 魅力女友（多情感）
    "zh_female_shuangkuaisisi_emo_v2_mars_bigtts",     # 爽快思思（多情感）
    
    # Regional/Accent voices (方言音色)
    "zh_male_jingqiangkanye_moon_bigtts",              # 京腔侃爷
    "zh_female_wanwanxiaohe_moon_bigtts",              # 湾湾小何
    "zh_male_guangzhoudege_moon_bigtts",               # 广州德哥
    "zh_female_daimengchuanmei_moon_bigtts",           # 呆萌川妹
    "zh_male_wanqudashu_moon_bigtts",                  # 湾区大叔
    "zh_male_haoyuxiaoge_moon_bigtts",                 # 浩宇小哥
    "zh_male_guangxiyuanzhou_moon_bigtts",             # 广西远舟
    "zh_female_meituojieer_moon_bigtts",               # 妹坨洁儿
    "zh_male_yuzhouzixuan_moon_bigtts",                # 豫州子轩
    
    # Role-play voices (角色音色)
    "zh_female_gaolengyujie_moon_bigtts",              # 高冷御姐
    "zh_male_aojiaobazong_moon_bigtts",                # 傲娇霸总
    "zh_male_shenyeboke_moon_bigtts",                  # 深夜播客
    "zh_male_naiqimengwa_mars_bigtts",                 # 奶气萌娃
    "zh_female_tianmeixiaoxi_mars_bigtts",             # 甜美小希
    "zh_male_wennuanxiaobai_mars_bigtts",              # 温暖小白
    
    # Professional voices (专业音色)
    "zh_female_tinalaoshi_mars_bigtts",                # Tina老师
    "zh_female_nuanyangnvsheng_mars_bigtts",           # 暖阳女声
    "zh_male_jieshuoxiaoming_mars_bigtts",             # 解说小明
    "zh_female_kehufuwu_mars_bigtts",                  # 客服小美
    "zh_male_xinwenboke_mars_bigtts",                  # 新闻播客
    
    # Character voices (角色音色)
    "zh_male_xionger_mars_bigtts",                     # 熊二
    "zh_female_peiqizhu_mars_bigtts",                  # 佩奇猪
    "zh_female_wuzetian_mars_bigtts",                  # 武则天
    "zh_male_zhubajie_mars_bigtts",                    # 猪八戒
    "zh_female_changee_mars_bigtts",                   # 嫦娥
    
    # Standard voices (标准音色)
    "zh_female_standard_mars_bigtts",                  # 标准女声
    "zh_male_standard_mars_bigtts",                    # 标准男声
    "zh_female_qingxin_mars_bigtts",                   # 清新女声
    "zh_male_chenwen_mars_bigtts",                     # 沉稳男声
    
    # Multilingual voices (多语言音色)
    "en_male_smith_mars_bigtts",                       # Smith (英语)
    "en_female_sara_mars_bigtts",                      # Sara (英语)
    "multi_male_international_mars_bigtts",            # 国际男声
    "multi_female_international_mars_bigtts",          # 国际女声
    
    # Additional potential voices based on patterns
    "zh_female_wenrou_mars_bigtts",                    # 温柔女声
    "zh_male_yanggang_mars_bigtts",                    # 阳刚男声
    "zh_female_huopo_mars_bigtts",                     # 活泼女声
    "zh_male_kenao_mars_bigtts",                       # 可爱男声
    "zh_female_zhiying_mars_bigtts",                   # 知性女声
    "zh_male_chengshu_mars_bigtts",                    # 成熟男声
    
    # Educational voices
    "zh_female_jiaoyu_mars_bigtts",                    # 教育女声
    "zh_male_jiaoyu_mars_bigtts",                      # 教育男声
    "zh_female_ertong_mars_bigtts",                    # 儿童女声
    "zh_male_ertong_mars_bigtts",                      # 儿童男声
    
    # Business voices
    "zh_female_shangwu_mars_bigtts",                   # 商务女声
    "zh_male_shangwu_mars_bigtts",                     # 商务男声
    "zh_female_zhuanye_mars_bigtts",                   # 专业女声
    "zh_male_zhuanye_mars_bigtts",                     # 专业男声
    
    # Entertainment voices
    "zh_female_yule_mars_bigtts",                      # 娱乐女声
    "zh_male_yule_mars_bigtts",                        # 娱乐男声
    "zh_female_youxi_mars_bigtts",                     # 游戏女声
    "zh_male_youxi_mars_bigtts",                       # 游戏男声
]

def test_voice_availability():
    """Test which voices are available for current app_id"""
    doubao = DoubaoTTS()
    
    if not doubao.is_configured:
        print("❌ Doubao TTS not configured")
        return
    
    print(f"🧪 Testing {len(POTENTIAL_VOICES)} potential voice types...")
    print("This may take several minutes...\n")
    
    working_voices = []
    failed_voices = []
    
    for i, voice_id in enumerate(POTENTIAL_VOICES, 1):
        print(f"[{i:2d}/{len(POTENTIAL_VOICES)}] Testing {voice_id}...", end=" ")
        
        try:
            # Test with short text to minimize usage
            result = doubao.synthesize_speech("测试", voice_id, "neutral")
            
            if result and len(result) > 0:
                print(f"✅ WORKING ({len(result)} bytes)")
                working_voices.append(voice_id)
            else:
                print("❌ No audio")
                failed_voices.append(voice_id)
                
        except Exception as e:
            if "resource not granted" in str(e).lower():
                print("❌ No permission")
            else:
                print(f"❌ Error: {e}")
            failed_voices.append(voice_id)
        
        # Small delay to avoid rate limiting
        time.sleep(0.5)
    
    print(f"\n🎉 DISCOVERY COMPLETE!")
    print(f"✅ Working voices: {len(working_voices)}")
    print(f"❌ Failed voices: {len(failed_voices)}")
    
    if working_voices:
        print(f"\n🎤 AVAILABLE VOICES ({len(working_voices)}):")
        for voice in working_voices:
            print(f"  - {voice}")
    
    return working_voices

if __name__ == "__main__":
    working_voices = test_voice_availability()
    
    if working_voices:
        print(f"\n📝 Copy these {len(working_voices)} working voices to your configuration:")
        print("working_voices = [")
        for voice in working_voices:
            print(f'    "{voice}",')
        print("]")