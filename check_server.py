#!/usr/bin/env python3
"""
检查服务器状态并提供访问信息
"""

import socket
import requests
import subprocess
import time

def check_port(port):
    """检查端口是否可用"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', port))
    sock.close()
    return result == 0

def test_api(port):
    """测试API是否响应"""
    try:
        response = requests.get(f"http://localhost:{port}/api/test", timeout=3)
        return response.status_code == 200
    except:
        return False

print("🔍 检查深度研究系统状态...")
print("=" * 50)

# 检查可能的端口
ports_to_check = [5001, 5002, 5000, 8080]
running_servers = []

for port in ports_to_check:
    if check_port(port):
        print(f"✅ 端口 {port}: 有服务运行")
        if test_api(port):
            print(f"   🎯 API响应正常: http://localhost:{port}")
            running_servers.append(port)
        else:
            print(f"   ⚠️  端口占用但非研究系统API")
    else:
        print(f"❌ 端口 {port}: 无服务")

print("\n" + "=" * 50)

if running_servers:
    print("🎉 发现运行中的深度研究系统!")
    for port in running_servers:
        print(f"\n🌐 访问地址: http://localhost:{port}")
        print(f"📖 API文档: http://localhost:{port}/api/examples") 
        print(f"🧪 健康检查: http://localhost:{port}/api/test")
        print(f"📋 场景列表: http://localhost:{port}/api/scenarios")
        print(f"🔧 生成提示词: http://localhost:{port}/api/generate")
else:
    print("❌ 未发现运行中的API服务器")
    print("\n🚀 启动服务器的方法:")
    print("1. python3 start_new_server.py")
    print("2. python3 launch.py") 
    print("3. 或直接使用: python3 run_direct.py")

print(f"\n💡 当前工作目录: {subprocess.getoutput('pwd')}")
print("📁 确保在项目目录下运行命令")