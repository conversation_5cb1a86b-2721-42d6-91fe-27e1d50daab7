#!/usr/bin/env python3
"""
Verify that the contextual background system is properly configured
and all imports work correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_all_imports():
    """Verify all necessary imports work"""
    print("🔍 Verifying all imports...")
    
    try:
        # Test context analyzer imports
        from src.context_analyzer import ContextualThemeAnalyzer, IntelligentBackgroundPlanner
        print("✅ Context analyzer imports: OK")
        
        # Test dynamic background generator imports
        from src.dynamic_background_generator import DynamicBackgroundGenerator
        print("✅ Dynamic background generator imports: OK")
        
        # Test video assembler imports (to ensure no conflicts)
        from src.video_assembler import VideoAssembler
        print("✅ Video assembler imports: OK")
        
        # Test pipeline imports
        from src.pipeline import BookReviewPipeline
        print("✅ Pipeline imports: OK")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_contextual_creation():
    """Test creating contextual background generator"""
    print("\n🧠 Testing contextual background generator creation...")
    
    try:
        from src.dynamic_background_generator import DynamicBackgroundGenerator
        
        # Test with book metadata
        book_metadata = {
            "title": "《小王子》- 安托万·德·圣埃克苏佩里",
            "author": "安托万·德·圣埃克苏佩里"
        }
        
        generator = DynamicBackgroundGenerator(book_metadata=book_metadata)
        print("✅ DynamicBackgroundGenerator created successfully")
        print(f"📚 Book title: {generator.book_title}")
        print(f"✍️ Author: {generator.book_author}")
        
        # Test if contextual analyzer is available
        if hasattr(generator, 'contextual_analyzer'):
            print("✅ Contextual analyzer available")
        else:
            print("❌ Contextual analyzer not available")
            return False
            
        # Test if background planner is available
        if hasattr(generator, 'background_planner'):
            print("✅ Background planner available")
        else:
            print("❌ Background planner not available")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Error creating generator: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contextual_analysis():
    """Test the contextual analysis functionality"""
    print("\n🎭 Testing contextual analysis...")
    
    try:
        from src.context_analyzer import ContextualThemeAnalyzer
        
        analyzer = ContextualThemeAnalyzer()
        
        # Test with Little Prince content
        test_cases = [
            {
                "text": "小王子遇到了狐狸，他们在沙漠中建立了友谊",
                "book": "《小王子》",
                "expected_chars": ["小王子", "狐狸"],
                "expected_locs": ["沙漠"]
            },
            {
                "text": "福贵在农村的田野里耕作，和老牛一起工作",
                "book": "《活着》",
                "expected_chars": ["福贵"],
                "expected_locs": ["农村", "田野"]
            }
        ]
        
        all_passed = True
        
        for i, case in enumerate(test_cases):
            print(f"\n  Test {i+1}: {case['book']}")
            print(f"  Text: {case['text']}")
            
            result = analyzer.analyze_content_context(case['text'], case['book'])
            
            found_chars = [c['name'] for c in result.get('characters', [])]
            found_locs = [l['name'] for l in result.get('locations', [])]
            
            print(f"  Generated scene: {result['scene_description'][:80]}...")
            print(f"  Found characters: {found_chars}")
            print(f"  Found locations: {found_locs}")
            print(f"  Importance: {result.get('importance', 0):.2f}")
            
            # Check if expected elements were found
            chars_found = any(char in found_chars for char in case['expected_chars'])
            locs_found = any(loc in found_locs for loc in case['expected_locs'])
            
            if chars_found and locs_found:
                print("  ✅ Context analysis: PASSED")
            else:
                print("  ❌ Context analysis: FAILED")
                all_passed = False
        
        return all_passed
    except Exception as e:
        print(f"❌ Context analysis error: {e}")
        return False

def test_video_assembler_integration():
    """Test that video assembler can use the new system"""
    print("\n🎬 Testing video assembler integration...")
    
    try:
        from src.video_assembler import VideoAssembler
        from src.video_config import VideoConfig
        
        config = VideoConfig()
        assembler = VideoAssembler(config)
        
        # Check if the method exists
        if hasattr(assembler, 'assemble_video_with_dynamic_backgrounds'):
            print("✅ assemble_video_with_dynamic_backgrounds method exists")
            
            # Check method signature
            import inspect
            sig = inspect.signature(assembler.assemble_video_with_dynamic_backgrounds)
            params = list(sig.parameters.keys())
            
            if 'book_metadata' in params:
                print("✅ book_metadata parameter exists")
                return True
            else:
                print(f"❌ book_metadata parameter missing. Found: {params}")
                return False
        else:
            print("❌ assemble_video_with_dynamic_backgrounds method not found")
            return False
            
    except Exception as e:
        print(f"❌ Video assembler integration error: {e}")
        return False

def main():
    print("=== Verifying Contextual Background System Fix ===")
    print("This will check if all imports and functionality work correctly")
    print("-" * 60)
    
    tests = [
        ("Import Verification", verify_all_imports),
        ("Generator Creation", test_contextual_creation),
        ("Contextual Analysis", test_contextual_analysis),
        ("Video Assembler Integration", test_video_assembler_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 VERIFICATION SUMMARY")
    print(f"{'='*60}")
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The contextual background system is properly configured")
        print("🚀 You can now start the server and test video generation")
        print("\nTo test:")
        print("1. python3 app.py  # Start server")
        print("2. python3 test_contextual_backgrounds.py  # Test functionality")
    else:
        print(f"\n❌ {total_tests - total_passed} tests failed")
        print("💡 Please check the errors above and fix them")
        print("🔧 The system may not work properly until all tests pass")

if __name__ == "__main__":
    main()