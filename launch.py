#!/usr/bin/env python3
"""
简化的深度研究系统启动器
"""

import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent
src_path = project_root / 'src'
sys.path.insert(0, str(src_path))

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv 未安装，请运行: pip install python-dotenv")

# 创建必要目录
for dir_name in ['output', 'temp', 'logs']:
    dir_path = project_root / dir_name
    dir_path.mkdir(exist_ok=True)

def test_template_system():
    """测试模板系统"""
    print("🧪 测试模板系统...")
    
    try:
        from universal_research_template import (
            UniversalResearchPromptTemplate, 
            ResearchRequest, 
            ResearchScenario
        )
        
        # 创建模板系统
        template_system = UniversalResearchPromptTemplate()
        
        # 测试书籍分析
        request = ResearchRequest(
            scenario=ResearchScenario.BOOK_ANALYSIS,
            target="人工智能导论",
            audience="general"
        )
        
        prompt = template_system.generate_research_prompt(request)
        
        print(f"✅ 提示词生成成功，长度: {len(prompt)}字符")
        print(f"📋 支持的场景: {len(template_system.list_available_scenarios())}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板系统测试失败: {str(e)}")
        return False

def start_simple_api():
    """启动简化API"""
    print("🚀 启动简化API服务器...")
    
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    
    app = Flask(__name__)
    CORS(app)
    
    # 导入组件
    from universal_research_template import (
        UniversalResearchPromptTemplate, 
        ResearchRequest, 
        ResearchScenario
    )
    
    template_system = UniversalResearchPromptTemplate()
    
    @app.route('/')
    def home():
        return jsonify({
            "message": "深度研究系统 API",
            "version": "1.0.0",
            "endpoints": [
                "/api/scenarios",
                "/api/generate",
                "/api/test"
            ]
        })
    
    @app.route('/api/scenarios')
    def get_scenarios():
        try:
            scenarios = template_system.list_available_scenarios()
            return jsonify({
                "success": True,
                "scenarios": scenarios,
                "count": len(scenarios)
            })
        except Exception as e:
            return jsonify({"success": False, "error": str(e)}), 500
    
    @app.route('/api/generate', methods=['POST'])
    def generate_prompt():
        try:
            data = request.json
            target = data.get('target', '默认研究目标')
            scenario = data.get('scenario', 'book_analysis')
            audience = data.get('audience', 'general')
            
            # 创建请求
            research_request = ResearchRequest(
                scenario=ResearchScenario(scenario),
                target=target,
                audience=audience
            )
            
            # 生成提示词
            prompt = template_system.generate_research_prompt(research_request)
            
            return jsonify({
                "success": True,
                "target": target,
                "scenario": scenario,
                "prompt_length": len(prompt),
                "prompt_preview": prompt[:500] + "...",
                "full_prompt": prompt
            })
            
        except Exception as e:
            return jsonify({"success": False, "error": str(e)}), 500
    
    @app.route('/api/test')
    def test_api():
        return jsonify({
            "success": True,
            "message": "API运行正常",
            "timestamp": str(Path(__file__).stat().st_mtime)
        })
    
    print("🌐 API服务地址: http://localhost:5001")
    print("📖 测试地址: http://localhost:5001/api/test")
    print("💡 按 Ctrl+C 停止服务\n")
    
    # 启动服务器
    try:
        app.run(
            host='0.0.0.0',
            port=5001,
            debug=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def show_examples():
    """显示使用示例"""
    print("""
📖 API使用示例:

1. 获取支持的场景:
   curl http://localhost:5001/api/scenarios

2. 生成书籍分析提示词:
   curl -X POST http://localhost:5001/api/generate \\
     -H "Content-Type: application/json" \\
     -d '{"target": "人工智能导论", "scenario": "book_analysis"}'

3. 生成行业报告提示词:
   curl -X POST http://localhost:5001/api/generate \\
     -H "Content-Type: application/json" \\
     -d '{"target": "新能源汽车", "scenario": "industry_report"}'

4. 支持的场景类型:
   - book_analysis (书籍分析)
   - industry_report (行业报告)
   - research_progress (研究进展)
   - news_analysis (新闻分析)
   - market_analysis (市场分析)
   - book_recommendation (图书推荐)
""")

def main():
    print("🎯 深度研究系统")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'test':
            test_template_system()
        elif command == 'examples':
            show_examples()
        elif command == 'help':
            print("""
使用方法:
  python3 launch.py [命令]

命令:
  test      测试模板系统
  examples  显示API使用示例
  help      显示帮助信息
  (无参数)   启动API服务器
""")
        else:
            print(f"❌ 未知命令: {command}")
            print("运行 'python3 launch.py help' 查看帮助")
    else:
        # 先测试系统
        if test_template_system():
            # 启动API服务器
            start_simple_api()
        else:
            print("❌ 系统测试失败，无法启动服务器")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        import traceback
        traceback.print_exc()