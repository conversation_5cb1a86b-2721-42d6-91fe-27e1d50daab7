#!/usr/bin/env python3
"""
启动深度研究系统
"""

import os
import sys
import json
import time
import threading
import subprocess
from pathlib import Path
from datetime import datetime

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

# 检查依赖
def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'flask', 'flask_cors', 'requests', 'openai', 
        'numpy', 'PIL', 'websockets', 'dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'flask_cors':
                import flask_cors
            elif package == 'dotenv':
                import dotenv
            else:
                __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 缺失")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip3 install -r requirements_minimal.txt")
        return False
    
    return True

def check_environment():
    """检查环境变量"""
    required_env_vars = [
        'DOUBAO_API_KEY',
        'OPENROUTER_API_KEY'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("请检查 .env 文件")
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        'output',
        'temp',
        'logs'
    ]
    
    for dir_name in directories:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
        print(f"📁 创建目录: {dir_path}")

def test_basic_functionality():
    """测试基础功能"""
    print("\n🧪 测试基础功能...")
    
    try:
        # 测试模板系统
        from universal_research_template import UniversalResearchPromptTemplate, ResearchRequest, ResearchScenario
        
        template_system = UniversalResearchPromptTemplate()
        
        # 创建测试请求
        test_request = ResearchRequest(
            scenario=ResearchScenario.BOOK_ANALYSIS,
            target="测试书籍",
            audience="general"
        )
        
        # 生成提示词
        prompt = template_system.generate_research_prompt(test_request)
        
        if len(prompt) > 100:
            print("✅ 提示词模板系统 - 正常")
        else:
            print("❌ 提示词模板系统 - 异常")
            return False
        
        # 测试场景列表
        scenarios = template_system.list_available_scenarios()
        if len(scenarios) >= 6:
            print("✅ 研究场景 - 正常")
        else:
            print("❌ 研究场景 - 异常")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {str(e)}")
        return False

def start_api_server():
    """启动API服务器"""
    try:
        # 导入Flask应用
        from research_api import app
        
        print("\n🚀 启动API服务器...")
        print("🌐 服务地址: http://localhost:5000")
        print("📖 API文档: http://localhost:5000/api/research/examples")
        print("💡 按 Ctrl+C 停止服务")
        
        # 启动服务器
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ API服务器启动失败: {str(e)}")
        return False

def start_test_mode():
    """启动测试模式"""
    print("\n🧪 启动测试模式...")
    
    try:
        # 导入测试组件
        from universal_research_template import UniversalResearchPromptTemplate, ResearchRequest, ResearchScenario
        
        # 创建模板系统
        template_system = UniversalResearchPromptTemplate()
        
        # 交互式测试
        print("\n=== 深度研究系统测试模式 ===")
        print("支持的研究场景:")
        scenarios = template_system.list_available_scenarios()
        for i, scenario in enumerate(scenarios, 1):
            print(f"{i}. {scenario}")
        
        while True:
            print("\n选择测试选项:")
            print("1. 测试提示词生成")
            print("2. 测试API接口")
            print("3. 退出")
            
            choice = input("请输入选项 (1-3): ").strip()
            
            if choice == '1':
                # 测试提示词生成
                target = input("请输入研究目标: ").strip()
                scenario_choice = input("请选择场景编号 (1-6): ").strip()
                
                try:
                    scenario_index = int(scenario_choice) - 1
                    scenario = ResearchScenario(scenarios[scenario_index])
                    
                    request = ResearchRequest(
                        scenario=scenario,
                        target=target,
                        audience="general"
                    )
                    
                    prompt = template_system.generate_research_prompt(request)
                    
                    print(f"\n生成的提示词 (长度: {len(prompt)}字符):")
                    print("=" * 50)
                    print(prompt[:500] + "...")
                    print("=" * 50)
                    
                except Exception as e:
                    print(f"❌ 生成失败: {str(e)}")
            
            elif choice == '2':
                # 测试API接口
                print("启动API服务器进行测试...")
                threading.Thread(target=start_api_server, daemon=True).start()
                time.sleep(2)
                
                # 测试API
                import requests
                try:
                    response = requests.get("http://localhost:5000/api/research/scenarios", timeout=5)
                    if response.status_code == 200:
                        print("✅ API服务器正常运行")
                        data = response.json()
                        print(f"支持的场景数量: {len(data.get('scenarios', {}))}")
                    else:
                        print(f"❌ API响应异常: {response.status_code}")
                except Exception as e:
                    print(f"❌ API测试失败: {str(e)}")
            
            elif choice == '3':
                print("退出测试模式")
                break
            
            else:
                print("❌ 无效选项，请重新输入")
                
    except Exception as e:
        print(f"❌ 测试模式启动失败: {str(e)}")

def show_usage():
    """显示使用说明"""
    print("""
🚀 深度研究系统启动器

使用方法:
  python3 start_research_system.py [选项]

选项:
  --server    启动API服务器 (默认)
  --test      启动测试模式
  --check     检查系统状态
  --help      显示帮助信息

示例:
  python3 start_research_system.py --server
  python3 start_research_system.py --test
  python3 start_research_system.py --check
""")

def main():
    """主函数"""
    print("🎯 深度研究系统启动器")
    print("=" * 50)
    
    # 解析命令行参数
    args = sys.argv[1:] if len(sys.argv) > 1 else ['--server']
    
    if '--help' in args or '-h' in args:
        show_usage()
        return
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    # 检查系统状态
    if '--check' in args:
        print("🔍 检查系统状态...")
        dep_ok = check_dependencies()
        env_ok = check_environment()
        
        if dep_ok and env_ok:
            print("✅ 系统状态正常")
            if test_basic_functionality():
                print("✅ 基础功能测试通过")
            else:
                print("❌ 基础功能测试失败")
        else:
            print("❌ 系统状态异常")
        return
    
    # 基础检查
    print("🔍 检查依赖...")
    if not check_dependencies():
        print("❌ 依赖检查失败，请先安装依赖")
        return
    
    print("🔍 检查环境...")
    if not check_environment():
        print("❌ 环境检查失败，请检查配置")
        return
    
    print("📁 创建目录...")
    create_directories()
    
    # 启动对应模式
    if '--test' in args:
        start_test_mode()
    else:
        # 默认启动API服务器
        start_api_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {str(e)}")
        sys.exit(1)

