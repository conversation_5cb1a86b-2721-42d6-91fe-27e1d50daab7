#!/usr/bin/env python3
"""
测试字幕分割和动态背景功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.audio_aligner import AudioAligner

def test_subtitle_splitting():
    """测试字幕分割功能"""
    
    print("🎬 测试字幕分割功能")
    print("=" * 50)
    
    aligner = AudioAligner()
    
    # 测试长字幕分割
    test_segments = [
        {
            "text": "这是一个非常长的句子，需要被分割成多个较短的字幕片段，以确保用户能够完整地阅读每一部分内容。",
            "start": 0,
            "end": 8000,
            "audio_path": "test.mp3"
        },
        {
            "text": "<speak emotion=\"happy\">这本书真的很精彩！</speak>",
            "start": 8000,
            "end": 12000,
            "audio_path": "test.mp3"
        },
        {
            "text": "短句子",
            "start": 12000,
            "end": 14000,
            "audio_path": "test.mp3"
        }
    ]
    
    print(f"原始字幕段落数: {len(test_segments)}")
    for i, seg in enumerate(test_segments):
        duration = (seg['end'] - seg['start']) / 1000
        print(f"  {i+1}. \"{seg['text']}\" ({duration:.1f}s, {len(seg['text'])}字符)")
    
    # 执行分割
    split_segments = aligner.split_long_sentences(test_segments, max_duration=3000, max_chars=15)
    
    print(f"\n分割后字幕段落数: {len(split_segments)}")
    for i, seg in enumerate(split_segments):
        duration = (seg['end'] - seg['start']) / 1000
        print(f"  {i+1}. \"{seg['text']}\" ({duration:.1f}s, {len(seg['text'])}字符)")
    
    # 检查结果
    print(f"\n📊 分割结果分析:")
    too_long_text = [seg for seg in split_segments if len(seg['text']) > 15]
    too_long_time = [seg for seg in split_segments if (seg['end'] - seg['start']) > 3500]
    
    print(f"  文字超长片段: {len(too_long_text)}")
    print(f"  时间超长片段: {len(too_long_time)}")
    
    if too_long_text:
        print("  文字超长的片段:")
        for seg in too_long_text:
            print(f"    \"{seg['text']}\" ({len(seg['text'])}字符)")
    
    if too_long_time:
        print("  时间超长的片段:")
        for seg in too_long_time:
            duration = (seg['end'] - seg['start']) / 1000
            print(f"    \"{seg['text']}\" ({duration:.1f}s)")
    
    if not too_long_text and not too_long_time:
        print("  ✅ 所有片段都符合要求！")
    
    print(f"\n✅ 字幕分割测试完成")

if __name__ == "__main__":
    test_subtitle_splitting()