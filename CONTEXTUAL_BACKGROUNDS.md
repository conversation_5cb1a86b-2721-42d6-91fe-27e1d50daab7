# 🧠 智能上下文背景生成系统

## 概述

新的上下文背景生成系统根据文本内容的具体语境动态生成背景图片，而不是静态的主题切换。系统能够识别具体的人物、场景和情节，生成精确匹配的图像。

## 核心特性

### 1. 🎭 上下文分析 (Context Analysis)
- **人物识别**: 识别文中提到的具体人物（如"小王子"、"狐狸"、"福贵"）
- **场景定位**: 识别具体场景（如"B-612小行星"、"沙漠"、"农村"）
- **情节理解**: 识别具体情节（如"驯养"、"告别"、"耕作"）
- **情感检测**: 识别情感状态（如"悲伤"、"快乐"、"平静"）

### 2. ⏱️ 智能时间控制 (Intelligent Timing)
- **最小间隔**: 背景切换间隔至少15秒，避免频繁切换
- **重要性阈值**: 只有重要场景变化（importance > 0.6）才会触发切换
- **内容相似性**: 检测新旧内容的相似性，避免无意义切换

### 3. 🎨 精确图像生成 (Precise Image Generation)
- **具体场景**: 生成"小王子和狐狸在沙漠中"而非"小王子主题"
- **人物组合**: 根据同时出现的人物生成组合场景
- **环境匹配**: 根据文本中的环境描述生成对应背景

## 示例对比

### 传统系统 vs 新系统

| 文本内容 | 传统系统 | 新系统 |
|---------|---------|--------|
| "小王子遇到了狐狸" | 小王子主题图片 | 小王子和狐狸相遇的场景 |
| "福贵在赌场赌博" | 中国农村风景 | 中国男人在赌场的场景 |
| "在B-612小行星上" | 星空背景 | B-612小行星上的小王子和玫瑰 |

## 技术实现

### 1. 内容上下文分析器 (ContextualThemeAnalyzer)

```python
class ContextualThemeAnalyzer:
    def analyze_content_context(self, text: str, book_title: str) -> Dict:
        # 提取人物、地点、场景、动作、情感
        characters = self._extract_characters(text, book_type)
        locations = self._extract_locations(text, book_type)
        scenes = self._extract_scenes(text, book_type)
        actions = self._extract_actions(text)
        emotions = self._extract_emotions(text)
        
        # 创建具体场景描述
        scene_description = self._create_contextual_scene(
            characters, locations, scenes, actions, emotions
        )
```

### 2. 智能背景规划器 (IntelligentBackgroundPlanner)

```python
class IntelligentBackgroundPlanner:
    def __init__(self, min_duration=15.0, importance_threshold=0.6):
        self.min_duration = min_duration  # 最小间隔15秒
        self.importance_threshold = importance_threshold  # 重要性阈值
    
    def _should_change_background(self, new_context, current_theme, time_since_last):
        # 智能判断是否应该切换背景
        if time_since_last < self.min_duration:
            if new_context["importance"] < 0.9:  # 除非极其重要
                return False
        
        if new_context["importance"] < self.importance_threshold:
            return False
```

### 3. 书籍特定场景库

系统为不同书籍预定义了具体场景:

#### 《小王子》场景库:
```python
"小王子": {
    "characters": {
        "小王子": "little prince character",
        "狐狸": "fox character",
        "玫瑰": "rose flower",
        "飞行员": "pilot aviator"
    },
    "locations": {
        "小行星": "small planet asteroid in space",
        "沙漠": "vast desert landscape",
        "B-612": "small planet with baobab trees"
    },
    "scenes": {
        "驯养": "little prince taming fox scene",
        "告别": "farewell departure scene",
        "日落": "sunset on small planet"
    }
}
```

## 生成效果示例

### 小王子示例:
1. **"小王子和狐狸在沙漠中"**
   → `"Little Prince story scene featuring little prince character, fox character in vast desert landscape showing little prince taming fox scene"`

2. **"小王子在B-612小行星上照顾玫瑰"**
   → `"Little Prince story scene featuring little prince character, rose flower in small planet with baobab trees"`

### 活着示例:
1. **"福贵在赌场赌博"**
   → `"Chinese historical rural scene featuring old Chinese farmer in gambling scene"`

2. **"福贵和老牛在田野里耕作"**
   → `"Chinese historical rural scene featuring old Chinese farmer in Chinese farmland fields showing farming plowing fields scene"`

## 配置参数

```python
# 在video generation request中
{
    "enable_dynamic_backgrounds": true,
    "transition_interval": [15, 25],  # 15-25秒间隔，比之前更长
    "importance_threshold": 0.6,      # 重要性阈值
    "min_scene_duration": 15.0        # 最小场景持续时间
}
```

## 测试验证

运行测试脚本验证新功能:

```bash
python3 test_contextual_backgrounds.py
```

该测试会:
1. 分析不同书籍的具体情节
2. 验证人物和场景识别准确性
3. 检查时间间隔是否合理
4. 确认背景切换的智能性

## 优势对比

| 特性 | 传统系统 | 新上下文系统 |
|------|---------|-------------|
| 背景相关性 | 通用主题 | 具体场景匹配 |
| 切换频率 | 固定间隔(10-20s) | 智能控制(≥15s) |
| 内容理解 | 关键词匹配 | 深度语境分析 |
| 场景精确度 | 模糊主题 | 精确场景描述 |
| 用户体验 | 可能频繁切换 | 流畅观看体验 |

新系统确保背景图片与内容高度相关，同时避免过于频繁的切换，提供更好的观看体验。