# 深度研究Agent启动指南

## 🎯 系统概述

基于您提供的优秀DeepResearch提示词，我们构建了一个完整的**多智能体深度研究平台**，支持多种研究场景和媒体输出格式。

## ⚡ 快速启动

### 方法1：简化启动（推荐）

```bash
# 1. 测试系统
python3 launch.py test

# 2. 启动API服务器
python3 launch.py

# 3. 查看使用示例
python3 launch.py examples
```

### 方法2：完整功能启动

```bash
# 1. 安装依赖
pip3 install -r requirements_minimal.txt

# 2. 检查系统状态
python3 start_research_system.py --check

# 3. 启动服务器
python3 start_research_system.py --server
```

## 🌐 API接口

服务器启动后，访问以下地址：

- **主页**: http://localhost:5001/
- **测试**: http://localhost:5001/api/test
- **场景列表**: http://localhost:5001/api/scenarios

### 核心API端点

#### 1. 获取支持的研究场景
```bash
curl http://localhost:5001/api/scenarios
```

#### 2. 生成研究提示词
```bash
curl -X POST http://localhost:5001/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "target": "人工智能导论",
    "scenario": "book_analysis",
    "audience": "general"
  }'
```

## 📋 支持的研究场景

| 场景类型 | 描述 | 应用领域 |
|----------|------|----------|
| `book_analysis` | 图书深度分析 | 基于您的原始提示词设计 |
| `industry_report` | 行业研究报告 | 商业分析、市场研究 |
| `research_progress` | 研究进展分析 | 学术研究、技术发展 |
| `news_analysis` | 新闻深度解读 | 媒体分析、事件研究 |
| `market_analysis` | 市场分析报告 | 投资决策、商业策略 |
| `book_recommendation` | 图书推荐评价 | 阅读指导、教育培训 |

## 🎨 输出格式

| 格式类型 | 描述 | 文件类型 |
|----------|------|----------|
| `report` | 详细研究报告 | Markdown/HTML |
| `video_script` | 视频制作脚本 | 带视觉提示 |
| `podcast_script` | 播客录制脚本 | 音频友好格式 |
| `video` | 完整视频文件 | MP4 |
| `audio` | 音频文件 | WAV/MP3 |
| `interactive_report` | 交互式报告 | HTML+CSS+JS |

## 📖 使用示例

### 1. 书籍分析（您的原始场景）
```json
{
  "target": "Structures: Or Why Things Don't Fall Down by J.E. Gordon",
  "scenario": "book_analysis",
  "audience": "general",
  "additional_requirements": {
    "focus_areas": ["工程学", "物理原理"],
    "analysis_depth": "深度"
  }
}
```

### 2. 行业报告
```json
{
  "target": "人工智能",
  "scenario": "industry_report",
  "audience": "business",
  "additional_requirements": {
    "focus_area": "中国市场",
    "time_frame": "2024-2025"
  }
}
```

### 3. 研究进展
```json
{
  "target": "量子计算",
  "scenario": "research_progress",
  "audience": "academic"
}
```

## 🔧 系统架构

```
用户请求 → API接口 → DeepResearchAgent → 研究结果
                  ↓
         UniversalPromptTemplate → 动态提示词生成
                  ↓
         ConceptExpander → 语义扩展和概念挖掘
                  ↓
         SearchEnhancer → 网络搜索增强
                  ↓
         ContentGenerator → 高质量内容生成
                  ↓
         MediaPipeline → 多媒体输出
```

## 🎯 核心特性

### ✨ 基于您的提示词设计
- **完整保留**您的原始提示词结构
- **扩展支持**多种研究场景
- **智能适配**不同受众需求

### 🧠 智能分析引擎
- **概念扩展**: 深度挖掘相关概念
- **语义分析**: 理解用户意图和内容类型
- **搜索增强**: 实时网络信息整合
- **质量评估**: 多维度内容质量控制

### 🎨 多媒体输出
- **报告文档**: 结构化深度分析
- **视频脚本**: 可视化制作指导
- **音频内容**: TTS语音合成
- **交互式展示**: 网页版动态报告

## 🚀 商业应用

### 目标用户
- **企业决策者**: 行业分析、市场研究
- **研究机构**: 学术研究、技术分析
- **媒体机构**: 深度报道、内容创作
- **教育培训**: 教学材料、学习资源

### 应用场景
- **商业智能**: 竞争分析、市场洞察
- **学术研究**: 文献综述、研究进展
- **内容创作**: 深度文章、视频制作
- **决策支持**: 投资分析、战略规划

## 📊 性能特性

- **响应时间**: 2-10秒（简单分析）
- **处理能力**: 支持并发请求
- **输出质量**: 多维度质量评估（>0.8分）
- **可扩展性**: 模块化架构，易于扩展

## 🔒 安全特性

- **API密钥管理**: 环境变量配置
- **输入验证**: 请求参数校验
- **错误处理**: 多级回退机制
- **访问控制**: CORS配置

## 📈 监控指标

系统提供实时性能监控：
- 处理成功率
- 平均响应时间
- 内容质量评分
- 用户使用分布

## 🛠️ 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip3 install -r requirements_minimal.txt
   ```

2. **环境变量缺失**
   - 检查 `.env` 文件
   - 确保API密钥正确配置

3. **API连接失败**
   - 检查网络连接
   - 验证API密钥有效性

4. **生成质量不高**
   - 调整提示词参数
   - 增加上下文信息

## 📞 技术支持

如遇问题，请：
1. 运行 `python3 launch.py test` 检查系统状态
2. 查看日志文件了解详细错误信息
3. 检查网络连接和API配置

---

🎉 **恭喜！您的深度研究Agent系统已成功构建并可独立运行！**