<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度研究Agent - 统一工作流平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 40px;
        }
        .workflow-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .workflow-card:hover {
            transform: translateY(-5px);
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            position: relative;
        }
        .step-indicator::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .step {
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }
        .step.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        .step.completed {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }
        .progress-bar-custom {
            height: 25px;
            border-radius: 20px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .result-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .scenario-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .scenario-card:hover {
            border-color: #667eea;
            background: #f8f9fa;
        }
        .scenario-card.selected {
            border-color: #667eea;
            background: #e7f3ff;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .status-message {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        .media-preview {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            font-family: inherit;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        textarea {
            resize: vertical;
            line-height: 1.5;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn-secondary:hover:not(:disabled) {
            background: #5a6268;
        }
        
        .btn-secondary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .bgm-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .bgm-info {
            margin-top: 5px;
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
        }
        
        .bgm-info strong {
            color: #333;
        }
        
        #bgmPlayer {
            margin-top: 10px;
            width: 100%;
            height: 40px;
        }
        
        .status {
            margin-top: 30px;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
            display: none;
        }
        
        .status.show {
            display: block;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .status-message {
            color: #666;
            margin-bottom: 15px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s;
            animation: progress-animation 2s ease-in-out infinite;
        }
        
        @keyframes progress-animation {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(200%); }
        }
        
        .video-preview {
            margin-top: 20px;
            text-align: center;
        }
        
        video {
            width: 100%;
            max-width: 400px;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .download-btn {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 30px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background 0.3s;
        }
        
        .download-btn:hover {
            background: #45a049;
        }
        
        .error {
            color: #f44336;
            margin-top: 10px;
        }
        
        .steps {
            margin-top: 15px;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            transition: background-color 0.3s;
        }
        
        .step.processing {
            background: #fff3e0;
        }
        
        .step.completed {
            background: #e8f5e8;
        }
        
        .step.failed {
            background: #ffebee;
        }
        
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e0e0e0;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .step-icon.completed {
            background: #4CAF50;
            color: white;
        }
        
        .step-icon.processing {
            background: #ff9800;
            color: white;
            animation: pulse 1s infinite;
        }
        
        .step-icon.failed {
            background: #f44336;
            color: white;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .step-details {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .step-result {
            font-size: 12px;
            color: #888;
            font-style: italic;
        }
        
        .step-image {
            margin-top: 10px;
            max-width: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .step-script {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #4CAF50;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .step-script h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
            font-size: 16px;
            font-weight: 600;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e0e0e0;
        }
        
        input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }
        
        .form-group label {
            font-weight: 600;
            color: #555;
        }
        
        .voice-preview-section {
            margin-top: 10px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .voice-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .voice-details {
            flex: 1;
        }
        
        .voice-name {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .voice-description {
            display: block;
            font-size: 14px;
            color: #666;
        }
        
        .preview-btn {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .preview-btn:hover {
            background: #45a049;
        }
        
        .preview-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .personality-preview-section {
            margin-top: 10px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .personality-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .personality-details {
            flex: 1;
        }
        
        .personality-name {
            display: block;
            font-weight: 600;
            color: #2c5aa0;
            margin-bottom: 4px;
            font-size: 16px;
        }
        
        .personality-description {
            display: block;
            font-size: 14px;
            color: #555;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .personality-style {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }
        
        .personality-tag {
            padding: 4px 8px;
            background: #2c5aa0;
            color: white;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .data-enhancement-info {
            margin-top: 5px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 6px;
            font-size: 12px;
            color: #666;
            border-left: 3px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="text-center mb-5">
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-brain me-3"></i>深度研究Agent
                </h1>
                <p class="lead text-muted">统一工作流平台：用户输入 → 提示词增强 → 深度研究 → 脚本生成 → 媒体制作</p>
            </div>

            <!-- 工作流程指示器 -->
            <div class="workflow-card">
                <h3 class="text-center mb-4">工作流程</h3>
                <div class="step-indicator">
                    <div class="step" id="step-1" title="用户输入">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="step" id="step-2" title="提示词增强">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="step" id="step-3" title="深度研究">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="step" id="step-4" title="脚本生成">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="step" id="step-5" title="媒体制作">
                        <i class="fas fa-video"></i>
                    </div>
                </div>
            </div>

            <!-- 输入表单 -->
            <div class="workflow-card">
                <h3 class="mb-4"><i class="fas fa-cogs me-2"></i>配置研究任务</h3>
                <form id="workflowForm">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <label for="userInput" class="form-label">
                                <i class="fas fa-edit me-2"></i>研究目标
                            </label>
                            <textarea 
                                class="form-control" 
                                id="userInput" 
                                rows="4" 
                                placeholder="请输入您想要深度研究的内容，例如：分析《结构：或者为什么建筑物不会倒塌》这本书的核心观点和应用价值..."
                                required
                            ></textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="researchScenario" class="form-label">
                                <i class="fas fa-microscope me-2"></i>研究场景
                            </label>
                            <select class="form-select" id="researchScenario">
                                <option value="book_analysis">图书深度分析</option>
                                <option value="industry_report">行业研究报告</option>
                                <option value="research_progress">研究进展分析</option>
                                <option value="news_analysis">新闻深度解读</option>
                                <option value="market_analysis">市场分析报告</option>
                                <option value="book_recommendation">图书推荐评价</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="outputFormat" class="form-label">
                                <i class="fas fa-file-export me-2"></i>输出格式
                            </label>
                            <select class="form-select" id="outputFormat">
                                <option value="research_report">研究报告</option>
                                <option value="video_script">视频脚本</option>
                                <option value="podcast_script">播客脚本</option>
                                <option value="full_video">完整视频</option>
                                <option value="audio_podcast">音频播客</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="audience" class="form-label">
                                <i class="fas fa-users me-2"></i>目标受众
                            </label>
                            <select class="form-select" id="audience">
                                <option value="general">一般读者</option>
                                <option value="professional">专业人士</option>
                                <option value="academic">学术研究者</option>
                                <option value="business">商业决策者</option>
                                <option value="student">学生</option>
                            </select>
                        </div>
                    </div>

                    <!-- 媒体制作配置（在选择视频或音频格式时显示） -->
                    <div id="mediaConfigSection" style="display: none;">
                        <h5 class="mt-4 mb-3"><i class="fas fa-cogs me-2"></i>媒体制作配置</h5>
                        
                        <!-- 语音配置 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="voiceSelect" class="form-label">
                                    <i class="fas fa-microphone me-2"></i>语音选择
                                </label>
                                <select class="form-select" id="voiceSelect">
                                    <option value="">正在加载语音...</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 语音预览区域 -->
                        <div class="voice-preview-section" id="voicePreviewSection" style="display: none;">
                            <div class="voice-info" id="voiceInfo">
                                <div class="voice-details">
                                    <span class="voice-name" id="voiceName"></span>
                                    <span class="voice-description" id="voiceDescription"></span>
                                    <div class="voice-provider" id="voiceProvider" style="font-size: 12px; color: #666; margin-top: 5px;"></div>
                                    <div class="voice-features" id="voiceFeatures" style="font-size: 12px; color: #888; margin-top: 3px;"></div>
                                </div>
                                <button type="button" class="preview-btn" id="previewBtn">🔊 试听</button>
                            </div>
                            <audio id="voicePreviewPlayer" style="display: none;" preload="none"></audio>
                        </div>

                        <!-- 评论者风格配置 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="personalitySelect" class="form-label">
                                    <i class="fas fa-user-circle me-2"></i>评论者风格
                                </label>
                                <select class="form-select" id="personalitySelect">
                                    <option value="">正在加载评论者风格...</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 评论者风格预览区域 -->
                        <div class="personality-preview-section" id="personalityPreviewSection" style="display: none;">
                            <div class="personality-info" id="personalityInfo">
                                <div class="personality-details">
                                    <span class="personality-name" id="personalityName"></span>
                                    <span class="personality-description" id="personalityDescription"></span>
                                    <div class="personality-style" id="personalityStyle"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 视频配置（仅视频格式显示） -->
                        <div id="videoOnlyConfig" style="display: none;">
                            <h6 class="mt-3 mb-3"><i class="fas fa-video me-2"></i>视频参数</h6>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="aspectRatio" class="form-label">画面比例</label>
                                    <select class="form-select" id="aspectRatio">
                                        <option value="9:16">竖屏 (9:16)</option>
                                        <option value="16:9">横屏 (16:9)</option>
                                        <option value="1:1">正方形 (1:1)</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="resolution" class="form-label">分辨率</label>
                                    <select class="form-select" id="resolution">
                                        <option value="1920x1080">1920x1080 (1080p)</option>
                                        <option value="1280x720">1280x720 (720p)</option>
                                        <option value="3840x2160">3840x2160 (4K)</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="subtitleStyle" class="form-label">字幕位置</label>
                                    <select class="form-select" id="subtitleStyle">
                                        <option value="bottom_center">底部居中</option>
                                        <option value="bottom_left">底部左对齐</option>
                                        <option value="middle_center">中央居中</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableSubtitles" checked>
                                        <label class="form-check-label" for="enableSubtitles">
                                            启用字幕
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 背景音乐配置 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="bgmSelect" class="form-label">
                                    <i class="fas fa-music me-2"></i>背景音乐（可选）
                                </label>
                                <div class="d-flex align-items-center gap-2">
                                    <select class="form-select" id="bgmSelect">
                                        <option value="">默认背景音乐</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-secondary" id="bgmPreviewBtn" disabled>
                                        <i class="fas fa-play me-1"></i>预览
                                    </button>
                                </div>
                                <div id="bgmInfo" class="bgm-info mt-2"></div>
                                <audio id="bgmPlayer" controls style="width: 100%; margin-top: 10px; display: none;"></audio>
                            </div>
                        </div>

                        <!-- 数据增强选项 -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="useWebData" checked>
                                    <label class="form-check-label" for="useWebData">
                                        <i class="fas fa-globe me-2"></i>使用网络数据增强
                                        <small class="text-muted d-block">自动搜索相关信息提升内容质量</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket me-2"></i>开始深度研究
                        </button>
                    </div>
                </form>
            </div>

            <!-- 进度显示 -->
            <div id="progressSection" class="workflow-card" style="display: none;">
                <h3 class="mb-4"><i class="fas fa-tasks me-2"></i>执行进度</h3>
                <div class="progress mb-3">
                    <div id="progressBar" class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="currentStep" class="text-center mb-3">
                    <h5 class="text-muted">准备中...</h5>
                </div>
                <div id="statusMessages"></div>
            </div>

            <!-- 结果显示 -->
            <div id="resultSection" class="workflow-card" style="display: none;">
                <h3 class="mb-4"><i class="fas fa-check-circle me-2 text-success"></i>研究完成</h3>
                <div class="row">
                    <div class="col-md-8">
                        <div class="result-panel">
                            <h5><i class="fas fa-chart-line me-2"></i>质量评分</h5>
                            <div class="progress mb-3">
                                <div id="qualityScore" class="progress-bar bg-success" role="progressbar"></div>
                            </div>
                            <p id="qualityText" class="mb-0"></p>
                        </div>
                        
                        <div class="result-panel">
                            <h5><i class="fas fa-file-text me-2"></i>研究内容预览</h5>
                            <div id="contentPreview" class="text-muted"></div>
                        </div>

                        <!-- 视频/音频预览区域 -->
                        <div class="result-panel" id="mediaPreviewSection" style="display: none;">
                            <h5><i class="fas fa-play-circle me-2"></i>媒体预览</h5>
                            <div id="mediaPreview" class="text-center">
                                <video id="videoPlayer" controls style="width: 100%; max-width: 400px; border-radius: 8px; display: none;">
                                    您的浏览器不支持视频播放
                                </video>
                                <audio id="audioPlayer" controls style="width: 100%; margin-top: 10px; display: none;">
                                    您的浏览器不支持音频播放
                                </audio>
                                <div id="mediaInfo" class="mt-2 text-muted"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="result-panel">
                            <h5><i class="fas fa-download me-2"></i>生成文件</h5>
                            <div id="mediaFiles"></div>
                        </div>
                        
                        <div class="result-panel">
                            <h5><i class="fas fa-clock me-2"></i>处理信息</h5>
                            <p id="processingTime" class="mb-2"></p>
                            <p id="timestamp" class="text-muted mb-0"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统统计 -->
            <div class="workflow-card">
                <h5><i class="fas fa-chart-bar me-2"></i>系统统计</h5>
                <div class="row" id="systemStats">
                    <div class="col-md-3 text-center">
                        <div class="h4 text-primary" id="totalTasks">0</div>
                        <div class="text-muted">总任务数</div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="h4 text-success" id="completedTasks">0</div>
                        <div class="text-muted">已完成</div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="h4 text-warning" id="activeTasks">0</div>
                        <div class="text-muted">处理中</div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="h4 text-info" id="avgTime">0s</div>
                        <div class="text-muted">平均用时</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTaskId = null;
        let progressInterval = null;

        // 页面加载时获取系统信息
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStats();
            loadVoiceOptions();
            loadPersonalityOptions();
            loadBGMOptions();
            
            // 监听输出格式变化
            document.getElementById('outputFormat').addEventListener('change', function() {
                const mediaConfig = document.getElementById('mediaConfigSection');
                const videoOnlyConfig = document.getElementById('videoOnlyConfig');
                
                if (this.value === 'full_video' || this.value === 'audio_podcast') {
                    mediaConfig.style.display = 'block';
                    
                    // 只有视频格式才显示视频特定配置
                    if (this.value === 'full_video') {
                        videoOnlyConfig.style.display = 'block';
                    } else {
                        videoOnlyConfig.style.display = 'none';
                    }
                } else {
                    mediaConfig.style.display = 'none';
                }
            });
        });

        // 提交表单
        document.getElementById('workflowForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                user_input: document.getElementById('userInput').value,
                research_scenario: document.getElementById('researchScenario').value,
                output_format: document.getElementById('outputFormat').value,
                audience: document.getElementById('audience').value
            };

            // 添加媒体制作配置
            if (formData.output_format === 'full_video' || formData.output_format === 'audio_podcast') {
                formData.voice_id = document.getElementById('voiceSelect').value || null;
                formData.personality_id = document.getElementById('personalitySelect').value || null;
                formData.bgm_path = document.getElementById('bgmSelect').value || null;
                formData.use_web_data = document.getElementById('useWebData').checked;
                
                // 视频特定配置
                if (formData.output_format === 'full_video') {
                    formData.aspect_ratio = document.getElementById('aspectRatio').value;
                    formData.resolution = document.getElementById('resolution').value;
                    formData.subtitle_style = document.getElementById('subtitleStyle').value;
                    formData.enable_subtitles = document.getElementById('enableSubtitles').checked;
                }
            }

            try {
                const response = await fetch('/api/workflow/start', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentTaskId = result.task_id;
                    showProgress();
                    startProgressMonitoring();
                } else {
                    alert('启动失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        });

        // 显示进度界面
        function showProgress() {
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            updateStepIndicator(1);
        }

        // 开始进度监控
        function startProgressMonitoring() {
            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/workflow/status/${currentTaskId}`);
                    const status = await response.json();
                    
                    if (status.success) {
                        updateProgress(status);
                        
                        if (status.status === 'completed') {
                            clearInterval(progressInterval);
                            showResult();
                        } else if (status.status === 'failed') {
                            clearInterval(progressInterval);
                            showError(status.error);
                        }
                    }
                } catch (error) {
                    console.error('监控错误:', error);
                }
            }, 2000);
        }

        // 更新进度
        function updateProgress(status) {
            const progressBar = document.getElementById('progressBar');
            const currentStep = document.getElementById('currentStep');
            
            progressBar.style.width = status.progress + '%';
            currentStep.innerHTML = `<h5>${status.current_step}</h5><p class="text-muted mb-0">${status.message}</p>`;
            
            // 更新步骤指示器
            const stepNumber = Math.ceil(status.progress / 20);
            updateStepIndicator(stepNumber);
            
            // 添加状态消息
            if (status.steps && status.steps.length > 0) {
                const lastStep = status.steps[status.steps.length - 1];
                if (lastStep.status === 'completed') {
                    addStatusMessage(lastStep.step, lastStep.message, 'success');
                }
            }
        }

        // 更新步骤指示器
        function updateStepIndicator(currentStep) {
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step-${i}`);
                step.classList.remove('active', 'completed');
                
                if (i < currentStep) {
                    step.classList.add('completed');
                } else if (i === currentStep) {
                    step.classList.add('active');
                }
            }
        }

        // 添加状态消息
        function addStatusMessage(step, message, type) {
            const statusMessages = document.getElementById('statusMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message border-${type === 'success' ? 'success' : 'primary'}`;
            messageDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2 text-${type === 'success' ? 'success' : 'primary'}"></i>
                    <strong>${step}:</strong> ${message}
                </div>
            `;
            statusMessages.appendChild(messageDiv);
        }

        // 显示结果
        async function showResult() {
            try {
                const response = await fetch(`/api/workflow/result/${currentTaskId}`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.result;
                    
                    // 更新质量评分
                    const qualityScore = Math.round(data.quality_score * 100);
                    document.getElementById('qualityScore').style.width = qualityScore + '%';
                    document.getElementById('qualityText').textContent = `研究质量评分: ${qualityScore}%`;
                    
                    // 显示内容预览
                    document.getElementById('contentPreview').textContent = 
                        data.final_output.substring(0, 500) + '...';
                    
                    // 显示生成的文件
                    const mediaFiles = document.getElementById('mediaFiles');
                    mediaFiles.innerHTML = '';
                    
                    // 检查是否有媒体文件可以预览
                    let hasVideoFile = false;
                    let hasAudioFile = false;
                    
                    data.media_files.forEach((file, index) => {
                        const fileName = file.split('/').pop();
                        const fileExt = fileName.split('.').pop().toLowerCase();
                        
                        const fileDiv = document.createElement('div');
                        fileDiv.className = 'mb-2 d-flex align-items-center gap-2';
                        
                        // 下载按钮
                        const downloadBtn = document.createElement('a');
                        downloadBtn.href = `/api/workflow/download/${currentTaskId}/${index}`;
                        downloadBtn.className = 'btn btn-outline-primary btn-sm';
                        downloadBtn.download = true;
                        downloadBtn.innerHTML = `<i class="fas fa-download me-1"></i>${fileName}`;
                        
                        fileDiv.appendChild(downloadBtn);
                        
                        // 如果是视频或音频文件，添加预览按钮
                        if (['mp4', 'avi', 'mov', 'mkv'].includes(fileExt)) {
                            hasVideoFile = true;
                            const previewBtn = document.createElement('button');
                            previewBtn.className = 'btn btn-outline-success btn-sm';
                            previewBtn.innerHTML = '<i class="fas fa-play me-1"></i>预览';
                            previewBtn.onclick = () => showVideoPreview(`/api/workflow/download/${currentTaskId}/${index}`, fileName);
                            fileDiv.appendChild(previewBtn);
                        } else if (['mp3', 'wav', 'aac', 'm4a'].includes(fileExt)) {
                            hasAudioFile = true;
                            const previewBtn = document.createElement('button');
                            previewBtn.className = 'btn btn-outline-info btn-sm';
                            previewBtn.innerHTML = '<i class="fas fa-volume-up me-1"></i>播放';
                            previewBtn.onclick = () => showAudioPreview(`/api/workflow/download/${currentTaskId}/${index}`, fileName);
                            fileDiv.appendChild(previewBtn);
                        }
                        
                        mediaFiles.appendChild(fileDiv);
                    });
                    
                    // 如果有媒体文件，显示预览区域
                    if (hasVideoFile || hasAudioFile) {
                        document.getElementById('mediaPreviewSection').style.display = 'block';
                    }
                    
                    // 显示处理信息
                    document.getElementById('processingTime').textContent = 
                        `处理时长: ${data.total_processing_time.toFixed(1)}秒`;
                    document.getElementById('timestamp').textContent = 
                        `完成时间: ${new Date(data.timestamp).toLocaleString()}`;
                    
                    // 显示结果区域
                    document.getElementById('resultSection').style.display = 'block';
                    updateStepIndicator(6); // 全部完成
                }
            } catch (error) {
                showError('获取结果失败: ' + error.message);
            }
        }

        // 显示错误
        function showError(error) {
            const currentStep = document.getElementById('currentStep');
            currentStep.innerHTML = `
                <h5 class="text-danger">处理失败</h5>
                <p class="text-muted mb-0">${error}</p>
            `;
        }

        // 加载系统统计
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/system/stats');
                const stats = await response.json();
                
                if (stats.success) {
                    const data = stats.stats;
                    document.getElementById('totalTasks').textContent = data.tasks.pending + data.tasks.processing + data.tasks.completed + data.tasks.failed;
                    document.getElementById('completedTasks').textContent = data.tasks.completed;
                    document.getElementById('activeTasks').textContent = data.active_tasks;
                    document.getElementById('avgTime').textContent = data.workflow.average_processing_time.toFixed(1) + 's';
                }
            } catch (error) {
                console.error('加载统计失败:', error);
            }
        }

        // 加载语音选项
        async function loadVoiceOptions() {
            try {
                const response = await fetch('/api/voices');
                const data = await response.json();
                const voiceSelect = document.getElementById('voiceSelect');
                
                voiceSelect.innerHTML = '';
                
                // 添加默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '选择语音（默认：晓晓）';
                voiceSelect.appendChild(defaultOption);
                
                // 添加语音选项
                if (data.voices) {
                    data.voices.forEach(voice => {
                        const option = document.createElement('option');
                        option.value = voice.id;
                        option.textContent = `${voice.name} (${voice.provider})`;
                        voiceSelect.appendChild(option);
                    });
                }
                
                // 添加语音选择监听器
                voiceSelect.addEventListener('change', handleVoiceSelection);
                
            } catch (error) {
                console.error('加载语音失败:', error);
                document.getElementById('voiceSelect').innerHTML = '<option value="">加载语音失败</option>';
            }
        }

        // 处理语音选择
        function handleVoiceSelection() {
            const voiceSelect = document.getElementById('voiceSelect');
            const previewSection = document.getElementById('voicePreviewSection');
            
            if (voiceSelect.value) {
                // 这里可以显示语音详情和试听功能
                previewSection.style.display = 'block';
                document.getElementById('voiceName').textContent = voiceSelect.options[voiceSelect.selectedIndex].text;
            } else {
                previewSection.style.display = 'none';
            }
        }

        // 加载评论者风格选项
        async function loadPersonalityOptions() {
            try {
                const response = await fetch('/api/personalities');
                const data = await response.json();
                const personalitySelect = document.getElementById('personalitySelect');
                
                personalitySelect.innerHTML = '';
                
                // 添加默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '选择评论者风格（默认：董宇辉）';
                personalitySelect.appendChild(defaultOption);
                
                // 添加评论者风格选项
                if (data.personalities) {
                    data.personalities.forEach(personality => {
                        const option = document.createElement('option');
                        option.value = personality.id;
                        option.textContent = personality.name;
                        personalitySelect.appendChild(option);
                    });
                }
                
                // 添加选择监听器
                personalitySelect.addEventListener('change', handlePersonalitySelection);
                
            } catch (error) {
                console.error('加载评论者风格失败:', error);
                document.getElementById('personalitySelect').innerHTML = '<option value="">加载评论者风格失败</option>';
            }
        }

        // 处理评论者风格选择
        function handlePersonalitySelection() {
            const personalitySelect = document.getElementById('personalitySelect');
            const previewSection = document.getElementById('personalityPreviewSection');
            
            if (personalitySelect.value) {
                previewSection.style.display = 'block';
                document.getElementById('personalityName').textContent = personalitySelect.options[personalitySelect.selectedIndex].text;
            } else {
                previewSection.style.display = 'none';
            }
        }

        // 加载背景音乐选项
        async function loadBGMOptions() {
            try {
                const response = await fetch('/api/bgm');
                const data = await response.json();
                const bgmSelect = document.getElementById('bgmSelect');
                
                bgmSelect.innerHTML = '';
                
                // 添加默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '默认背景音乐';
                bgmSelect.appendChild(defaultOption);
                
                // 添加背景音乐选项
                if (data.bgm_files) {
                    data.bgm_files.forEach(bgm => {
                        const option = document.createElement('option');
                        option.value = bgm.path;
                        option.textContent = bgm.name;
                        option.disabled = !bgm.exists;
                        bgmSelect.appendChild(option);
                    });
                }
                
                // 添加选择监听器
                bgmSelect.addEventListener('change', handleBGMSelection);
                
            } catch (error) {
                console.error('加载背景音乐失败:', error);
                document.getElementById('bgmInfo').textContent = '背景音乐加载失败';
            }
        }

        // 处理背景音乐选择
        function handleBGMSelection() {
            const bgmSelect = document.getElementById('bgmSelect');
            const bgmInfo = document.getElementById('bgmInfo');
            const previewBtn = document.getElementById('bgmPreviewBtn');
            
            if (bgmSelect.value) {
                bgmInfo.textContent = `已选择: ${bgmSelect.options[bgmSelect.selectedIndex].text}`;
                previewBtn.disabled = false;
            } else {
                bgmInfo.textContent = '请选择背景音乐';
                previewBtn.disabled = true;
            }
        }

        // 语音试听功能
        document.getElementById('previewBtn').addEventListener('click', function() {
            const voiceSelect = document.getElementById('voiceSelect');
            if (voiceSelect.value) {
                // 这里可以实现语音试听功能
                alert('语音试听功能：' + voiceSelect.options[voiceSelect.selectedIndex].text);
            }
        });

        // 背景音乐预览功能
        document.getElementById('bgmPreviewBtn').addEventListener('click', function() {
            const bgmSelect = document.getElementById('bgmSelect');
            const bgmPlayer = document.getElementById('bgmPlayer');
            
            if (bgmSelect.value) {
                bgmPlayer.src = bgmSelect.value;
                bgmPlayer.style.display = 'block';
                bgmPlayer.play();
            }
        });

        // 视频预览功能
        function showVideoPreview(videoUrl, fileName) {
            const videoPlayer = document.getElementById('videoPlayer');
            const audioPlayer = document.getElementById('audioPlayer');
            const mediaInfo = document.getElementById('mediaInfo');
            
            // 隐藏音频播放器
            audioPlayer.style.display = 'none';
            
            // 显示视频播放器
            videoPlayer.src = videoUrl;
            videoPlayer.style.display = 'block';
            mediaInfo.textContent = `正在播放: ${fileName}`;
            
            // 滚动到预览区域
            document.getElementById('mediaPreviewSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 音频预览功能
        function showAudioPreview(audioUrl, fileName) {
            const videoPlayer = document.getElementById('videoPlayer');
            const audioPlayer = document.getElementById('audioPlayer');
            const mediaInfo = document.getElementById('mediaInfo');
            
            // 隐藏视频播放器
            videoPlayer.style.display = 'none';
            
            // 显示音频播放器
            audioPlayer.src = audioUrl;
            audioPlayer.style.display = 'block';
            mediaInfo.textContent = `正在播放: ${fileName}`;
            
            // 滚动到预览区域
            document.getElementById('mediaPreviewSection').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
