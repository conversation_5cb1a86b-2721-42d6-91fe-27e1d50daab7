<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多智能体协作系统 - 增强版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            padding: 40px;
        }
        .agent-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            border-left: 4px solid #667eea;
        }
        .agent-card:hover {
            transform: translateY(-3px);
        }
        .agent-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-idle { background: #e9ecef; color: #6c757d; }
        .status-running { background: #fff3cd; color: #856404; }
        .status-completed { background: #d1edff; color: #0c63e4; }
        .status-failed { background: #f8d7da; color: #721c24; }
        
        .workflow-canvas {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 400px;
            border: 2px dashed #dee2e6;
        }
        .workflow-node {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: inline-block;
            min-width: 150px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .workflow-node:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .workflow-node.active {
            border: 2px solid #667eea;
            background: #e7f3ff;
        }
        .provider-config {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .capability-tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin: 2px;
        }
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
        }
        .nav-tabs .nav-link.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .progress-ring {
            width: 60px;
            height: 60px;
            margin: 10px auto;
        }
        .progress-ring circle {
            fill: none;
            stroke: #667eea;
            stroke-width: 4;
            stroke-dasharray: 188.5;
            stroke-dashoffset: 188.5;
            transition: stroke-dashoffset 0.3s ease;
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="text-center mb-4">
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-network-wired me-3"></i>多智能体协作系统
                </h1>
                <p class="lead text-muted">统一管理和协调多个AI智能体，实现复杂任务的自动化处理</p>
            </div>

            <!-- 导航标签 -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="agents-tab" data-bs-toggle="tab" data-bs-target="#agents" type="button" role="tab">
                        <i class="fas fa-robot me-2"></i>智能体管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="workflow-tab" data-bs-toggle="tab" data-bs-target="#workflow" type="button" role="tab">
                        <i class="fas fa-project-diagram me-2"></i>工作流编排
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="providers-tab" data-bs-toggle="tab" data-bs-target="#providers" type="button" role="tab">
                        <i class="fas fa-cloud me-2"></i>提供商管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>实时监控
                    </button>
                </li>
            </ul>

            <!-- 标签内容 -->
            <div class="tab-content" id="mainTabContent">
                <!-- 智能体管理 -->
                <div class="tab-pane fade show active" id="agents" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <h4><i class="fas fa-robot me-2"></i>智能体状态</h4>
                            <div id="agentsContainer">
                                <!-- 智能体卡片将在这里动态加载 -->
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h4><i class="fas fa-cogs me-2"></i>智能体配置</h4>
                            <div id="agentConfigPanel">
                                <p class="text-muted">选择一个智能体进行配置</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工作流编排 -->
                <div class="tab-pane fade" id="workflow" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <h4><i class="fas fa-project-diagram me-2"></i>工作流设计器</h4>
                            <div class="workflow-canvas" id="workflowCanvas">
                                <div class="text-center text-muted mt-5">
                                    <i class="fas fa-plus-circle fa-3x mb-3"></i>
                                    <p>拖拽智能体到此处创建工作流</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h4><i class="fas fa-list me-2"></i>工作流模板</h4>
                            <div id="workflowTemplates">
                                <!-- 工作流模板将在这里加载 -->
                            </div>
                            
                            <h4 class="mt-4"><i class="fas fa-play me-2"></i>执行控制</h4>
                            <div class="mb-3">
                                <label class="form-label">选择工作流</label>
                                <select class="form-select" id="workflowSelect">
                                    <option value="">选择工作流模板</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">初始数据 (JSON)</label>
                                <textarea class="form-control" id="initialData" rows="4" placeholder='{"user_input": "示例输入"}'></textarea>
                            </div>
                            <button class="btn btn-primary w-100" onclick="executeWorkflow()">
                                <i class="fas fa-play me-2"></i>执行工作流
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 提供商管理 -->
                <div class="tab-pane fade" id="providers" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <h4><i class="fas fa-cloud me-2"></i>AI提供商配置</h4>
                            <div id="providersContainer">
                                <!-- 提供商列表将在这里加载 -->
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h4><i class="fas fa-plus me-2"></i>添加提供商</h4>
                            <form id="providerForm">
                                <div class="mb-3">
                                    <label class="form-label">提供商ID</label>
                                    <input type="text" class="form-control" id="providerId" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">提供商名称</label>
                                    <input type="text" class="form-control" id="providerName" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">提供商类型</label>
                                    <select class="form-select" id="providerType" required>
                                        <option value="">选择类型</option>
                                        <option value="text_generation">文本生成</option>
                                        <option value="image_generation">图像生成</option>
                                        <option value="speech_synthesis">语音合成</option>
                                        <option value="embedding">向量嵌入</option>
                                        <option value="translation">翻译</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">API URL</label>
                                    <input type="url" class="form-control" id="apiUrl" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">API Key</label>
                                    <input type="password" class="form-control" id="apiKey" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">模型名称</label>
                                    <input type="text" class="form-control" id="modelName" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>添加提供商
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 实时监控 -->
                <div class="tab-pane fade" id="monitor" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <h4><i class="fas fa-chart-line me-2"></i>系统状态</h4>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="progress-ring">
                                        <svg width="60" height="60">
                                            <circle cx="30" cy="30" r="25"></circle>
                                        </svg>
                                    </div>
                                    <h5 id="activeAgents">0</h5>
                                    <small class="text-muted">活跃智能体</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-ring">
                                        <svg width="60" height="60">
                                            <circle cx="30" cy="30" r="25"></circle>
                                        </svg>
                                    </div>
                                    <h5 id="runningTasks">0</h5>
                                    <small class="text-muted">运行任务</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-ring">
                                        <svg width="60" height="60">
                                            <circle cx="30" cy="30" r="25"></circle>
                                        </svg>
                                    </div>
                                    <h5 id="completedTasks">0</h5>
                                    <small class="text-muted">完成任务</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-ring">
                                        <svg width="60" height="60">
                                            <circle cx="30" cy="30" r="25"></circle>
                                        </svg>
                                    </div>
                                    <h5 id="errorRate">0%</h5>
                                    <small class="text-muted">错误率</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4><i class="fas fa-terminal me-2"></i>系统日志</h4>
                            <div class="log-container" id="systemLogs">
                                <div>[INFO] 系统启动完成</div>
                                <div>[INFO] 智能体管理器初始化成功</div>
                                <div>[INFO] 等待任务...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let agents = {};
        let providers = {};
        let workflowTemplates = {};
        let currentTasks = {};

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAgents();
            loadProviders();
            loadWorkflowTemplates();
            startMonitoring();
            
            // 提供商表单提交
            document.getElementById('providerForm').addEventListener('submit', handleProviderSubmit);
        });

        // 加载智能体
        async function loadAgents() {
            try {
                const response = await fetch('/api/agents');
                const data = await response.json();
                
                if (data.success) {
                    agents = data.agents;
                    renderAgents();
                }
            } catch (error) {
                console.error('加载智能体失败:', error);
            }
        }

        // 渲染智能体卡片
        function renderAgents() {
            const container = document.getElementById('agentsContainer');
            container.innerHTML = '';
            
            Object.entries(agents).forEach(([agentType, agentInfo]) => {
                const card = createAgentCard(agentType, agentInfo);
                container.appendChild(card);
            });
        }

        // 创建智能体卡片
        function createAgentCard(agentType, agentInfo) {
            const card = document.createElement('div');
            card.className = 'agent-card';
            card.onclick = () => selectAgent(agentType);

            const statusClass = `status-${agentInfo.status}`;
            const capabilities = agentInfo.capabilities.map(cap =>
                `<span class="capability-tag">${cap}</span>`
            ).join('');

            card.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5><i class="fas fa-robot me-2"></i>${agentType.toUpperCase()}</h5>
                        <span class="agent-status ${statusClass}">${agentInfo.status}</span>
                        <div class="mt-2">${capabilities}</div>
                    </div>
                    <div class="text-end">
                        <button class="btn btn-sm btn-outline-primary" onclick="executeAgent('${agentType}')">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        输入: ${agentInfo.required_inputs.join(', ')}
                    </small>
                </div>
            `;

            return card;
        }

        // 选择智能体
        function selectAgent(agentType) {
            const agentInfo = agents[agentType];
            const configPanel = document.getElementById('agentConfigPanel');

            configPanel.innerHTML = `
                <h5><i class="fas fa-robot me-2"></i>${agentType.toUpperCase()}</h5>
                <div class="mb-3">
                    <label class="form-label">智能体状态</label>
                    <div class="agent-status status-${agentInfo.status}">${agentInfo.status}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">必需输入</label>
                    <div class="form-control-plaintext">${agentInfo.required_inputs.join(', ')}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">输出格式</label>
                    <div class="form-control-plaintext">${Object.keys(agentInfo.output_schema).join(', ')}</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">配置参数 (JSON)</label>
                    <textarea class="form-control" id="agentConfig" rows="6">${JSON.stringify(agentInfo.config, null, 2)}</textarea>
                </div>
                <button class="btn btn-primary w-100" onclick="updateAgentConfig('${agentType}')">
                    <i class="fas fa-save me-2"></i>更新配置
                </button>
                <button class="btn btn-success w-100 mt-2" onclick="showExecuteDialog('${agentType}')">
                    <i class="fas fa-play me-2"></i>执行智能体
                </button>
            `;
        }

        // 更新智能体配置
        async function updateAgentConfig(agentType) {
            try {
                const configText = document.getElementById('agentConfig').value;
                const config = JSON.parse(configText);

                const response = await fetch(`/api/agents/${agentType}/config`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({config})
                });

                const data = await response.json();
                if (data.success) {
                    alert('配置更新成功');
                    loadAgents(); // 重新加载智能体状态
                } else {
                    alert('配置更新失败: ' + data.error);
                }
            } catch (error) {
                alert('配置格式错误: ' + error.message);
            }
        }

        // 显示执行对话框
        function showExecuteDialog(agentType) {
            const agentInfo = agents[agentType];
            const inputFields = agentInfo.required_inputs.map(input =>
                `<div class="mb-3">
                    <label class="form-label">${input}</label>
                    <textarea class="form-control" id="input_${input}" rows="3" placeholder="输入${input}数据"></textarea>
                </div>`
            ).join('');

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">执行智能体: ${agentType.toUpperCase()}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${inputFields}
                            <div class="mb-3">
                                <label class="form-label">任务优先级</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="LOW">低</option>
                                    <option value="MEDIUM" selected>中</option>
                                    <option value="HIGH">高</option>
                                    <option value="CRITICAL">紧急</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="executeAgentWithInputs('${agentType}')">执行</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // 执行智能体任务
        async function executeAgentWithInputs(agentType) {
            try {
                const agentInfo = agents[agentType];
                const inputData = {};

                // 收集输入数据
                agentInfo.required_inputs.forEach(input => {
                    const element = document.getElementById(`input_${input}`);
                    if (element) {
                        try {
                            inputData[input] = JSON.parse(element.value);
                        } catch {
                            inputData[input] = element.value;
                        }
                    }
                });

                const priority = document.getElementById('taskPriority').value;

                const response = await fetch(`/api/agents/${agentType}/execute`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        input_data: inputData,
                        priority: priority
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert(`任务已启动，任务ID: ${data.task_id}`);
                    currentTasks[data.task_id] = {
                        agent_type: agentType,
                        created_at: new Date()
                    };

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
                    modal.hide();

                    // 开始监控任务
                    monitorTask(data.task_id);
                } else {
                    alert('任务启动失败: ' + data.error);
                }
            } catch (error) {
                alert('执行失败: ' + error.message);
            }
        }

        // 加载提供商
        async function loadProviders() {
            try {
                const response = await fetch('/api/mcp/providers');
                const data = await response.json();

                if (data.success) {
                    providers = data.providers;
                    renderProviders();
                }
            } catch (error) {
                console.error('加载提供商失败:', error);
            }
        }

        // 渲染提供商
        function renderProviders() {
            const container = document.getElementById('providersContainer');
            container.innerHTML = '';

            Object.entries(providers).forEach(([providerId, providerInfo]) => {
                const card = createProviderCard(providerId, providerInfo);
                container.appendChild(card);
            });
        }

        // 创建提供商卡片
        function createProviderCard(providerId, providerInfo) {
            const card = document.createElement('div');
            card.className = 'provider-config';

            card.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6><i class="fas fa-cloud me-2"></i>${providerInfo.provider_name}</h6>
                        <small class="text-muted">${providerInfo.provider_type} | ${providerInfo.model_name}</small>
                        <div class="mt-1">
                            <span class="badge bg-info">${providerInfo.max_requests_per_minute}/min</span>
                            <span class="badge bg-success">$${providerInfo.cost_per_request}</span>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeProvider('${providerId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            return card;
        }

        // 处理提供商表单提交
        async function handleProviderSubmit(event) {
            event.preventDefault();

            const formData = {
                provider_id: document.getElementById('providerId').value,
                provider_name: document.getElementById('providerName').value,
                provider_type: document.getElementById('providerType').value,
                api_url: document.getElementById('apiUrl').value,
                api_key: document.getElementById('apiKey').value,
                model_name: document.getElementById('modelName').value
            };

            try {
                const response = await fetch('/api/mcp/providers', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                if (data.success) {
                    alert('提供商添加成功');
                    document.getElementById('providerForm').reset();
                    loadProviders();
                } else {
                    alert('添加失败: ' + data.error);
                }
            } catch (error) {
                alert('添加失败: ' + error.message);
            }
        }

        // 删除提供商
        async function removeProvider(providerId) {
            if (!confirm('确定要删除这个提供商吗？')) return;

            try {
                const response = await fetch(`/api/mcp/providers/${providerId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();
                if (data.success) {
                    alert('提供商删除成功');
                    loadProviders();
                } else {
                    alert('删除失败: ' + data.error);
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        // 加载工作流模板
        async function loadWorkflowTemplates() {
            try {
                const response = await fetch('/api/workflow/templates');
                const data = await response.json();

                if (data.success) {
                    workflowTemplates = data.templates;
                    renderWorkflowTemplates();
                }
            } catch (error) {
                console.error('加载工作流模板失败:', error);
            }
        }

        // 渲染工作流模板
        function renderWorkflowTemplates() {
            const container = document.getElementById('workflowTemplates');
            const select = document.getElementById('workflowSelect');

            container.innerHTML = '';
            select.innerHTML = '<option value="">选择工作流模板</option>';

            Object.entries(workflowTemplates).forEach(([templateName, steps]) => {
                // 添加到模板列表
                const templateCard = document.createElement('div');
                templateCard.className = 'workflow-node';
                templateCard.innerHTML = `
                    <h6>${templateName}</h6>
                    <small>${steps.length} 个步骤</small>
                `;
                container.appendChild(templateCard);

                // 添加到选择框
                const option = document.createElement('option');
                option.value = templateName;
                option.textContent = templateName;
                select.appendChild(option);
            });
        }

        // 执行工作流
        async function executeWorkflow() {
            const workflowName = document.getElementById('workflowSelect').value;
            const initialDataText = document.getElementById('initialData').value;

            if (!workflowName) {
                alert('请选择工作流模板');
                return;
            }

            let initialData = {};
            try {
                if (initialDataText.trim()) {
                    initialData = JSON.parse(initialDataText);
                }
            } catch (error) {
                alert('初始数据格式错误: ' + error.message);
                return;
            }

            try {
                const response = await fetch('/api/workflow/execute', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        workflow_name: workflowName,
                        initial_data: initialData
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert(`工作流已启动，任务ID: ${data.task_id}`);
                    currentTasks[data.task_id] = {
                        workflow_name: workflowName,
                        created_at: new Date()
                    };
                    monitorTask(data.task_id);
                } else {
                    alert('工作流启动失败: ' + data.error);
                }
            } catch (error) {
                alert('执行失败: ' + error.message);
            }
        }

        // 监控任务
        async function monitorTask(taskId) {
            const checkStatus = async () => {
                try {
                    const response = await fetch(`/api/workflow/status/${taskId}`);
                    const data = await response.json();

                    if (data.success) {
                        updateTaskStatus(taskId, data);

                        if (data.status === 'completed' || data.status === 'failed') {
                            delete currentTasks[taskId];
                            return;
                        }

                        // 继续监控
                        setTimeout(checkStatus, 2000);
                    }
                } catch (error) {
                    console.error('监控任务失败:', error);
                }
            };

            checkStatus();
        }

        // 更新任务状态
        function updateTaskStatus(taskId, statusData) {
            addLog(`[${taskId.substr(0, 8)}] ${statusData.current_step || statusData.message}`);
        }

        // 开始监控
        function startMonitoring() {
            setInterval(async () => {
                try {
                    const response = await fetch('/api/system/stats');
                    const data = await response.json();

                    if (data.success) {
                        updateSystemStats(data.stats);
                    }
                } catch (error) {
                    console.error('获取系统状态失败:', error);
                }
            }, 5000);
        }

        // 更新系统统计
        function updateSystemStats(stats) {
            document.getElementById('activeAgents').textContent = Object.keys(agents).length;
            document.getElementById('runningTasks').textContent = Object.keys(currentTasks).length;
            document.getElementById('completedTasks').textContent = stats.tasks?.completed || 0;
            document.getElementById('errorRate').textContent = '0%';
        }

        // 添加日志
        function addLog(message) {
            const logsContainer = document.getElementById('systemLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;

            // 限制日志条数
            while (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }
    </script>
</body>
</html>
