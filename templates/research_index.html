<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度研究系统 - AI驱动的内容生成平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 40px;
        }
        .research-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            border-left: 4px solid #667eea;
            cursor: pointer;
        }
        .research-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .research-card.selected {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .format-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .format-card:hover {
            border-color: #667eea;
            background: #e7f3ff;
        }
        .format-card.selected {
            border-color: #28a745;
            background: #f8fff9;
        }
        .progress-container {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .progress-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .progress-step.active {
            background: #e7f3ff;
            border-left: 4px solid #667eea;
        }
        .progress-step.completed {
            background: #f8fff9;
            border-left: 4px solid #28a745;
        }
        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            background: #dee2e6;
            color: #6c757d;
        }
        .step-icon.active {
            background: #667eea;
            color: white;
            animation: pulse 2s infinite;
        }
        .step-icon.completed {
            background: #28a745;
            color: white;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .scenario-icon {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        .format-icon {
            font-size: 1.5rem;
            margin-right: 10px;
        }
        .results-container {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .result-file {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .quality-score {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            color: white;
        }
        .score-excellent { background: #28a745; }
        .score-good { background: #17a2b8; }
        .score-fair { background: #ffc107; color: #212529; }
        .score-poor { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="text-center mb-5">
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-brain me-3"></i>深度研究系统
                </h1>
                <p class="lead text-muted">AI驱动的深度调研、多人对谈播客和视频内容生成平台</p>
                <div class="row text-center mt-4">
                    <div class="col-md-3">
                        <i class="fas fa-search fa-2x text-primary mb-2"></i>
                        <h6>深度调研</h6>
                        <small class="text-muted">多维度分析研究</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-microphone fa-2x text-primary mb-2"></i>
                        <h6>多人对谈</h6>
                        <small class="text-muted">智能播客生成</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-video fa-2x text-primary mb-2"></i>
                        <h6>视频制作</h6>
                        <small class="text-muted">完整内容输出</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-magic fa-2x text-primary mb-2"></i>
                        <h6>端到端</h6>
                        <small class="text-muted">自动化工作流</small>
                    </div>
                </div>
            </div>

            <!-- 研究配置表单 -->
            <div id="configForm">
                <div class="row">
                    <div class="col-md-8">
                        <!-- 研究主题输入 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-lightbulb me-2"></i>研究主题
                            </label>
                            <textarea class="form-control" id="userInput" rows="4" 
                                placeholder="请输入您想要深度研究的主题，例如：
• 分析《人类简史》这本书的核心观点和现实意义
• 研究人工智能在教育领域的应用现状和发展趋势
• 分析新能源汽车行业的竞争格局和投资机会"></textarea>
                        </div>

                        <!-- 研究场景选择 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-compass me-2"></i>研究场景
                            </label>
                            <div id="scenarioCards">
                                <!-- 场景卡片将在这里动态加载 -->
                            </div>
                        </div>

                        <!-- 输出格式选择 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-file-alt me-2"></i>输出格式
                            </label>
                            <div id="formatCards">
                                <!-- 格式卡片将在这里动态加载 -->
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- 目标受众 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-users me-2"></i>目标受众
                            </label>
                            <select class="form-select" id="targetAudience">
                                <option value="general">一般读者</option>
                                <option value="professional">专业人士</option>
                                <option value="academic">学术研究者</option>
                                <option value="business">商业决策者</option>
                                <option value="student">学生群体</option>
                            </select>
                        </div>

                        <!-- 附加要求 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-plus-circle me-2"></i>附加要求
                            </label>
                            <textarea class="form-control" id="additionalRequirements" rows="3" 
                                placeholder="可选：特殊要求或偏好设置"></textarea>
                        </div>

                        <!-- 启动按钮 -->
                        <div class="d-grid">
                            <button class="btn btn-primary btn-lg" onclick="startResearch()">
                                <i class="fas fa-rocket me-2"></i>开始深度研究
                            </button>
                        </div>

                        <!-- 预估时间显示 -->
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                预估完成时间: <span id="estimatedTime">请选择输出格式</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 进度显示 -->
            <div id="progressContainer" class="progress-container">
                <h4 class="mb-4">
                    <i class="fas fa-cogs me-2"></i>研究进行中...
                </h4>
                <div id="progressSteps">
                    <!-- 进度步骤将在这里动态显示 -->
                </div>
                <div class="progress mt-3" style="height: 10px;">
                    <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">
                        任务ID: <span id="currentTaskId"></span>
                    </small>
                </div>
            </div>

            <!-- 结果显示 -->
            <div id="resultsContainer" class="results-container">
                <h4 class="mb-4">
                    <i class="fas fa-check-circle text-success me-2"></i>研究完成
                </h4>
                <div id="resultsContent">
                    <!-- 结果内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedScenario = null;
        let selectedFormat = null;
        let currentTaskId = null;
        let progressInterval = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadScenarios();
            loadFormats();
        });

        // 加载研究场景
        async function loadScenarios() {
            try {
                const response = await fetch('/api/research/scenarios');
                const data = await response.json();
                
                if (data.success) {
                    renderScenarios(data.scenarios);
                }
            } catch (error) {
                console.error('加载场景失败:', error);
            }
        }

        // 渲染场景卡片
        function renderScenarios(scenarios) {
            const container = document.getElementById('scenarioCards');
            container.innerHTML = '';
            
            scenarios.forEach(scenario => {
                const card = document.createElement('div');
                card.className = 'research-card';
                card.onclick = () => selectScenario(scenario.id, card);
                
                card.innerHTML = `
                    <div class="text-center">
                        <div class="scenario-icon">${scenario.icon}</div>
                        <h6>${scenario.name}</h6>
                        <small class="text-muted">${scenario.description}</small>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        // 选择场景
        function selectScenario(scenarioId, cardElement) {
            // 移除其他选中状态
            document.querySelectorAll('#scenarioCards .research-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 设置当前选中
            cardElement.classList.add('selected');
            selectedScenario = scenarioId;
        }

        // 加载输出格式
        async function loadFormats() {
            try {
                const response = await fetch('/api/research/output-formats');
                const data = await response.json();

                if (data.success) {
                    renderFormats(data.formats);
                }
            } catch (error) {
                console.error('加载格式失败:', error);
            }
        }

        // 渲染格式卡片
        function renderFormats(formats) {
            const container = document.getElementById('formatCards');
            container.innerHTML = '';

            formats.forEach(format => {
                const card = document.createElement('div');
                card.className = 'format-card';
                card.onclick = () => selectFormat(format.id, format.estimated_time, card);

                card.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="format-icon">${format.icon}</div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${format.name}</h6>
                            <small class="text-muted">${format.description}</small>
                        </div>
                        <div class="text-end">
                            <small class="text-primary">${format.estimated_time}</small>
                        </div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 选择格式
        function selectFormat(formatId, estimatedTime, cardElement) {
            // 移除其他选中状态
            document.querySelectorAll('#formatCards .format-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 设置当前选中
            cardElement.classList.add('selected');
            selectedFormat = formatId;

            // 更新预估时间
            document.getElementById('estimatedTime').textContent = estimatedTime;
        }

        // 启动研究
        async function startResearch() {
            // 验证输入
            const userInput = document.getElementById('userInput').value.trim();
            if (!userInput) {
                alert('请输入研究主题');
                return;
            }

            if (!selectedScenario) {
                alert('请选择研究场景');
                return;
            }

            if (!selectedFormat) {
                alert('请选择输出格式');
                return;
            }

            const targetAudience = document.getElementById('targetAudience').value;
            const additionalRequirements = document.getElementById('additionalRequirements').value.trim();

            try {
                const response = await fetch('/api/research/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_input: userInput,
                        research_scenario: selectedScenario,
                        output_format: selectedFormat,
                        target_audience: targetAudience,
                        additional_requirements: additionalRequirements
                    })
                });

                const data = await response.json();

                if (data.success) {
                    currentTaskId = data.task_id;

                    // 隐藏配置表单，显示进度
                    document.getElementById('configForm').style.display = 'none';
                    document.getElementById('progressContainer').style.display = 'block';
                    document.getElementById('currentTaskId').textContent = currentTaskId;

                    // 开始监控进度
                    startProgressMonitoring();
                } else {
                    alert('启动研究失败: ' + data.error);
                }
            } catch (error) {
                alert('启动研究失败: ' + error.message);
            }
        }

        // 开始进度监控
        function startProgressMonitoring() {
            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/research/status/${currentTaskId}`);
                    const data = await response.json();

                    if (data.success) {
                        updateProgress(data);

                        if (data.status === 'completed') {
                            clearInterval(progressInterval);
                            showResults();
                        } else if (data.status === 'failed') {
                            clearInterval(progressInterval);
                            showError(data.error);
                        }
                    }
                } catch (error) {
                    console.error('获取进度失败:', error);
                }
            }, 2000);
        }

        // 更新进度显示
        function updateProgress(data) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = data.progress + '%';
            progressBar.textContent = data.progress + '%';

            // 更新步骤显示
            const stepsContainer = document.getElementById('progressSteps');
            const stepHtml = `
                <div class="progress-step active">
                    <div class="step-icon active">
                        <i class="fas fa-cog fa-spin"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">${data.current_step}</h6>
                        <small class="text-muted">进度: ${data.progress}%</small>
                    </div>
                </div>
            `;
            stepsContainer.innerHTML = stepHtml;
        }

        // 显示结果
        async function showResults() {
            try {
                const response = await fetch(`/api/research/result/${currentTaskId}`);
                const data = await response.json();

                if (data.success) {
                    document.getElementById('progressContainer').style.display = 'none';
                    document.getElementById('resultsContainer').style.display = 'block';

                    renderResults(data.result);
                }
            } catch (error) {
                console.error('获取结果失败:', error);
            }
        }

        // 渲染结果
        function renderResults(result) {
            const container = document.getElementById('resultsContent');

            let qualityClass = 'score-fair';
            if (result.quality_score >= 90) qualityClass = 'score-excellent';
            else if (result.quality_score >= 75) qualityClass = 'score-good';
            else if (result.quality_score < 60) qualityClass = 'score-poor';

            container.innerHTML = `
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>研究质量评分</h5>
                        <span class="quality-score ${qualityClass}">${result.quality_score}分</span>
                    </div>
                    <p class="text-muted mt-2">${result.summary}</p>
                </div>

                <div class="mb-4">
                    <h5>生成的文件</h5>
                    ${Object.entries(result.output_files || {}).map(([type, path]) => `
                        <div class="result-file">
                            <div>
                                <i class="fas fa-file me-2"></i>
                                <strong>${getFileTypeName(type)}</strong>
                            </div>
                            <a href="/api/research/download/${currentTaskId}/${type}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i>下载
                            </a>
                        </div>
                    `).join('')}
                </div>

                <div class="text-center">
                    <button class="btn btn-primary" onclick="startNewResearch()">
                        <i class="fas fa-plus me-2"></i>开始新的研究
                    </button>
                </div>
            `;
        }

        // 获取文件类型名称
        function getFileTypeName(type) {
            const names = {
                'research_report': '研究报告',
                'podcast_script': '播客脚本',
                'audio_podcast': '音频播客',
                'complete_video': '完整视频'
            };
            return names[type] || type;
        }

        // 显示错误
        function showError(error) {
            document.getElementById('progressContainer').style.display = 'none';
            alert('研究失败: ' + error);
            startNewResearch();
        }

        // 开始新研究
        function startNewResearch() {
            // 重置状态
            selectedScenario = null;
            selectedFormat = null;
            currentTaskId = null;

            // 清除选中状态
            document.querySelectorAll('.research-card, .format-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 清空表单
            document.getElementById('userInput').value = '';
            document.getElementById('additionalRequirements').value = '';
            document.getElementById('estimatedTime').textContent = '请选择输出格式';

            // 显示配置表单
            document.getElementById('configForm').style.display = 'block';
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';
        }
    </script>
</body>
</html>
