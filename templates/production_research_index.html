<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产级深度研究系统 - 真实AI驱动的内容生成平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin: 20px auto;
            max-width: 1400px;
            padding: 40px;
        }
        .production-badge {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        .ai-model-tag {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin: 2px;
            display: inline-block;
        }
        .research-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #3498db;
            cursor: pointer;
            position: relative;
        }
        .research-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .research-card.selected {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        .format-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        .format-card:hover {
            border-color: #3498db;
            background: linear-gradient(135deg, #e7f3ff 0%, #d1edff 100%);
        }
        .format-card.selected {
            border-color: #27ae60;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        .progress-container {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }
        .progress-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .progress-step.active {
            background: linear-gradient(135deg, #e7f3ff 0%, #d1edff 100%);
            border-left: 4px solid #3498db;
        }
        .progress-step.completed {
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
            border-left: 4px solid #27ae60;
        }
        .step-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            background: #dee2e6;
            color: #6c757d;
            font-size: 18px;
        }
        .step-icon.active {
            background: #3498db;
            color: white;
            animation: pulse 2s infinite;
        }
        .step-icon.completed {
            background: #27ae60;
            color: white;
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
            70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
        }
        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        .scenario-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        .format-icon {
            font-size: 1.8rem;
            margin-right: 12px;
        }
        .results-container {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }
        .result-file {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }
        .result-file:hover {
            background: #e9ecef;
        }
        .quality-score {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 600;
            color: white;
            font-size: 14px;
        }
        .score-excellent { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .score-good { background: linear-gradient(135deg, #3498db, #5dade2); }
        .score-fair { background: linear-gradient(135deg, #f39c12, #f1c40f); color: #2c3e50; }
        .score-poor { background: linear-gradient(135deg, #e74c3c, #ec7063); }
        .system-status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background: #27ae60; }
        .status-degraded { background: #f39c12; }
        .status-unhealthy { background: #e74c3c; }
        .cost-breakdown {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 12px;
        }
        .processing-details {
            background: #e8f4fd;
            border-radius: 8px;
            padding: 10px;
            margin: 8px 0;
            font-size: 11px;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 页面标题 -->
            <div class="text-center mb-5">
                <div class="production-badge">
                    <i class="fas fa-cog me-2"></i>PRODUCTION MODE
                </div>
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-brain me-3"></i>生产级深度研究系统
                </h1>
                <p class="lead text-muted">真实AI驱动的深度调研、多人对谈播客和视频内容生成平台</p>
                
                <!-- 系统状态指示器 -->
                <div class="system-status" id="systemStatus">
                    <div class="d-flex justify-content-center align-items-center">
                        <span class="status-indicator status-healthy" id="statusIndicator"></span>
                        <span id="statusText">系统检查中...</span>
                        <button class="btn btn-sm btn-outline-primary ms-3" onclick="checkSystemHealth()">
                            <i class="fas fa-sync-alt"></i> 刷新状态
                        </button>
                    </div>
                </div>
                
                <div class="row text-center mt-4">
                    <div class="col-md-3">
                        <i class="fas fa-search fa-2x text-primary mb-2"></i>
                        <h6>真实深度调研</h6>
                        <small class="text-muted">多AI模型协作分析</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-microphone fa-2x text-primary mb-2"></i>
                        <h6>豆包TTS音频</h6>
                        <small class="text-muted">真实多声音合成</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-video fa-2x text-primary mb-2"></i>
                        <h6>完整视频制作</h6>
                        <small class="text-muted">生产级视频管道</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-magic fa-2x text-primary mb-2"></i>
                        <h6>端到端自动化</h6>
                        <small class="text-muted">无模拟真实处理</small>
                    </div>
                </div>
            </div>

            <!-- 研究配置表单 -->
            <div id="configForm">
                <div class="row">
                    <div class="col-md-8">
                        <!-- 研究主题输入 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-lightbulb me-2"></i>研究主题
                            </label>
                            <textarea class="form-control" id="userInput" rows="4" 
                                placeholder="请输入您想要深度研究的主题，系统将使用真实的AI模型进行分析：
• 分析《人类简史》这本书的核心观点和现实意义
• 研究人工智能在教育领域的应用现状和发展趋势
• 分析新能源汽车行业的竞争格局和投资机会
• 评估区块链技术在金融领域的应用前景"></textarea>
                        </div>

                        <!-- 研究场景选择 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-compass me-2"></i>研究场景
                            </label>
                            <div id="scenarioCards">
                                <!-- 场景卡片将在这里动态加载 -->
                            </div>
                        </div>

                        <!-- 输出格式选择 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-file-alt me-2"></i>输出格式
                            </label>
                            <div id="formatCards">
                                <!-- 格式卡片将在这里动态加载 -->
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- 目标受众 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-users me-2"></i>目标受众
                            </label>
                            <select class="form-select" id="targetAudience">
                                <option value="general">一般读者</option>
                                <option value="professional">专业人士</option>
                                <option value="academic">学术研究者</option>
                                <option value="business">商业决策者</option>
                                <option value="student">学生群体</option>
                                <option value="expert">领域专家</option>
                            </select>
                        </div>

                        <!-- 语音配置 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-volume-up me-2"></i>语音配置
                            </label>
                            <select class="form-select" id="voiceConfig">
                                <option value="">加载中...</option>
                            </select>
                            <small class="text-muted">使用豆包TTS真实语音合成</small>
                        </div>

                        <!-- 评论者风格 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-user-tie me-2"></i>评论者风格
                            </label>
                            <select class="form-select" id="personalityStyle">
                                <option value="">加载中...</option>
                            </select>
                        </div>

                        <!-- 附加要求 -->
                        <div class="mb-4">
                            <label class="form-label h5">
                                <i class="fas fa-plus-circle me-2"></i>附加要求
                            </label>
                            <textarea class="form-control" id="additionalRequirements" rows="3" 
                                placeholder="可选：特殊要求或偏好设置"></textarea>
                        </div>

                        <!-- 启动按钮 -->
                        <div class="d-grid">
                            <button class="btn btn-primary btn-lg" onclick="startProductionResearch()">
                                <i class="fas fa-rocket me-2"></i>开始生产级研究
                            </button>
                        </div>

                        <!-- 预估信息显示 -->
                        <div class="mt-3">
                            <div class="text-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    预估完成时间: <span id="estimatedTime">请选择输出格式</span>
                                </small>
                            </div>
                            <div class="text-center mt-2" id="aiModelsUsed">
                                <!-- AI模型标签将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 进度显示 -->
            <div id="progressContainer" class="progress-container">
                <h4 class="mb-4">
                    <i class="fas fa-cogs me-2"></i>生产级研究进行中...
                </h4>
                <div id="progressSteps">
                    <!-- 进度步骤将在这里动态显示 -->
                </div>
                <div class="progress mt-3" style="height: 12px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <small class="text-muted">
                            任务ID: <span id="currentTaskId"></span>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            预计完成: <span id="estimatedCompletion"></span>
                        </small>
                    </div>
                </div>
                <div id="processingDetails" class="mt-3">
                    <!-- 处理详情将在这里显示 -->
                </div>
            </div>

            <!-- 结果显示 -->
            <div id="resultsContainer" class="results-container">
                <h4 class="mb-4">
                    <i class="fas fa-check-circle text-success me-2"></i>生产级研究完成
                </h4>
                <div id="resultsContent">
                    <!-- 结果内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedScenario = null;
        let selectedFormat = null;
        let currentTaskId = null;
        let progressInterval = null;
        let systemHealthy = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemHealth();
            loadScenarios();
            loadFormats();
            loadVoices();
            loadPersonalities();
        });

        // 检查系统健康状态
        async function checkSystemHealth() {
            try {
                const response = await fetch('/api/system/health');
                const data = await response.json();

                if (data.success) {
                    const health = data.health;
                    const indicator = document.getElementById('statusIndicator');
                    const statusText = document.getElementById('statusText');

                    if (health.system_status === 'healthy') {
                        indicator.className = 'status-indicator status-healthy';
                        statusText.textContent = '系统运行正常 - 所有组件健康';
                        systemHealthy = true;
                    } else if (health.system_status === 'degraded') {
                        indicator.className = 'status-indicator status-degraded';
                        statusText.textContent = '系统部分功能受限 - 某些组件异常';
                        systemHealthy = false;
                    } else {
                        indicator.className = 'status-indicator status-unhealthy';
                        statusText.textContent = '系统异常 - 多个组件故障';
                        systemHealthy = false;
                    }

                    // 显示详细状态信息
                    console.log('系统健康状态:', health);
                } else {
                    document.getElementById('statusIndicator').className = 'status-indicator status-unhealthy';
                    document.getElementById('statusText').textContent = '无法获取系统状态';
                    systemHealthy = false;
                }
            } catch (error) {
                console.error('健康检查失败:', error);
                document.getElementById('statusIndicator').className = 'status-indicator status-unhealthy';
                document.getElementById('statusText').textContent = '系统连接失败';
                systemHealthy = false;
            }
        }

        // 加载研究场景
        async function loadScenarios() {
            try {
                const response = await fetch('/api/research/scenarios');
                const data = await response.json();

                if (data.success) {
                    renderScenarios(data.scenarios);
                }
            } catch (error) {
                console.error('加载场景失败:', error);
            }
        }

        // 渲染场景卡片
        function renderScenarios(scenarios) {
            const container = document.getElementById('scenarioCards');
            container.innerHTML = '';

            scenarios.forEach(scenario => {
                const card = document.createElement('div');
                card.className = 'research-card';
                card.onclick = () => selectScenario(scenario.id, card);

                const sourcesHtml = scenario.required_sources.map(source =>
                    `<span class="ai-model-tag">${source}</span>`
                ).join('');

                card.innerHTML = `
                    <div class="text-center">
                        <div class="scenario-icon">${scenario.icon}</div>
                        <h6>${scenario.name}</h6>
                        <small class="text-muted">${scenario.description}</small>
                        <div class="mt-2">
                            <small class="text-primary">研究深度: ${scenario.estimated_depth}</small>
                        </div>
                        <div class="mt-2">${sourcesHtml}</div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 选择场景
        function selectScenario(scenarioId, cardElement) {
            document.querySelectorAll('#scenarioCards .research-card').forEach(card => {
                card.classList.remove('selected');
            });

            cardElement.classList.add('selected');
            selectedScenario = scenarioId;
        }

        // 加载输出格式
        async function loadFormats() {
            try {
                const response = await fetch('/api/research/output-formats');
                const data = await response.json();

                if (data.success) {
                    renderFormats(data.formats);
                }
            } catch (error) {
                console.error('加载格式失败:', error);
            }
        }

        // 渲染格式卡片
        function renderFormats(formats) {
            const container = document.getElementById('formatCards');
            container.innerHTML = '';

            formats.forEach(format => {
                const card = document.createElement('div');
                card.className = 'format-card';
                card.onclick = () => selectFormat(format.id, format.estimated_time, format.ai_models_used, card);

                const modelsHtml = format.ai_models_used.map(model =>
                    `<span class="ai-model-tag">${model}</span>`
                ).join('');

                const outputsHtml = format.output_files.map(file =>
                    `<small class="text-muted">${file}</small>`
                ).join(' • ');

                card.innerHTML = `
                    <div class="d-flex align-items-start">
                        <div class="format-icon">${format.icon}</div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${format.name}</h6>
                            <small class="text-muted">${format.description}</small>
                            <div class="mt-2">${modelsHtml}</div>
                            <div class="mt-1">${outputsHtml}</div>
                        </div>
                        <div class="text-end">
                            <small class="text-primary">${format.estimated_time}</small>
                        </div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 选择格式
        function selectFormat(formatId, estimatedTime, aiModels, cardElement) {
            document.querySelectorAll('#formatCards .format-card').forEach(card => {
                card.classList.remove('selected');
            });

            cardElement.classList.add('selected');
            selectedFormat = formatId;

            // 更新预估时间
            document.getElementById('estimatedTime').textContent = estimatedTime;

            // 更新AI模型显示
            const modelsContainer = document.getElementById('aiModelsUsed');
            const modelsHtml = aiModels.map(model =>
                `<span class="ai-model-tag">${model}</span>`
            ).join('');
            modelsContainer.innerHTML = modelsHtml;
        }

        // 加载语音选项
        async function loadVoices() {
            try {
                const response = await fetch('/api/research/voices');
                const data = await response.json();

                const select = document.getElementById('voiceConfig');
                select.innerHTML = '<option value="">默认语音配置</option>';

                if (data.success && data.voices) {
                    data.voices.forEach(voice => {
                        const option = document.createElement('option');
                        option.value = voice.id;
                        option.textContent = `${voice.name} (${voice.language})`;
                        select.appendChild(option);
                    });

                    // 显示TTS服务状态
                    if (data.tts_service_status === 'connected') {
                        const statusSpan = document.createElement('small');
                        statusSpan.className = 'text-success';
                        statusSpan.innerHTML = '<i class="fas fa-check-circle me-1"></i>豆包TTS已连接';
                        select.parentNode.appendChild(statusSpan);
                    }
                }
            } catch (error) {
                console.error('加载语音失败:', error);
                document.getElementById('voiceConfig').innerHTML = '<option value="">语音加载失败</option>';
            }
        }

        // 加载评论者风格
        async function loadPersonalities() {
            try {
                const response = await fetch('/api/research/personalities');
                const data = await response.json();

                const select = document.getElementById('personalityStyle');
                select.innerHTML = '<option value="academic">学术风格</option>';

                if (data.success && data.personalities) {
                    data.personalities.forEach(personality => {
                        const option = document.createElement('option');
                        option.value = personality.id;
                        option.textContent = `${personality.name} - ${personality.description}`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载评论者风格失败:', error);
            }
        }

        // 启动生产级研究
        async function startProductionResearch() {
            // 检查系统健康状态
            if (!systemHealthy) {
                if (!confirm('系统状态异常，可能影响研究质量。是否继续？')) {
                    return;
                }
            }

            // 验证输入
            const userInput = document.getElementById('userInput').value.trim();
            if (!userInput) {
                alert('请输入研究主题');
                return;
            }

            if (!selectedScenario) {
                alert('请选择研究场景');
                return;
            }

            if (!selectedFormat) {
                alert('请选择输出格式');
                return;
            }

            const targetAudience = document.getElementById('targetAudience').value;
            const voiceConfig = document.getElementById('voiceConfig').value;
            const personalityStyle = document.getElementById('personalityStyle').value;
            const additionalRequirements = document.getElementById('additionalRequirements').value.trim();

            try {
                const response = await fetch('/api/research/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_input: userInput,
                        research_scenario: selectedScenario,
                        output_format: selectedFormat,
                        target_audience: targetAudience,
                        voice_config: { voice_id: voiceConfig },
                        personality_style: personalityStyle,
                        additional_requirements: additionalRequirements
                    })
                });

                const data = await response.json();

                if (data.success) {
                    currentTaskId = data.task_id;

                    // 隐藏配置表单，显示进度
                    document.getElementById('configForm').style.display = 'none';
                    document.getElementById('progressContainer').style.display = 'block';
                    document.getElementById('currentTaskId').textContent = currentTaskId;

                    // 显示预估完成时间和AI模型
                    document.getElementById('estimatedCompletion').textContent = data.estimated_time;

                    // 开始监控进度
                    startProgressMonitoring();
                } else {
                    alert('启动研究失败: ' + data.error);
                }
            } catch (error) {
                alert('启动研究失败: ' + error.message);
            }
        }

        // 开始进度监控
        function startProgressMonitoring() {
            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/research/status/${currentTaskId}`);
                    const data = await response.json();

                    if (data.success) {
                        updateProgress(data);

                        if (data.status === 'completed') {
                            clearInterval(progressInterval);
                            showResults();
                        } else if (data.status === 'failed') {
                            clearInterval(progressInterval);
                            showError(data.error);
                        }
                    }
                } catch (error) {
                    console.error('获取进度失败:', error);
                }
            }, 3000); // 每3秒检查一次
        }

        // 更新进度显示
        function updateProgress(data) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = data.progress + '%';
            progressBar.textContent = data.progress + '%';

            // 更新步骤显示
            const stepsContainer = document.getElementById('progressSteps');
            const stepHtml = `
                <div class="progress-step active">
                    <div class="step-icon active">
                        <i class="fas fa-cog fa-spin"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${data.current_step}</h6>
                        <small class="text-muted">进度: ${data.progress}% | 状态: ${data.status}</small>
                        ${data.ai_models_used ? `<div class="mt-1">${data.ai_models_used.map(model => `<span class="ai-model-tag">${model}</span>`).join('')}</div>` : ''}
                    </div>
                </div>
            `;
            stepsContainer.innerHTML = stepHtml;

            // 显示处理详情
            if (data.processing_details && data.processing_details.length > 0) {
                const detailsContainer = document.getElementById('processingDetails');
                const detailsHtml = data.processing_details.map(detail =>
                    `<div class="processing-details">${detail}</div>`
                ).join('');
                detailsContainer.innerHTML = detailsHtml;
            }
        }

        // 显示结果
        async function showResults() {
            try {
                const response = await fetch(`/api/research/result/${currentTaskId}`);
                const data = await response.json();

                if (data.success) {
                    document.getElementById('progressContainer').style.display = 'none';
                    document.getElementById('resultsContainer').style.display = 'block';

                    renderProductionResults(data.result);
                }
            } catch (error) {
                console.error('获取结果失败:', error);
            }
        }

        // 渲染生产级结果
        function renderProductionResults(result) {
            const container = document.getElementById('resultsContent');

            let qualityClass = 'score-fair';
            if (result.quality_score >= 90) qualityClass = 'score-excellent';
            else if (result.quality_score >= 75) qualityClass = 'score-good';
            else if (result.quality_score < 60) qualityClass = 'score-poor';

            const costBreakdownHtml = result.cost_breakdown ?
                Object.entries(result.cost_breakdown).map(([service, cost]) =>
                    `<div>${service}: $${cost}</div>`
                ).join('') : '';

            container.innerHTML = `
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>研究质量评分</h5>
                        <span class="quality-score ${qualityClass}">${result.quality_score}分</span>
                        <p class="text-muted mt-2">${result.summary}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>处理统计</h5>
                        <div class="cost-breakdown">
                            <div><strong>处理时间:</strong> ${result.processing_time}秒</div>
                            <div><strong>Token使用:</strong> ${result.tokens_used || 'N/A'}</div>
                            <div><strong>研究深度:</strong> ${result.research_depth || 'N/A'}</div>
                            <div><strong>分析来源:</strong> ${result.sources_analyzed || 'N/A'}</div>
                            ${costBreakdownHtml ? `<div class="mt-2"><strong>成本明细:</strong>${costBreakdownHtml}</div>` : ''}
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5>生成的文件</h5>
                    ${Object.entries(result.output_files || {}).map(([type, path]) => `
                        <div class="result-file">
                            <div>
                                <i class="fas fa-file me-2"></i>
                                <strong>${getFileTypeName(type)}</strong>
                                <small class="text-muted ms-2">${path}</small>
                            </div>
                            <a href="/api/research/download/${currentTaskId}/${type}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i>下载
                            </a>
                        </div>
                    `).join('')}
                </div>

                <div class="text-center">
                    <button class="btn btn-primary" onclick="startNewResearch()">
                        <i class="fas fa-plus me-2"></i>开始新的研究
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="checkSystemHealth()">
                        <i class="fas fa-sync-alt me-2"></i>刷新系统状态
                    </button>
                </div>
            `;
        }

        // 获取文件类型名称
        function getFileTypeName(type) {
            const names = {
                'research_report': '研究报告',
                'podcast_script': '播客脚本',
                'audio_podcast': '音频播客',
                'complete_video': '完整视频',
                'full_video': '完整视频'
            };
            return names[type] || type;
        }

        // 显示错误
        function showError(error) {
            document.getElementById('progressContainer').style.display = 'none';
            alert('研究失败: ' + error);
            startNewResearch();
        }

        // 开始新研究
        function startNewResearch() {
            // 重置状态
            selectedScenario = null;
            selectedFormat = null;
            currentTaskId = null;

            // 清除选中状态
            document.querySelectorAll('.research-card, .format-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 清空表单
            document.getElementById('userInput').value = '';
            document.getElementById('additionalRequirements').value = '';
            document.getElementById('estimatedTime').textContent = '请选择输出格式';
            document.getElementById('aiModelsUsed').innerHTML = '';

            // 显示配置表单
            document.getElementById('configForm').style.display = 'block';
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';

            // 重新检查系统健康状态
            checkSystemHealth();
        }
    </script>
</body>
</html>
